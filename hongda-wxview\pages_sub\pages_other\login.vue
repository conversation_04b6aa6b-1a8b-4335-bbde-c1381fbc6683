<template>
  <view class="login-page">
    <!-- 背景图片 -->
  <image 
      class="background-image" 
      :src="loginBgUrl" 
      mode="aspectFill"
    ></image>
   <!--uview-plus 导航栏 -->
   <up-navbar
     title="登录"
     :autoBack="true"
     :safeAreaInsetTop="true"
     :fixed="true"
     :placeholder="true"
     bgColor="transparent"
     :zIndex="99"
     leftIconColor="#333333"
     :titleStyle="{
         fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',
         fontWeight: 'normal',
         fontSize: '32rpx',
         color: '#000000',
         lineHeight: '44rpx'
     }"
   >
   </up-navbar>
    <view class="content-wrapper">
      <view class="login-content">
    <!--  Logo 区域 -->
      <view class="logo-section">
        <image class="logo-image" :src="logoHdUrl" mode="aspectFit"></image>
      </view>
        <view class="action-section">
			    <button
			      class="login-btn"
			      :open-type="isAgreementChecked ? 'getPhoneNumber' : ''"
			      @getphonenumber="onGetPhoneNumber"
				  @click="handleLoginClick"
			    >
			      微信手机号快捷登录
			    </button>
				<!-- 自定义的协议勾选区域 -->
				<view class="agreement-section" @click="toggleAgreement">
				  <!-- 这是我们自定义的复选框 -->
				  <view class="custom-checkbox" :class="{ 'is-checked': isAgreementChecked }">
				    <!-- 选中时显示的勾 -->
				    <view class="checkmark" v-if="isAgreementChecked"></view>
				  </view>
				  <!-- 协议文本 -->
				  <view class="agreement-text">
				    <text>我已阅读并同意</text>
				    <text class="link-text" @click.stop="goToUserAgreement">《用户协议》</text>
				    <text>和</text>
				    <text class="link-text" @click.stop="goToPrivacyPolicy">《隐私政策》</text>
				  </view>
				</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getPhoneNumberApi, wxLoginApi} from '@/api/data/user.js'
import {getLatestPolicyApi, acceptPolicyApi} from '@/pages_sub/pages_other/api/data/policy.js'

// 定义页面名称
defineOptions({
  name: 'LoginPage'
})

const isAgreementChecked = ref(false);

// 静态资源 URL（仅暗号读取）
const loginBgUrl = ref('')
const logoHdUrl = ref('')

// 缓存协议版本信息
const policyVersions = ref({
  userAgreement: null,
  privacyPolicy: null
});

// 点击整个协议区域时，切换勾选状态
const toggleAgreement = () => {
  isAgreementChecked.value = !isAgreementChecked.value;
};

// 获取手机号并执行登录的事件处理函数
const onGetPhoneNumber = async (e) => {
  console.log('=== 开始微信手机号快捷登录流程 ===')
  console.log('授权事件详情:', e)
  console.log('e.detail:', e.detail)
  console.log('e.detail.code:', e.detail.code)

  // 2. 检查微信是否返回了手机号授权码
  if (!e.detail.code) {
    console.log('微信未返回授权code');
    console.log('错误信息:', e.detail.errMsg);
    console.log('可能原因：1. 用户拒绝授权 2. 配置问题 3. 网络异常');
    uni.showToast({
      title: '获取手机号失败，请重试',
      icon: 'none'
    });
    return;
  }
  
  const phoneCode = e.detail.code;
  console.log('获取到微信手机号授权码:', phoneCode)
  uni.showLoading({ title: '正在登录...' })

  try {
    // 3. 清除可能存在的旧token，防止缓存问题
    uni.removeStorageSync('token')
    uni.removeStorageSync('userInfo')
    
    // 3. 执行基础登录 uni.login 获取 loginCode
    console.log('开始执行uni.login...')
    const loginRes = await uni.login()
    console.log('uni.login结果:', loginRes)
    const loginCode = loginRes.code
    console.log('获得loginCode:', loginCode)

    // 4. 调用后端的 /wxlogin 接口
    const res = await wxLoginApi(loginCode)
    console.log('后端登录成功:', res)

    // 登录成功，保存 token 和基础用户信息
    const token = res.token
    uni.setStorageSync('token', token)
    uni.setStorageSync('userInfo', res.userInfo)

    // 5. 调用后端的 /getPhoneNumber 接口
    console.log('使用phoneCode:', phoneCode)
    console.log('使用token:', token)
    const phoneRes = await getPhoneNumberApi(phoneCode)
    console.log('获取手机号成功:', phoneRes)

    // 获取手机号成功，更新本地用户信息
    console.log('开始更新本地用户信息...')
    try {
      const userInfo = uni.getStorageSync('userInfo')
      console.log('当前userInfo:', userInfo)
      
      if (!userInfo) {
        console.log('userInfo为空，创建新的用户信息对象')
        const newUserInfo = {
          phoneNumber: phoneRes.phoneNumber
        }
        uni.setStorageSync('userInfo', newUserInfo)
      } else {
        console.log('更新现有userInfo的手机号')
        userInfo.phoneNumber = phoneRes.phoneNumber
        uni.setStorageSync('userInfo', userInfo)
      }
      
      console.log('用户信息更新完成')
    } catch (updateError) {
      console.error('更新用户信息时出错:', updateError)
      throw updateError
    }
    
    console.log('登录流程完全成功，最终保存的数据:')
    console.log('- token:', uni.getStorageSync('token'))
    console.log('- userInfo:', uni.getStorageSync('userInfo'))
    
    console.log('准备隐藏加载提示...')
    uni.hideLoading()
    
    console.log('准备显示成功提示...')
    uni.showToast({ title: '登录成功', icon: 'success' })

    // 登录成功后上报协议同意记录
    console.log('开始上报协议同意记录...')
    try {
      await reportPolicyAcceptance()
    } catch (error) {
      console.error('协议同意记录上报异常:', error)
    }

    // 延迟后返回上一页，确保数据保存完成
    console.log('设置延迟返回定时器...')
    setTimeout(() => {
      console.log('定时器触发，准备返回上一页...')
      
      // 检查是否有指定的返回页面
      try {
        const loginBackPage = uni.getStorageSync('loginBackPage');
        if (loginBackPage) {
          console.log('检测到指定的返回页面:', loginBackPage);
          // 不清除标记，让目标页面自己处理
          uni.navigateBack({
            success: () => {
              console.log('成功返回上一页')
            },
            fail: (err) => {
              console.error('返回上一页失败，尝试直接跳转到指定页面:', err);
              // 如果无法返回，直接跳转到指定页面
              if (loginBackPage.startsWith('/pages/')) {
                uni.redirectTo({
                  url: loginBackPage,
                  fail: () => {
                    // 如果重定向也失败，跳转到首页
                    uni.switchTab({ url: '/pages/index/index' });
                  }
                });
              }
            }
          });
        } else {
          // 正常返回上一页
          uni.navigateBack({
            success: () => {
              console.log('成功返回上一页')
            },
            fail: (err) => {
              console.error('返回上一页失败，跳转到首页:', err);
              // 如果无法返回，跳转到首页
              uni.switchTab({ url: '/pages/index/index' });
            }
          });
        }
      } catch (e) {
        console.warn('检查返回页面标记失败:', e);
        // 出错时正常返回
        uni.navigateBack({
          fail: (err) => {
            console.error('返回上一页失败，跳转到首页:', err);
            uni.switchTab({ url: '/pages/index/index' });
          }
        });
      }
    }, 2000) // 增加延迟时间，确保用户看到成功提示
    
    console.log('登录方法执行完成，等待定时器触发返回...')

  } catch (error) {
    console.error('登录过程中发生错误:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    
    uni.hideLoading()
    
    const errorMessage = error.message || '网络请求失败'
    console.error('显示错误提示:', errorMessage)
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
  }
}


const handleLoginClick = () => {
  // 这个函数只在协议未勾选时需要给出提示
  // 因为勾选后，按钮的 open-type 生效，@click 事件可能不会触发或被 open-type 覆盖
  if (!isAgreementChecked.value) {
    console.log('用户未同意协议');
    uni.showToast({
      title: '请先同意用户协议和隐私政策',
      icon: 'none'
    });
  }
};

// 跳转到用户协议
const goToUserAgreement = () => {
  console.log('点击用户协议链接')
  uni.navigateTo({
    url: '/pages_sub/pages_other/policy?type=user_agreement',
    success: () => {
      console.log('成功跳转到用户协议页面')
    },
    fail: (err) => {
      console.error('跳转用户协议页面失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

// 跳转到隐私政策
const goToPrivacyPolicy = () => {
  console.log('点击隐私政策链接')
  uni.navigateTo({
    url: '/pages_sub/pages_other/policy?type=privacy_policy',
    success: () => {
      console.log('成功跳转到隐私政策页面')
    },
    fail: (err) => {
      console.error('跳转隐私政策页面失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

const cancelLogin = () => {
  uni.navigateBack() // 返回上一页
}

// 加载协议版本信息
const loadPolicyVersions = async () => {
  try {
    console.log('开始加载协议版本信息...')
    
    // 并行加载两个协议的版本信息
    const [userAgreementRes, privacyPolicyRes] = await Promise.all([
      getLatestPolicyApi('user_agreement'),
      getLatestPolicyApi('privacy_policy')
    ])
    
    if (userAgreementRes && userAgreementRes.code === 200 && userAgreementRes.data) {
      policyVersions.value.userAgreement = userAgreementRes.data.version
      console.log('用户协议版本:', userAgreementRes.data.version)
    }
    
    if (privacyPolicyRes && privacyPolicyRes.code === 200 && privacyPolicyRes.data) {
      policyVersions.value.privacyPolicy = privacyPolicyRes.data.version
      console.log('隐私政策版本:', privacyPolicyRes.data.version)
    }
    
    console.log('协议版本信息加载完成:', policyVersions.value)
  } catch (error) {
    console.error('加载协议版本信息失败:', error)
    // 设置默认版本以防加载失败
    policyVersions.value.userAgreement = '1.0.0'
    policyVersions.value.privacyPolicy = '1.0.0'
  }
}

// 上报协议同意记录
const reportPolicyAcceptance = async () => {
  try {
    console.log('=== 开始上报协议同意记录 ===')
    console.log('当前协议版本信息:', policyVersions.value)
    
    // 如果协议版本信息为空，先尝试重新加载
    if (!policyVersions.value.userAgreement || !policyVersions.value.privacyPolicy) {
      console.log('协议版本信息不完整，尝试重新加载...')
      await loadPolicyVersions()
      console.log('重新加载后的协议版本信息:', policyVersions.value)
    }
    
    const reports = []
    
    // 上报用户协议同意
    if (policyVersions.value.userAgreement) {
      console.log('准备上报用户协议，版本:', policyVersions.value.userAgreement)
      reports.push(
        acceptPolicyApi('user_agreement', policyVersions.value.userAgreement)
      )
    } else {
      console.warn('用户协议版本为空，使用默认版本1.0.0')
      reports.push(
        acceptPolicyApi('user_agreement', '1.0.0')
      )
    }
    
    // 上报隐私政策同意
    if (policyVersions.value.privacyPolicy) {
      console.log('准备上报隐私政策，版本:', policyVersions.value.privacyPolicy)
      reports.push(
        acceptPolicyApi('privacy_policy', policyVersions.value.privacyPolicy)
      )
    } else {
      console.warn('隐私政策版本为空，使用默认版本1.0.0')
      reports.push(
        acceptPolicyApi('privacy_policy', '1.0.0')
      )
    }
    
    if (reports.length === 0) {
      console.error('无法创建上报请求')
      return
    }
    
    console.log(`准备并行执行${reports.length}个上报请求...`)
    
    // 并行执行上报
    const results = await Promise.all(reports)
    console.log('协议同意记录上报原始结果:', results)
    
    // 检查上报结果
    let successCount = 0
    results.forEach((result, index) => {
      const type = index === 0 ? '用户协议' : '隐私政策'
      console.log(`${type}上报结果:`, result)
      
      if (result && result.code === 200) {
        successCount++
        console.log(`${type}同意记录上报成功`)
      } else {
        console.error(`${type}同意记录上报失败:`, result)
      }
    })
    
    if (successCount === results.length) {
      console.log('所有协议同意记录上报成功')
    } else {
      console.warn(`部分协议同意记录上报失败，成功数量: ${successCount}/${results.length}`)
    }
    
  } catch (error) {
    console.error('上报协议同意记录过程中发生异常:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack
    })
  }
}

// 生命周期钩子
onLoad(() => {
  // 页面加载时读取静态资源配置
  try {
    const assets = uni.getStorageSync('staticAssets')
    loginBgUrl.value = assets?.['login-bg'] || ''
    logoHdUrl.value = assets?.['logo-hd'] || ''
  } catch (e) {}

  // 页面加载时预加载协议版本信息
  loadPolicyVersions()
})
</script>

<style lang="scss" scoped>
.login-page {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 100vh;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}



.content-wrapper {
flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;; // 可以改回垂直居中
  align-items: center;
  padding: 0 60rpx;
  position: relative;
  padding-top: 200rpx;
  z-index: 1;
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%; // Ensure content takes full width within wrapper
  max-width: 600rpx; // 限制最大宽度，避免在大屏幕上过宽
}


.action-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 针对未选中时的圆圈样式 */
    :deep(.up-checkbox__icon-wrap) {
      width: 24rpx !important;
      height: 24rpx !important;
      border: 2rpx solid #9B9A9A !important;
      
      /* 关键: 组件默认可能会有一个背景色，我们把它设置为透明来显示出边框 */
      background-color: transparent !important; 
    }
  
    /* 针对选中时的样式，确保边框和背景色都正确 
      当选中时，uview会给 icon-wrap 加上 --checked 后缀的类
    */
    :deep(.up-checkbox__icon-wrap--checked) {
      border-color: #023F98 !important; /* 选中时，边框颜色变为主题色 */
      background-color: #023F98 !important; /* 选中时，背景色也变为主题色 */
    }
}

.login-btn {
width: 652rpx;
  height: 76rpx;
  background: #023F98;
  border-radius: 8rpx;
  color: #ffffff;
  border: none;
  line-height: 76rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  margin: 0; // 清除默认的margin

  &::after { // 移除按钮默认边框
    border: none;
  }
}

/* --- 自定义协议区域 --- */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32rpx; // 与上方按钮的间距
}

.custom-checkbox {
  /* 这是未选中时的圆圈样式 */
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #9B9A9A;
  border-radius: 50%; // 圆形
  margin-right: 16rpx; // 与文字的间距
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s; // 添加一个过渡效果
  position: relative; // 添加相对定位

  /* 这是选中时的样式 */
  &.is-checked {
    border-color: #023F98; // 选中时边框变为主题色
  }
}

.checkmark {
  /* 这是里面的小圆点 - 修复对齐问题 */
  width: 14rpx;
  height: 14rpx;
  background-color: #023F98; // 选中后的填充颜色
  border-radius: 50%;
  position: absolute; // 使用绝对定位确保居中
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); // 完全居中
}

.agreement-text {
  font-size: 24rpx;
  color: #666666;

  .link-text {
    color: #023F98;
    text-decoration: none;
  }
}

.cancel-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 40rpx;
  cursor: pointer;
}
</style>