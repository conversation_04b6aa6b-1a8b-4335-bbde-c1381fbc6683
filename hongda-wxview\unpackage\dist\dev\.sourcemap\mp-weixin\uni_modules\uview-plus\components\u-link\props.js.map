{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-link/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 文字颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.link.color\r\n        },\r\n        // 字体大小，单位px\r\n        fontSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.link.fontSize\r\n        },\r\n        // 是否显示下划线\r\n        underLine: {\r\n            type: Boolean,\r\n            default: () => defProps.link.underLine\r\n        },\r\n        // 要跳转的链接\r\n        href: {\r\n            type: String,\r\n            default: () => defProps.link.href\r\n        },\r\n        // 小程序中复制到粘贴板的提示语\r\n        mpTips: {\r\n            type: String,\r\n            default: () => defProps.link.mpTips\r\n        },\r\n        // 下划线颜色\r\n        lineColor: {\r\n            type: String,\r\n            default: () => defProps.link.lineColor\r\n        },\r\n        // 超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色\r\n        text: {\r\n            type: String,\r\n            default: () => defProps.link.text\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}