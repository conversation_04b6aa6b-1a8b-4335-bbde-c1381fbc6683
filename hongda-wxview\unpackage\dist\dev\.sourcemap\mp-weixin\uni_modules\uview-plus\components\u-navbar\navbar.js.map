{"version": 3, "file": "navbar.js", "sources": ["uni_modules/uview-plus/components/u-navbar/navbar.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:16:18\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/navbar.js\r\n */\r\nimport color from '../../libs/config/color'\r\nexport default {\r\n    // navbar 组件\r\n    navbar: {\r\n        safeAreaInsetTop: true,\r\n        placeholder: false,\r\n        fixed: true,\r\n        border: false,\r\n        leftIcon: 'arrow-left',\r\n        leftText: '',\r\n        rightText: '',\r\n        rightIcon: '',\r\n        title: '',\r\n        titleColor: '',\r\n        bgColor: '#ffffff',\r\n        titleWidth: '400rpx',\r\n        height: '44px',\r\n\t\tleftIconSize: 20,\r\n\t\tleftIconColor: color.mainColor,\r\n\t\tautoBack: false,\r\n\t\ttitleStyle: ''\r\n    }\r\n\r\n}\r\n"], "names": ["color"], "mappings": ";;AAUA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACd,cAAc;AAAA,IACd,eAAeA,wCAAK,MAAC;AAAA,IACrB,UAAU;AAAA,IACV,YAAY;AAAA,EACT;AAEL;;"}