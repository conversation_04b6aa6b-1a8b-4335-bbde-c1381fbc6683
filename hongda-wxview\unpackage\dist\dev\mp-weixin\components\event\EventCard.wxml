<view class="event-card data-v-fa0ac472" bindtap="{{k}}"><view class="card-left data-v-fa0ac472"><image src="{{a}}" mode="aspectFill" class="event-image data-v-fa0ac472" lazy-load="{{true}}"></image><view wx:if="{{b}}" class="{{['data-v-fa0ac472', 'status-tag', d]}}">{{c}}</view></view><view class="card-right data-v-fa0ac472"><text class="event-title data-v-fa0ac472">{{e}}</text><view class="event-info-row data-v-fa0ac472"><view class="time-location-item data-v-fa0ac472"><image class="event-info-icon data-v-fa0ac472" src="{{f}}" mode="aspectFit"></image><text class="info-text data-v-fa0ac472">{{g}}</text></view><view class="time-location-item data-v-fa0ac472"><image class="event-info-icon data-v-fa0ac472" src="{{h}}" mode="aspectFit"></image><text class="info-text data-v-fa0ac472">{{i}}</text></view></view><view class="event-info remaining-spots data-v-fa0ac472"><text class="spots-count data-v-fa0ac472"> 剩余名额: {{j}}</text></view></view></view>