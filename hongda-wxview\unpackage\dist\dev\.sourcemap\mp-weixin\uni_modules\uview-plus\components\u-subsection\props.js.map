{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-subsection/props.js"], "sourcesContent": ["import {defineMixin} from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\n\r\nexport const props = defineMixin({\r\n    props: {\r\n        // tab的数据\r\n        list: {\r\n            type: Array,\r\n            default: () => defProps.subsection.list\r\n        },\r\n        // 当前活动的tab的index\r\n        current: {\r\n            type: [String, Number],\r\n            default: () => defProps.subsection.current\r\n        },\r\n        // 激活的颜色\r\n        activeColor: {\r\n            type: String,\r\n            default: () => defProps.subsection.activeColor\r\n        },\r\n        // 未激活的颜色\r\n        inactiveColor: {\r\n            type: String,\r\n            default: () => defProps.subsection.inactiveColor\r\n        },\r\n        // 模式选择，mode=button为按钮形式，mode=subsection时为分段模式\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.subsection.mode\r\n        },\r\n        // 字体大小\r\n        fontSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.subsection.fontSize\r\n        },\r\n        // 激活tab的字体是否加粗\r\n        bold: {\r\n            type: Boolean,\r\n            default: () => defProps.subsection.bold\r\n        },\r\n        // mode = button时，组件背景颜色\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.subsection.bgColor\r\n        },\r\n        // 从list元素对象中读取的键名\r\n        keyName: {\r\n            type: String,\r\n            default: () => defProps.subsection.keyName\r\n        },\r\n        // 从`list`元素对象中读取激活时的颜色  如果存在字段 优先级大于 activeColor\r\n        activeColorKeyName: {\r\n            type: String,\r\n            default: () => defProps.subsection.activeColorKeyName\r\n        },\r\n        // 从`list`元素对象中读取未激活时的颜色 如果存在字段 优先级大于 inactiveColor\r\n        inactiveColorKeyName: {\r\n            type: String,\r\n            default: () => defProps.subsection.inactiveColorKeyName\r\n        },\r\n        // 是否禁用\r\n        disabled: {\r\n            type: String,\r\n            default: () => defProps.subsection.disabled\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,oBAAoB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA,EACJ;AACL,CAAC;;"}