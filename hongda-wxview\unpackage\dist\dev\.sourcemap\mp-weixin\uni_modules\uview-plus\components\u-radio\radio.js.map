{"version": 3, "file": "radio.js", "sources": ["uni_modules/uview-plus/components/u-radio/radio.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:02:34\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radio.js\r\n */\r\nexport default {\r\n    // radio组件\r\n    radio: {\r\n        name: '',\r\n        shape: '',\r\n        disabled: '',\r\n        labelDisabled: '',\r\n        activeColor: '',\r\n        inactiveColor: '',\r\n        iconSize: '',\r\n        labelSize: '',\r\n        label: '',\r\n        labelColor: '',\r\n        size: '',\r\n        iconColor: '',\r\n        placement: ''\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,EACd;AACL;;"}