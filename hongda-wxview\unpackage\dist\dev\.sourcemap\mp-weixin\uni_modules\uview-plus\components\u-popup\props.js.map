{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-popup/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否展示弹窗\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.show\r\n        },\r\n        // 是否显示遮罩\r\n        overlay: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.overlay\r\n        },\r\n        // 弹出的方向，可选值为 top bottom right left center\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.popup.mode\r\n        },\r\n        // 动画时长，单位ms\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.popup.duration\r\n        },\r\n        // 是否显示关闭图标\r\n        closeable: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.closeable\r\n        },\r\n        // 自定义遮罩的样式\r\n        overlayStyle: {\r\n            type: [Object, String],\r\n            default: () => defProps.popup.overlayStyle\r\n        },\r\n        // 点击遮罩是否关闭弹窗\r\n        closeOnClickOverlay: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.closeOnClickOverlay\r\n        },\r\n        // 层级\r\n        zIndex: {\r\n            type: [String, Number],\r\n            default: () => defProps.popup.zIndex\r\n        },\r\n        // 是否为iPhoneX留出底部安全距离\r\n        safeAreaInsetBottom: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.safeAreaInsetBottom\r\n        },\r\n        // 是否留出顶部安全距离（状态栏高度）\r\n        safeAreaInsetTop: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.safeAreaInsetTop\r\n        },\r\n        // 自定义关闭图标位置，top-left为左上角，top-right为右上角，bottom-left为左下角，bottom-right为右下角\r\n        closeIconPos: {\r\n            type: String,\r\n            default: () => defProps.popup.closeIconPos\r\n        },\r\n        // 是否显示圆角\r\n        round: {\r\n            type: [Boolean, String, Number],\r\n            default: () => defProps.popup.round\r\n        },\r\n        // mode=center，也即中部弹出时，是否使用缩放模式\r\n        zoom: {\r\n            type: Boolean,\r\n            default: () => defProps.popup.zoom\r\n        },\r\n        // 弹窗背景色，设置为transparent可去除白色背景\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.popup.bgColor\r\n        },\r\n        // 遮罩的透明度，0-1之间\r\n        overlayOpacity: {\r\n            type: [Number, String],\r\n            default: () => defProps.popup.overlayOpacity\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,MAC9B,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}