{"version": 3, "file": "u-cell.js", "sources": ["uni_modules/uview-plus/components/u-cell/u-cell.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWNlbGwvdS1jZWxsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"u-cell\" :class=\"[customClass]\" :style=\"[addStyle(customStyle)]\"\r\n\t\t:hover-class=\"(!disabled && (clickable || isLink)) ? 'u-cell--clickable' : ''\" :hover-stay-time=\"250\"\r\n\t\t@tap=\"clickHandler\">\r\n\t\t<view class=\"u-cell__body\" :class=\"[ center && 'u-cell--center', size === 'large' && 'u-cell__body--large']\">\r\n\t\t\t<view class=\"u-cell__body__content\">\r\n\t\t\t\t<view class=\"u-cell__left-icon-wrap\" v-if=\"$slots.icon || icon\">\r\n\t\t\t\t\t<slot name=\"icon\" v-if=\"$slots.icon\">\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t\t<u-icon v-else :name=\"icon\"\r\n\t\t\t\t\t\t:custom-style=\"iconStyle\"\r\n\t\t\t\t\t\t:size=\"size === 'large' ? 22 : 18\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-cell__title\">\r\n                    <!-- 将slot与默认内容用if/else分开主要是因为微信小程序不支持slot嵌套传递，这样才能解决collapse组件的slot不失效问题，label暂时未用到。 -->\r\n\t\t\t\t\t<slot name=\"title\" v-if=\"$slots.title || !title\">\r\n\t\t\t\t\t</slot>\r\n                    <text v-else class=\"u-cell__title-text\" :style=\"[titleTextStyle]\"\r\n                        :class=\"[required && 'u-cell--required', disabled && 'u-cell--disabled', size === 'large' && 'u-cell__title-text--large']\">{{ title }}</text>\r\n\t\t\t\t\t<slot name=\"label\">\r\n\t\t\t\t\t\t<text class=\"u-cell__label\" v-if=\"label\"\r\n\t\t\t\t\t\t\t:class=\"[disabled && 'u-cell--disabled', size === 'large' && 'u-cell__label--large']\">{{ label }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<slot name=\"value\">\r\n\t\t\t\t<text class=\"u-cell__value\"\r\n\t\t\t\t\t:class=\"[disabled && 'u-cell--disabled', size === 'large' && 'u-cell__value--large']\"\r\n\t\t\t\t\tv-if=\"!testEmpty(value)\">{{ value }}</text>\r\n\t\t\t</slot>\r\n\t\t\t<view class=\"u-cell__right-icon-wrap\" v-if=\"$slots['right-icon'] || isLink\"\r\n\t\t\t\t:class=\"[`u-cell__right-icon-wrap--${arrowDirection}`]\">\r\n\t\t\t\t<u-icon v-if=\"rightIcon && !$slots['right-icon']\" :name=\"rightIcon\"\r\n\t\t\t\t\t:custom-style=\"rightIconStyle\" :color=\"disabled ? '#c8c9cc' : 'info'\"\r\n\t\t\t\t\t:size=\"size === 'large' ? 18 : 16\"></u-icon>\r\n\t\t\t\t<slot v-else name=\"right-icon\">\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"u-cell__right-icon-wrap\" v-if=\"$slots['righticon']\"\r\n\t\t\t\t:class=\"[`u-cell__right-icon-wrap--${arrowDirection}`]\">\r\n\t\t\t\t<slot name=\"righticon\">\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<u-line v-if=\"border\"></u-line>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle } from '../../libs/function/index';\r\n\timport test from '../../libs/function/test';\r\n\t/**\r\n\t * cell  单元格\r\n\t * @description cell单元格一般用于一组列表的情况，比如个人中心页，设置页等。\r\n\t * @tutorial https://uview-plus.jiangruyi.com/components/cell.html\r\n\t * @property {String | Number}\ttitle\t\t\t标题\r\n\t * @property {String | Number}\tlabel\t\t\t标题下方的描述信息\r\n\t * @property {String | Number}\tvalue\t\t\t右侧的内容\r\n\t * @property {String}\t\t\ticon\t\t\t左侧图标名称，或者图片链接(本地文件建议使用绝对地址)\r\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用cell\t\r\n\t * @property {Boolean}\t\t\tborder\t\t\t是否显示下边框 (默认 true )\r\n\t * @property {Boolean}\t\t\tcenter\t\t\t内容是否垂直居中(主要是针对右侧的value部分) (默认 false )\r\n\t * @property {String}\t\t\turl\t\t\t\t点击后跳转的URL地址\r\n\t * @property {String}\t\t\tlinkType\t\t链接跳转的方式，内部使用的是uView封装的route方法，可能会进行拦截操作 (默认 'navigateTo' )\r\n\t * @property {Boolean}\t\t\tclickable\t\t是否开启点击反馈(表现为点击时加上灰色背景) （默认 false ） \r\n\t * @property {Boolean}\t\t\tisLink\t\t\t是否展示右侧箭头并开启点击反馈 （默认 false ）\r\n\t * @property {Boolean}\t\t\trequired\t\t是否显示表单状态下的必填星号(此组件可能会内嵌入input组件) （默认 false ）\r\n\t * @property {String}\t\t\trightIcon\t\t右侧的图标箭头 （默认 'arrow-right'）\r\n\t * @property {String}\t\t\tarrowDirection\t右侧箭头的方向，可选值为：left，up，down\r\n\t * @property {Object | String}\t\t\trightIconStyle\t右侧箭头图标的样式\r\n\t * @property {Object | String}\t\t\ttitleStyle\t\t标题的样式\r\n\t * @property {Object | String}\t\t\ticonStyle\t\t左侧图标样式\r\n\t * @property {String}\t\t\tsize\t\t\t单位元的大小，可选值为 large，normal，mini \r\n\t * @property {Boolean}\t\t\tstop\t\t\t点击cell是否阻止事件传播 (默认 true )\r\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t * \r\n\t * @pages_event {Function}\t\t\tclick\t\t\t点击cell列表时触发\r\n\t * @example 该组件需要搭配cell-group组件使用，见官方文档示例\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-cell',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tcomputed: {\r\n\t\t\ttitleTextStyle() {\r\n\t\t\t\treturn addStyle(this.titleStyle)\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: ['click'],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\ttestEmpty: test.empty,\r\n\t\t\t// 点击cell\r\n\t\t\tclickHandler(e) {\r\n\t\t\t\tif (this.disabled) return\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\tname: this.name\r\n\t\t\t\t})\r\n\t\t\t\t// 如果配置了url(此props参数通过mixin引入)参数，跳转页面\r\n\t\t\t\tthis.openPage()\r\n\t\t\t\t// 是否阻止事件传播\r\n\t\t\t\tthis.stop && this.preventEvent(e)\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t$u-cell-padding: 13px 15px !default;\r\n\t$u-cell-font-size: 15px !default;\r\n\t$u-cell-line-height: 24px !default;\r\n\t$u-cell-color: $u-main-color !default;\r\n\t$u-cell-icon-size: 16px !default;\r\n\t$u-cell-title-font-size: 15px !default;\r\n\t$u-cell-title-line-height: 22px !default;\r\n\t$u-cell-title-color: $u-main-color !default;\r\n\t$u-cell-label-font-size: 12px !default;\r\n\t$u-cell-label-color: $u-tips-color !default;\r\n\t$u-cell-label-line-height: 18px !default;\r\n\t$u-cell-value-font-size: 14px !default;\r\n\t$u-cell-value-color: $u-content-color !default;\r\n\t$u-cell-clickable-color: $u-bg-color !default;\r\n\t$u-cell-disabled-color: #c8c9cc !default;\r\n\t$u-cell-padding-top-large: 13px !default;\r\n\t$u-cell-padding-bottom-large: 13px !default;\r\n\t$u-cell-value-font-size-large: 15px !default;\r\n\t$u-cell-label-font-size-large: 14px !default;\r\n\t$u-cell-title-font-size-large: 16px !default;\r\n\t$u-cell-left-icon-wrap-margin-right: 4px !default;\r\n\t$u-cell-right-icon-wrap-margin-left: 4px !default;\r\n\t$u-cell-title-flex:1 !default;\r\n\t$u-cell-label-margin-top:5px !default;\r\n\r\n\r\n\t.u-cell {\r\n\t\t&__body {\r\n\t\t\t@include flex();\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\t\t\tpadding: $u-cell-padding;\r\n\t\t\tfont-size: $u-cell-font-size;\r\n\t\t\tcolor: $u-cell-color;\r\n\t\t\t// line-height: $u-cell-line-height;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&__content {\r\n\t\t\t\t@include flex(row);\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t&--large {\r\n\t\t\t\tpadding-top: $u-cell-padding-top-large;\r\n\t\t\t\tpadding-bottom: $u-cell-padding-bottom-large;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__left-icon-wrap,\r\n\t\t&__right-icon-wrap {\r\n\t\t\t@include flex();\r\n\t\t\talign-items: center;\r\n\t\t\t// height: $u-cell-line-height;\r\n\t\t\tfont-size: $u-cell-icon-size;\r\n\t\t}\r\n\r\n\t\t&__left-icon-wrap {\r\n\t\t\tmargin-right: $u-cell-left-icon-wrap-margin-right;\r\n\t\t}\r\n\r\n\t\t&__right-icon-wrap {\r\n\t\t\tmargin-left: $u-cell-right-icon-wrap-margin-left;\r\n\t\t\ttransition: transform 0.3s;\r\n\r\n\t\t\t&--up {\r\n\t\t\t\ttransform: rotate(-90deg);\r\n\t\t\t}\r\n\r\n\t\t\t&--down {\r\n\t\t\t\ttransform: rotate(90deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__title {\r\n\t\t\tflex: $u-cell-title-flex;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t&-text {\r\n\t\t\t\tfont-size: $u-cell-title-font-size;\r\n\t\t\t\tline-height: $u-cell-title-line-height;\r\n\t\t\t\tcolor: $u-cell-title-color;\r\n\r\n\t\t\t\t&--large {\r\n\t\t\t\t\tfont-size: $u-cell-title-font-size-large;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&__label {\r\n\t\t\tmargin-top: $u-cell-label-margin-top;\r\n\t\t\tfont-size: $u-cell-label-font-size;\r\n\t\t\tcolor: $u-cell-label-color;\r\n\t\t\tline-height: $u-cell-label-line-height;\r\n\r\n\t\t\t&--large {\r\n\t\t\t\tfont-size: $u-cell-label-font-size-large;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__value {\t\r\n\t\t\ttext-align: right;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tmargin-left: auto;\r\n\t\t\t/* #endif */\t\r\n\t\t\tfont-size: $u-cell-value-font-size;\r\n\t\t\tline-height: $u-cell-line-height;\r\n\t\t\tcolor: $u-cell-value-color;\r\n\t\t\t&--large {\r\n\t\t\t\tfont-size: $u-cell-value-font-size-large;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&--required {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\toverflow: visible;\r\n\t\t\t/* #endif */\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&--required:before {\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tcontent: '*';\r\n\t\t\t/* #endif */\r\n\t\t\tleft: -8px;\r\n\t\t\tmargin-top: 4rpx;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: $u-error;\r\n\t\t}\r\n\r\n\t\t&--clickable {\r\n\t\t\tbackground-color: $u-cell-clickable-color;\r\n\t\t}\r\n\r\n\t\t&--disabled {\r\n\t\t\tcolor: $u-cell-disabled-color;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tcursor: not-allowed;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t&--center {\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-cell/u-cell.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle", "test"], "mappings": ";;;;;;;AAkFC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,iBAAiB;AAChB,aAAOC,0CAAQ,SAAC,KAAK,UAAU;AAAA,IAChC;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,UAAAA,0CAAQ;AAAA,IACR,WAAWC,yCAAI,KAAC;AAAA;AAAA,IAEhB,aAAa,GAAG;AACf,UAAI,KAAK;AAAU;AACnB,WAAK,MAAM,SAAS;AAAA,QACnB,MAAM,KAAK;AAAA,OACX;AAED,WAAK,SAAS;AAEd,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IAChC;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9GD,GAAG,gBAAgB,SAAS;"}