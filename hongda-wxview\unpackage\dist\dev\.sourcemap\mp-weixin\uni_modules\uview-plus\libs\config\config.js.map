{"version": 3, "file": "config.js", "sources": ["uni_modules/uview-plus/libs/config/config.js"], "sourcesContent": ["const version = '3'\r\n\r\n// 开发环境才提示，生产环境不会提示\r\nif (process.env.NODE_ENV === 'development') {\r\n\tconsole.log(`\\n %c uview-plus V${version} %c https://ijry.github.io/uview-plus/ \\n\\n`, 'color: #ffffff; background: #3c9cff; padding:5px 0;', 'color: #3c9cff;background: #ffffff; padding:5px 0;');\r\n}\r\n\r\nexport default {\r\n    v: version,\r\n    version,\r\n    // 主题名称\r\n    type: [\r\n        'primary',\r\n        'success',\r\n        'info',\r\n        'error',\r\n        'warning'\r\n    ],\r\n    // 颜色部分，本来可以通过scss的:export导出供js使用，但是奈何nvue不支持\r\n    color: {\r\n        'u-primary': '#2979ff',\r\n        'u-warning': '#ff9900',\r\n        'u-success': '#19be6b',\r\n        'u-error': '#fa3534',\r\n        'u-info': '#909399',\r\n        'u-main-color': '#303133',\r\n        'u-content-color': '#606266',\r\n        'u-tips-color': '#909399',\r\n        'u-light-color': '#c0c4cc',\r\n        'up-primary': '#2979ff',\r\n        'up-warning': '#ff9900',\r\n        'up-success': '#19be6b',\r\n        'up-error': '#fa3534',\r\n        'up-info': '#909399',\r\n        'up-main-color': '#303133',\r\n        'up-content-color': '#606266',\r\n        'up-tips-color': '#909399',\r\n        'up-light-color': '#c0c4cc'\r\n    },\r\n    // 字体图标地址\r\n    iconUrl: 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf',\r\n     // 自定义图标\r\n    customIcon: {\r\n        family: '',\r\n        url: ''\r\n    },\r\n    customIcons: {}, // 自定义图标与unicode对应关系\r\n\t// 默认单位，可以通过配置为rpx，那么在用于传入组件大小参数为数值时，就默认为rpx\r\n\tunit: 'px',\r\n\t// 拦截器\r\n\tinterceptor: {\r\n\t\tnavbarLeftClick: null\r\n\t}\r\n}\r\n"], "names": [], "mappings": ";;AAAA,MAAM,UAAU;AAG4B;wFAC/B;AAAA,kBAAqB,OAAO;AAAA;AAAA,GAA+C,uDAAuD,oDAAoD;AACnM;AAEA,MAAe,SAAA;AAAA,EACX,GAAG;AAAA,EACH;AAAA;AAAA,EAEA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,OAAO;AAAA,IACH,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACtB;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,YAAY;AAAA,IACR,QAAQ;AAAA,IACR,KAAK;AAAA,EACT;AAAA,EACA,aAAa,CAAC;AAAA;AAAA;AAAA,EAEjB,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,IACZ,iBAAiB;AAAA,EAClB;AACD;;"}