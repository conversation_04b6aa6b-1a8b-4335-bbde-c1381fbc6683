{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-line/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.line.color\r\n        },\r\n        // 长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带px单位的值等\r\n        length: {\r\n            type: [String, Number],\r\n            default: () => defProps.line.length\r\n        },\r\n        // 线条方向，col-竖向，row-横向\r\n        direction: {\r\n            type: String,\r\n            default: () => defProps.line.direction\r\n        },\r\n        // 是否显示细边框\r\n        hairline: {\r\n            type: <PERSON><PERSON><PERSON>,\r\n            default: () => defProps.line.hairline\r\n        },\r\n        // 线条与上下左右元素的间距，字符串形式，如\"30px\"、\"20px 30px\"\r\n        margin: {\r\n            type: [String, Number],\r\n            default: () => defProps.line.margin\r\n        },\r\n        // 是否虚线，true-虚线，false-实线\r\n        dashed: {\r\n            type: Boolean,\r\n            default: () => defProps.line.dashed\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}