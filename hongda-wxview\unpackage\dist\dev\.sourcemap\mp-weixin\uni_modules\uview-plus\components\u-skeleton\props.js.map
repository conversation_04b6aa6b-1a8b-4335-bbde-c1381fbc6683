{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-skeleton/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否展示骨架组件\r\n        loading: {\r\n            type: Boolean,\r\n            default: () => defProps.skeleton.loading\r\n        },\r\n        // 是否开启动画效果\r\n        animate: {\r\n            type: Boolean,\r\n            default: () => defProps.skeleton.animate\r\n        },\r\n        // 段落占位图行数\r\n        rows: {\r\n            type: [String, Number],\r\n            default: () => defProps.skeleton.rows\r\n        },\r\n        // 段落占位图的宽度\r\n        rowsWidth: {\r\n            type: [String, Number, Array],\r\n            default: () => defProps.skeleton.rowsWidth\r\n        },\r\n        // 段落占位图的高度\r\n        rowsHeight: {\r\n            type: [String, Number, Array],\r\n            default: () => defProps.skeleton.rowsHeight\r\n        },\r\n        // 是否展示标题占位图\r\n        title: {\r\n            type: Boolean,\r\n            default: () => defProps.skeleton.title\r\n        },\r\n        // 段落标题的宽度\r\n        titleWidth: {\r\n            type: [String, Number],\r\n            default: () => defProps.skeleton.titleWidth\r\n        },\r\n        // 段落标题的高度\r\n        titleHeight: {\r\n            type: [String, Number],\r\n            default: () => defProps.skeleton.titleHeight\r\n        },\r\n        // 是否展示头像占位图\r\n        avatar: {\r\n            type: Boolean,\r\n            default: () => defProps.skeleton.avatar\r\n        },\r\n        // 头像占位图大小\r\n        avatarSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.skeleton.avatarSize\r\n        },\r\n        // 头像占位图的形状，circle-圆形，square-方形\r\n        avatarShape: {\r\n            type: String,\r\n            default: () => defProps.skeleton.avatarShape\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,QAAQ,KAAK;AAAA,MAC5B,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,QAAQ,KAAK;AAAA,MAC5B,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}