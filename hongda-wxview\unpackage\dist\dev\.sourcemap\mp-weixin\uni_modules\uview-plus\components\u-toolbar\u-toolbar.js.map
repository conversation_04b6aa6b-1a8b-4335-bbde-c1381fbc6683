{"version": 3, "file": "u-toolbar.js", "sources": ["uni_modules/uview-plus/components/u-toolbar/u-toolbar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRvb2xiYXIvdS10b29sYmFyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t\tclass=\"u-toolbar\"\r\n\t\************************=\"noop\"\r\n\t\tv-if=\"show\"\r\n\t>\r\n\t\t<view\r\n\t\t\tclass=\"u-toolbar__left\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-toolbar__cancel__wrapper\"\r\n\t\t\t\thover-class=\"u-hover-class\"\r\n\t\t\t>\r\n\t\t\t\t<text\r\n\t\t\t\t\tclass=\"u-toolbar__wrapper__cancel\"\r\n\t\t\t\t\t@tap=\"cancel\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\tcolor: cancelColor\r\n\t\t\t\t\t}\"\r\n\t\t\t\t>{{ cancelText }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<text\r\n\t\t\tclass=\"u-toolbar__title u-line-1\"\r\n\t\t\tv-if=\"title\"\r\n\t\t>{{ title }}</text>\r\n\t\t<view\r\n\t\t\tclass=\"u-toolbar__right\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tv-if=\"!rightSlot\"\r\n\t\t\t\tclass=\"u-toolbar__confirm__wrapper\"\r\n\t\t\t\thover-class=\"u-hover-class\"\r\n\t\t\t>\r\n\t\t\t\t<text\r\n\t\t\t\t\tclass=\"u-toolbar__wrapper__confirm\"\r\n\t\t\t\t\t@tap=\"confirm\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\tcolor: confirmColor\r\n\t\t\t\t}\"\r\n\t\t\t\t>{{ confirmText }}</text>\r\n\t\t\t</view>\r\n\t\t\t<template v-else>\r\n\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t</slot>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\t/**\r\n\t * Toolbar 工具条\r\n\t * @description \r\n\t * @tutorial https://ijry.github.io/uview-plus/components/toolbar.html\r\n\t * @property {Boolean}\tshow\t\t\t是否展示工具条（默认 true ）\r\n\t * @property {String}\tcancelText\t\t取消按钮的文字（默认 '取消' ）\r\n\t * @property {String}\tconfirmText\t\t确认按钮的文字（默认 '确认' ）\r\n\t * @property {String}\tcancelColor\t\t取消按钮的颜色（默认 '#909193' ）\r\n\t * @property {String}\tconfirmColor\t确认按钮的颜色（默认 '#3c9cff' ）\r\n\t * @property {String}\ttitle\t\t\t标题文字\r\n\t * @pages_event {Function}\r\n\t * @example \r\n\t */\r\n\texport default {\r\n\t\tname: 'u-toolbar',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\temits: [\"confirm\", \"cancel\"],\r\n\t\tcreated() {\r\n\t\t\t// console.log(this.$slots)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击取消按钮\r\n\t\t\tcancel() {\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\t\t\t// 点击确定按钮\r\n\t\t\tconfirm() {\r\n\t\t\t\tthis.$emit('confirm')\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-toolbar {\r\n\t\theight: 42px;\r\n\t\t@include flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\r\n\t\t&__wrapper {\r\n\t\t\t&__cancel {\r\n\t\t\t\tcolor: $u-tips-color;\r\n\t\t\t\tfont-size: 15px;\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__title {\r\n\t\t\tcolor: $u-main-color;\r\n\t\t\tpadding: 0 60rpx;\r\n\t\t\tfont-size: 16px;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t&__wrapper {\r\n\t\t\t&__left,\r\n\t\t\t&__right {\r\n\t\t\t\t@include flex;\r\n\t\t\t}\r\n\t\t\t&__confirm {\r\n\t\t\t\tcolor: $u-primary;\r\n\t\t\t\tfont-size: 15px;\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-toolbar/u-toolbar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props"], "mappings": ";;;;;AAmEC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,qDAAK;AAAA,EAC9B,OAAO,CAAC,WAAW,QAAQ;AAAA,EAC3B,UAAU;AAAA,EAET;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAS;AACR,WAAK,MAAM,QAAQ;AAAA,IACnB;AAAA;AAAA,IAED,UAAU;AACT,WAAK,MAAM,SAAS;AAAA,IACrB;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;ACnFD,GAAG,gBAAgB,SAAS;"}