"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_image = require("../../utils/image.js");
const _sfc_main = {
  __name: "EventDetailContent",
  props: {
    eventDetail: { type: Object, required: true }
  },
  setup(__props) {
    const props = __props;
    const processedDetails = common_vendor.computed(() => {
      var _a;
      if (!((_a = props.eventDetail) == null ? void 0 : _a.details) || typeof props.eventDetail.details !== "string") {
        return "";
      }
      let processedHtml = props.eventDetail.details;
      try {
        processedHtml = processedHtml.replace(
          /<img([^>]*?)src=["']([^"']*?)["']([^>]*?)>/gi,
          (match, beforeSrc, srcValue, afterSrc) => {
            const formattedSrc = utils_image.getFullImageUrl(srcValue);
            const hasStyle = /style\s*=/.test(beforeSrc + afterSrc);
            if (hasStyle) {
              const styleRegex = /style\s*=\s*["']([^"']*?)["']/i;
              const styleMatch = (beforeSrc + afterSrc).match(styleRegex);
              if (styleMatch) {
                const existingStyle = styleMatch[1];
                const newStyle = existingStyle.includes("max-width") ? existingStyle : existingStyle + "; max-width: 100%; height: auto;";
                return `<img${beforeSrc}src="${formattedSrc}"${afterSrc}`.replace(
                  styleRegex,
                  `style="${newStyle}"`
                );
              }
            }
            return `<img${beforeSrc}src="${formattedSrc}" style="max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;"${afterSrc}>`;
          }
        );
        processedHtml = processedHtml.replace(
          /<img([^>]*?)>/gi,
          (match) => {
            if (!/style\s*=/.test(match)) {
              return match.replace(">", ' style="max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;">');
            }
            return match;
          }
        );
        return processedHtml;
      } catch (error) {
        common_vendor.index.__f__("error", "at components/event/EventDetailContent.vue:71", "富文本处理失败:", error);
        return props.eventDetail.details;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: processedDetails.value && processedDetails.value.trim().length > 0
      }, processedDetails.value && processedDetails.value.trim().length > 0 ? {
        b: processedDetails.value
      } : __props.eventDetail.summary && __props.eventDetail.summary.trim().length > 0 ? {
        d: common_vendor.t(__props.eventDetail.summary)
      } : {}, {
        c: __props.eventDetail.summary && __props.eventDetail.summary.trim().length > 0
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6629e4f9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/event/EventDetailContent.js.map
