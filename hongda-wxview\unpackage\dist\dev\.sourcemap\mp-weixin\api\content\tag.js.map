{"version": 3, "file": "tag.js", "sources": ["api/content/tag.js"], "sourcesContent": ["import {\r\n\tget\r\n} from '@/utils/request' // 假设您的request.js导出了具名get方法\r\n\r\n/**\r\n * 获取所有可用的资讯分类标签列表\r\n * @returns {Promise}\r\n */\r\nexport function listAllTag() {\r\n\t// [修正] url只保留从 /content 开始的部分\r\n\treturn get('/content/tags');\r\n}"], "names": ["get"], "mappings": ";;AAQO,SAAS,aAAa;AAE5B,SAAOA,cAAAA,IAAI,eAAe;AAC3B;;"}