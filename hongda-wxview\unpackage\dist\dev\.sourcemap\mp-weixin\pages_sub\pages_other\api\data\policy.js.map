{"version": 3, "file": "policy.js", "sources": ["pages_sub/pages_other/api/data/policy.js"], "sourcesContent": ["import { get, post } from '@/utils/request.js';\r\n\r\n// 获取最新协议内容（公开接口）\r\nexport const getLatestPolicyApi = (type) => {\r\n  return get('/policy/latest', { type });\r\n};\r\n\r\n// 获取指定版本协议内容（公开接口）\r\nexport const getPolicyByVersionApi = (type, version) => {\r\n  return get('/policy/by-version', { type, version });\r\n};\r\n\r\n// 用户同意协议（需要登录）\r\nexport const acceptPolicyApi = (policyType, policyVersion) => {\r\n  return post('/policy/accept', { policyType, policyVersion });\r\n};\r\n\r\n// 获取用户已同意的协议版本（需要登录）\r\nexport const getUserAcceptedPolicyApi = (type) => {\r\n  return get('/policy/user-accepted', { type });\r\n};\r\n\r\n\r\n"], "names": ["get", "post"], "mappings": ";;AAGY,MAAC,qBAAqB,CAAC,SAAS;AAC1C,SAAOA,kBAAI,kBAAkB,EAAE,KAAM,CAAA;AACvC;AAQY,MAAC,kBAAkB,CAAC,YAAY,kBAAkB;AAC5D,SAAOC,cAAI,KAAC,kBAAkB,EAAE,YAAY,cAAe,CAAA;AAC7D;;;"}