{"version": 3, "file": "u-button.js", "sources": ["uni_modules/uview-plus/components/u-button/u-button.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWJ1dHRvbi91LWJ1dHRvbi52dWU"], "sourcesContent": ["<template>\r\n\r\n    <button\r\n        :hover-start-time=\"Number(hoverStartTime)\"\r\n        :hover-stay-time=\"Number(hoverStayTime)\"\r\n        :form-type=\"formType\"\r\n        :open-type=\"openType\"\r\n        :app-parameter=\"appParameter\"\r\n        :hover-stop-propagation=\"hoverStopPropagation\"\r\n        :send-message-title=\"sendMessageTitle\"\r\n        :send-message-path=\"sendMessagePath\"\r\n        :lang=\"lang\"\r\n        :data-name=\"dataName\"\r\n        :session-from=\"sessionFrom\"\r\n        :send-message-img=\"sendMessageImg\"\r\n        :show-message-card=\"showMessageCard\"\r\n        @getphonenumber=\"getphonenumber\"\r\n        @getuserinfo=\"getuserinfo\"\r\n        @error=\"error\"\r\n        @opensetting=\"opensetting\"\r\n        @launchapp=\"launchapp\"\r\n        @agreeprivacyauthorization=\"agreeprivacyauthorization\"\r\n        :hover-class=\"!disabled && !loading ? 'u-button--active' : ''\"\r\n        class=\"u-button u-reset-button\"\r\n        :style=\"[baseColor, addStyle(customStyle)]\"\r\n        @tap=\"clickHandler\"\r\n        :class=\"bemClass\"\r\n    >\r\n        <template v-if=\"loading\">\r\n            <u-loading-icon\r\n                :mode=\"loadingMode\"\r\n                :size=\"loadingSize * 1.15\"\r\n                :color=\"loadingColor\"\r\n            ></u-loading-icon>\r\n            <text\r\n                class=\"u-button__loading-text\"\r\n                :style=\"[{ fontSize: textSize + 'px' }]\"\r\n                >{{ loadingText || text }}</text\r\n            >\r\n        </template>\r\n        <template v-else>\r\n            <u-icon\r\n                v-if=\"icon\"\r\n                :name=\"icon\"\r\n                :color=\"iconColorCom\"\r\n                :size=\"textSize * 1.35\"\r\n                :customStyle=\"{ marginRight: '2px' }\"\r\n            ></u-icon>\r\n            <slot>\r\n                <text\r\n                    class=\"u-button__text\"\r\n                    :style=\"[{ fontSize: textSize + 'px' }]\"\r\n                    >{{ text }}</text\r\n                >\r\n            </slot>\r\n        </template>\r\n    </button>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { buttonMixin } from \"../../libs/mixin/button\";\r\nimport { openType } from \"../../libs/mixin/openType\";\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { props } from \"./props\";\r\nimport { addStyle } from '../../libs/function/index';\r\nimport { throttle } from '../../libs/function/throttle';\r\nimport color from '../../libs/config/color';\r\n/**\r\n * button 按钮\r\n * @description Button 按钮\r\n * @tutorial https://ijry.github.io/uview-plus/components/button.html\r\n *\r\n * @property {Boolean}\t\t\thairline\t\t\t\t是否显示按钮的细边框 (默认 true )\r\n * @property {String}\t\t\ttype\t\t\t\t\t按钮的预置样式，info，primary，error，warning，success (默认 'info' )\r\n * @property {String}\t\t\tsize\t\t\t\t\t按钮尺寸，large，normal，mini （默认 normal）\r\n * @property {String}\t\t\tshape\t\t\t\t\t按钮形状，circle（两边为半圆），square（带圆角） （默认 'square' ）\r\n * @property {Boolean}\t\t\tplain\t\t\t\t\t按钮是否镂空，背景色透明 （默认 false）\r\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用 （默认 false）\r\n * @property {Boolean}\t\t\tloading\t\t\t\t\t按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈) （默认 false）\r\n * @property {String | Number}\tloadingText\t\t\t\t加载中提示文字\r\n * @property {String}\t\t\tloadingMode\t\t\t\t加载状态图标类型 （默认 'spinner' ）\r\n * @property {String | Number}\tloadingSize\t\t\t\t加载图标大小 （默认 15 ）\r\n * @property {String}\t\t\topenType\t\t\t\t开放能力，具体请看uniapp稳定关于button组件部分说明\r\n * @property {String}\t\t\tformType\t\t\t\t用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n * @property {String}\t\t\tappParameter\t\t\t打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效 （注：只微信小程序、QQ小程序有效）\r\n * @property {Boolean}\t\t\thoverStopPropagation\t指定是否阻止本节点的祖先节点出现点击态，微信小程序有效（默认 true ）\r\n * @property {String}\t\t\tlang\t\t\t\t\t指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文（默认 en ）\r\n * @property {String}\t\t\tsessionFrom\t\t\t\t会话来源，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageTitle\t\t会话内消息卡片标题，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessagePath\t\t\t会话内消息卡片点击跳转小程序路径，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageImg\t\t\t会话内消息卡片图片，openType=\"contact\"时有效\r\n * @property {Boolean}\t\t\tshowMessageCard\t\t\t是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，openType=\"contact\"时有效（默认false）\r\n * @property {String}\t\t\tdataName\t\t\t\t额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n * @property {String | Number}\tthrottleTime\t\t\t节流，一定时间内只能触发一次 （默认 0 )\r\n * @property {String | Number}\thoverStartTime\t\t\t按住后多久出现点击态，单位毫秒 （默认 0 )\r\n * @property {String | Number}\thoverStayTime\t\t\t手指松开后点击态保留时间，单位毫秒 （默认 200 )\r\n * @property {String | Number}\ttext\t\t\t\t\t按钮文字，之所以通过props传入，是因为slot传入的话（注：nvue中无法控制文字的样式）\r\n * @property {String}\t\t\ticon\t\t\t\t\t按钮图标\r\n * @property {String}\t\t\ticonColor\t\t\t\t按钮图标颜色\r\n * @property {String}\t\t\tcolor\t\t\t\t\t按钮颜色，支持传入linear-gradient渐变色\r\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\r\n *\r\n * @pages_event {Function}\tclick\t\t\t非禁止并且非加载中，才能点击\r\n * @pages_event {Function}\tgetphonenumber\topen-type=\"getPhoneNumber\"时有效\r\n * @pages_event {Function}\tgetuserinfo\t\t用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\r\n * @pages_event {Function}\terror\t\t\t当使用开放能力时，发生错误的回调\r\n * @pages_event {Function}\topensetting\t\t在打开授权设置页并关闭后回调\r\n * @pages_event {Function}\tlaunchapp\t\t打开 APP 成功的回调\r\n * @pages_event {Function}\tagreeprivacyauthorization\t用户同意隐私协议事件回调\r\n * @example <u-button>月落</u-button>\r\n */\r\nexport default {\r\n    name: \"u-button\",\r\n\r\n    mixins: [mpMixin, mixin, buttonMixin, openType, props],\r\n\r\n\r\n\r\n\r\n    data() {\r\n        return {};\r\n    },\r\n    computed: {\r\n        // 生成bem风格的类名\r\n        bemClass() {\r\n            // this.bem为一个computed变量，在mixin中\r\n            if (!this.color) {\r\n                return this.bem(\r\n                    \"button\",\r\n                    [\"type\", \"shape\", \"size\"],\r\n                    [\"disabled\", \"plain\", \"hairline\"]\r\n                );\r\n            } else {\r\n                // 由于nvue的原因，在有color参数时，不需要传入type，否则会生成type相关的类型，影响最终的样式\r\n                return this.bem(\r\n                    \"button\",\r\n                    [\"shape\", \"size\"],\r\n                    [\"disabled\", \"plain\", \"hairline\"]\r\n                );\r\n            }\r\n        },\r\n        loadingColor() {\r\n            if (this.plain) {\r\n                // 如果有设置color值，则用color值，否则使用type主题颜色\r\n                return this.color\r\n                    ? this.color\r\n                    : color[`u-${this.type}`];\r\n            }\r\n            if (this.type === \"info\") {\r\n                return \"#c9c9c9\";\r\n            }\r\n            return \"rgb(200, 200, 200)\";\r\n        },\r\n        iconColorCom() {\r\n            // 如果是镂空状态，设置了color就用color值，否则使用主题颜色，\r\n            // u-icon的color能接受一个主题颜色的值\r\n\t\t\tif (this.iconColor) return this.iconColor;\r\n\t\t\tif (this.plain) {\r\n                return this.color ? this.color : this.type;\r\n            } else {\r\n                return this.type === \"info\" ? \"#000000\" : \"#ffffff\";\r\n            }\r\n        },\r\n        baseColor() {\r\n            let style = {};\r\n            if (this.color) {\r\n                // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n                style.color = this.plain ? this.color : \"white\";\r\n                if (!this.plain) {\r\n                    // 非镂空，背景色使用自定义的颜色\r\n                    style[\"background-color\"] = this.color;\r\n                }\r\n                if (this.color.indexOf(\"gradient\") !== -1) {\r\n                    // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色\r\n                    // weex文档说明可以写borderWidth的形式，为什么这里需要分开写？\r\n                    // 因为weex是阿里巴巴为了部门业绩考核而做的你懂的东西，所以需要这么写才有效\r\n                    style.borderTopWidth = 0;\r\n                    style.borderRightWidth = 0;\r\n                    style.borderBottomWidth = 0;\r\n                    style.borderLeftWidth = 0;\r\n                    if (!this.plain) {\r\n                        style.backgroundImage = this.color;\r\n                    }\r\n                } else {\r\n                    // 非渐变色，则设置边框相关的属性\r\n                    style.borderColor = this.color;\r\n                    style.borderWidth = \"1px\";\r\n                    style.borderStyle = \"solid\";\r\n                }\r\n            }\r\n            return style;\r\n        },\r\n        // nvue版本按钮的字体不会继承父组件的颜色，需要对每一个text组件进行单独的设置\r\n        nvueTextStyle() {\r\n            let style = {};\r\n            // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n            if (this.type === \"info\") {\r\n                style.color = \"#323233\";\r\n            }\r\n            if (this.color) {\r\n                style.color = this.plain ? this.color : \"white\";\r\n            }\r\n            style.fontSize = this.textSize + \"px\";\r\n            return style;\r\n        },\r\n        // 字体大小\r\n        textSize() {\r\n            let fontSize = 14,\r\n                { size } = this;\r\n            if (size === \"large\") fontSize = 16;\r\n            if (size === \"normal\") fontSize = 14;\r\n            if (size === \"small\") fontSize = 12;\r\n            if (size === \"mini\") fontSize = 10;\r\n            return fontSize;\r\n        },\r\n    },\r\n\temits: ['click', 'getphonenumber', 'getuserinfo',\r\n\t\t'error', 'opensetting', 'launchapp', 'agreeprivacyauthorization'],\r\n    methods: {\r\n        addStyle,\r\n        clickHandler(e: any) {\r\n            // 非禁止并且非加载中，才能点击\r\n            if (!this.disabled && !this.loading) {\r\n\t\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\r\n\t\t\t\tthrottle(() => {\r\n\t\t\t\t\tthis.$emit(\"click\", e);\r\n\t\t\t\t}, this.throttleTime);\r\n            }\r\n            // 是否阻止事件传播\r\n            this.stop && this.preventEvent(e)\r\n        },\r\n        // 下面为对接uniapp官方按钮开放能力事件回调的对接\r\n        getphonenumber(res: any) {\r\n            this.$emit(\"getphonenumber\", res);\r\n        },\r\n        getuserinfo(res: any) {\r\n            this.$emit(\"getuserinfo\", res);\r\n        },\r\n        error(res: any) {\r\n            this.$emit(\"error\", res);\r\n        },\r\n        opensetting(res: any) {\r\n            this.$emit(\"opensetting\", res);\r\n        },\r\n        launchapp(res: any) {\r\n            this.$emit(\"launchapp\", res);\r\n        },\r\n        agreeprivacyauthorization(res) {\r\n            this.$emit(\"agreeprivacyauthorization\", res);\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n@import \"./vue.scss\";\r\n\r\n\r\n\r\n\r\n\r\n\r\n$u-button-u-button-height: 40px !default;\r\n$u-button-text-font-size: 15px !default;\r\n$u-button-loading-text-font-size: 15px !default;\r\n$u-button-loading-text-margin-left: 4px !default;\r\n$u-button-large-width: 100% !default;\r\n$u-button-large-height: 50px !default;\r\n$u-button-normal-padding: 0 12px !default;\r\n$u-button-large-padding: 0 15px !default;\r\n$u-button-normal-font-size: 14px !default;\r\n$u-button-small-min-width: 60px !default;\r\n$u-button-small-height: 30px !default;\r\n$u-button-small-padding: 0px 8px !default;\r\n$u-button-mini-padding: 0px 8px !default;\r\n$u-button-small-font-size: 12px !default;\r\n$u-button-mini-height: 22px !default;\r\n$u-button-mini-font-size: 10px !default;\r\n$u-button-mini-min-width: 50px !default;\r\n$u-button-disabled-opacity: 0.5 !default;\r\n$u-button-info-color: #323233 !default;\r\n$u-button-info-background-color: #fff !default;\r\n$u-button-info-border-color: #ebedf0 !default;\r\n$u-button-info-border-width: 1px !default;\r\n$u-button-info-border-style: solid !default;\r\n$u-button-success-color: #fff !default;\r\n$u-button-success-background-color: $u-success !default;\r\n$u-button-success-border-color: $u-button-success-background-color !default;\r\n$u-button-success-border-width: 1px !default;\r\n$u-button-success-border-style: solid !default;\r\n$u-button-primary-color: #fff !default;\r\n$u-button-primary-background-color: $u-primary !default;\r\n$u-button-primary-border-color: $u-button-primary-background-color !default;\r\n$u-button-primary-border-width: 1px !default;\r\n$u-button-primary-border-style: solid !default;\r\n$u-button-error-color: #fff !default;\r\n$u-button-error-background-color: $u-error !default;\r\n$u-button-error-border-color: $u-button-error-background-color !default;\r\n$u-button-error-border-width: 1px !default;\r\n$u-button-error-border-style: solid !default;\r\n$u-button-warning-color: #fff !default;\r\n$u-button-warning-background-color: $u-warning !default;\r\n$u-button-warning-border-color: $u-button-warning-background-color !default;\r\n$u-button-warning-border-width: 1px !default;\r\n$u-button-warning-border-style: solid !default;\r\n$u-button-block-width: 100% !default;\r\n$u-button-circle-border-top-right-radius: 100px !default;\r\n$u-button-circle-border-top-left-radius: 100px !default;\r\n$u-button-circle-border-bottom-left-radius: 100px !default;\r\n$u-button-circle-border-bottom-right-radius: 100px !default;\r\n$u-button-square-border-top-right-radius: 3px !default;\r\n$u-button-square-border-top-left-radius: 3px !default;\r\n$u-button-square-border-bottom-left-radius: 3px !default;\r\n$u-button-square-border-bottom-right-radius: 3px !default;\r\n$u-button-icon-min-width: 1em !default;\r\n$u-button-plain-background-color: #fff !default;\r\n$u-button-hairline-border-width: 0.5px !default;\r\n\r\n.u-button {\r\n    height: $u-button-u-button-height;\r\n    position: relative;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @include flex;\r\n\r\n    box-sizing: border-box;\r\n\r\n    flex-direction: row;\r\n\r\n    &__text {\r\n        font-size: $u-button-text-font-size;\r\n    }\r\n\r\n    &__loading-text {\r\n        font-size: $u-button-loading-text-font-size;\r\n        margin-left: $u-button-loading-text-margin-left;\r\n    }\r\n\r\n    &--large {\r\n\r\n        width: $u-button-large-width;\r\n\r\n        height: $u-button-large-height;\r\n        padding: $u-button-large-padding;\r\n    }\r\n\r\n    &--normal {\r\n        padding: $u-button-normal-padding;\r\n        font-size: $u-button-normal-font-size;\r\n    }\r\n\r\n    &--small {\r\n\r\n        min-width: $u-button-small-min-width;\r\n\r\n        height: $u-button-small-height;\r\n        padding: $u-button-small-padding;\r\n        font-size: $u-button-small-font-size;\r\n    }\r\n\r\n    &--mini {\r\n        height: $u-button-mini-height;\r\n        font-size: $u-button-mini-font-size;\r\n\r\n        min-width: $u-button-mini-min-width;\r\n\r\n        padding: $u-button-mini-padding;\r\n    }\r\n\r\n    &--disabled {\r\n        opacity: $u-button-disabled-opacity;\r\n    }\r\n\r\n    &--info {\r\n        color: $u-button-info-color;\r\n        background-color: $u-button-info-background-color;\r\n        border-color: $u-button-info-border-color;\r\n        border-width: $u-button-info-border-width;\r\n        border-style: $u-button-info-border-style;\r\n    }\r\n\r\n    &--success {\r\n        color: $u-button-success-color;\r\n        background-color: $u-button-success-background-color;\r\n        border-color: $u-button-success-border-color;\r\n        border-width: $u-button-success-border-width;\r\n        border-style: $u-button-success-border-style;\r\n    }\r\n\r\n    &--primary {\r\n        color: $u-button-primary-color;\r\n        background-color: $u-button-primary-background-color;\r\n        border-color: $u-button-primary-border-color;\r\n        border-width: $u-button-primary-border-width;\r\n        border-style: $u-button-primary-border-style;\r\n    }\r\n\r\n    &--error {\r\n        color: $u-button-error-color;\r\n        background-color: $u-button-error-background-color;\r\n        border-color: $u-button-error-border-color;\r\n        border-width: $u-button-error-border-width;\r\n        border-style: $u-button-error-border-style;\r\n    }\r\n\r\n    &--warning {\r\n        color: $u-button-warning-color;\r\n        background-color: $u-button-warning-background-color;\r\n        border-color: $u-button-warning-border-color;\r\n        border-width: $u-button-warning-border-width;\r\n        border-style: $u-button-warning-border-style;\r\n    }\r\n\r\n    &--block {\r\n        @include flex;\r\n        width: $u-button-block-width;\r\n    }\r\n\r\n    &--circle {\r\n        border-top-right-radius: $u-button-circle-border-top-right-radius;\r\n        border-top-left-radius: $u-button-circle-border-top-left-radius;\r\n        border-bottom-left-radius: $u-button-circle-border-bottom-left-radius;\r\n        border-bottom-right-radius: $u-button-circle-border-bottom-right-radius;\r\n    }\r\n\r\n    &--square {\r\n        border-bottom-left-radius: $u-button-square-border-top-right-radius;\r\n        border-bottom-right-radius: $u-button-square-border-top-left-radius;\r\n        border-top-left-radius: $u-button-square-border-bottom-left-radius;\r\n        border-top-right-radius: $u-button-square-border-bottom-right-radius;\r\n    }\r\n\r\n    &__icon {\r\n\r\n        min-width: $u-button-icon-min-width;\r\n        line-height: inherit !important;\r\n        vertical-align: top;\r\n\r\n    }\r\n\r\n    &--plain {\r\n        background-color: $u-button-plain-background-color;\r\n    }\r\n\r\n    &--hairline {\r\n        border-width: $u-button-hairline-border-width !important;\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-button/u-button.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "buttonMixin", "openType", "props", "color", "addStyle", "throttle"], "mappings": ";;;;;;;;;;AAoKA,MAAe,YAAA;AAAA,EACX,MAAM;AAAA,EAEN,QAAQ,CAACA,yCAAAA,SAASC,uCAAAA,OAAOC,wCAAAA,aAAaC,0CAAAA,UAAUC,+CAAAA,KAAK;AAAA,EAKrD,OAAO;AACH,WAAO;EACX;AAAA,EACA,UAAU;AAAA;AAAA,IAEN,WAAW;AAEH,UAAA,CAAC,KAAK,OAAO;AACb,eAAO,KAAK;AAAA,UACR;AAAA,UACA,CAAC,QAAQ,SAAS,MAAM;AAAA,UACxB,CAAC,YAAY,SAAS,UAAU;AAAA,QAAA;AAAA,MACpC,OACG;AAEH,eAAO,KAAK;AAAA,UACR;AAAA,UACA,CAAC,SAAS,MAAM;AAAA,UAChB,CAAC,YAAY,SAAS,UAAU;AAAA,QAAA;AAAA,MAExC;AAAA,IACJ;AAAA,IACA,eAAe;AACX,UAAI,KAAK,OAAO;AAEL,eAAA,KAAK,QACN,KAAK,QACLC,wCAAAA,MAAM,KAAK,KAAK,IAAI,EAAE;AAAA,MAChC;AACI,UAAA,KAAK,SAAS,QAAQ;AACf,eAAA;AAAA,MACX;AACO,aAAA;AAAA,IACX;AAAA,IACA,eAAe;AAGpB,UAAI,KAAK;AAAW,eAAO,KAAK;AAChC,UAAI,KAAK,OAAO;AACH,eAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,MAAA,OACnC;AACI,eAAA,KAAK,SAAS,SAAS,YAAY;AAAA,MAC9C;AAAA,IACJ;AAAA,IACA,YAAY;AACR,UAAI,QAAQ,CAAA;AACZ,UAAI,KAAK,OAAO;AAEZ,cAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AACpC,YAAA,CAAC,KAAK,OAAO;AAEP,gBAAA,kBAAkB,IAAI,KAAK;AAAA,QACrC;AACA,YAAI,KAAK,MAAM,QAAQ,UAAU,MAAM,IAAI;AAIvC,gBAAM,iBAAiB;AACvB,gBAAM,mBAAmB;AACzB,gBAAM,oBAAoB;AAC1B,gBAAM,kBAAkB;AACpB,cAAA,CAAC,KAAK,OAAO;AACb,kBAAM,kBAAkB,KAAK;AAAA,UACjC;AAAA,QAAA,OACG;AAEH,gBAAM,cAAc,KAAK;AACzB,gBAAM,cAAc;AACpB,gBAAM,cAAc;AAAA,QACxB;AAAA,MACJ;AACO,aAAA;AAAA,IACX;AAAA;AAAA,IAEA,gBAAgB;AACZ,UAAI,QAAQ,CAAA;AAER,UAAA,KAAK,SAAS,QAAQ;AACtB,cAAM,QAAQ;AAAA,MAClB;AACA,UAAI,KAAK,OAAO;AACZ,cAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AAAA,MAC5C;AACM,YAAA,WAAW,KAAK,WAAW;AAC1B,aAAA;AAAA,IACX;AAAA;AAAA,IAEA,WAAW;AACP,UAAI,WAAW,IACX,EAAE,KAAA,IAAS;AACf,UAAI,SAAS;AAAoB,mBAAA;AACjC,UAAI,SAAS;AAAqB,mBAAA;AAClC,UAAI,SAAS;AAAoB,mBAAA;AACjC,UAAI,SAAS;AAAmB,mBAAA;AACzB,aAAA;AAAA,IACX;AAAA,EACJ;AAAA,EACH,OAAO;AAAA,IAAC;AAAA,IAAS;AAAA,IAAkB;AAAA,IAClC;AAAA,IAAS;AAAA,IAAe;AAAA,IAAa;AAAA,EAA2B;AAAA,EAC9D,SAAS;AAAA,IAAA,UACLC,0CAAA;AAAA,IACA,aAAa,GAAQ;AAEjB,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS;AAE7CC,qDAAAA,SAAS,MAAM;AACT,eAAA,MAAM,SAAS,CAAC;AAAA,QAAA,GACnB,KAAK,YAAY;AAAA,MACZ;AAEK,WAAA,QAAQ,KAAK,aAAa,CAAC;AAAA,IACpC;AAAA;AAAA,IAEA,eAAe,KAAU;AAChB,WAAA,MAAM,kBAAkB,GAAG;AAAA,IACpC;AAAA,IACA,YAAY,KAAU;AACb,WAAA,MAAM,eAAe,GAAG;AAAA,IACjC;AAAA,IACA,MAAM,KAAU;AACP,WAAA,MAAM,SAAS,GAAG;AAAA,IAC3B;AAAA,IACA,YAAY,KAAU;AACb,WAAA,MAAM,eAAe,GAAG;AAAA,IACjC;AAAA,IACA,UAAU,KAAU;AACX,WAAA,MAAM,aAAa,GAAG;AAAA,IAC/B;AAAA,IACA,0BAA0B,KAAK;AACtB,WAAA,MAAM,6BAA6B,GAAG;AAAA,IAC/C;AAAA,EACJ;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/SA,GAAG,gBAAgB,SAAS;"}