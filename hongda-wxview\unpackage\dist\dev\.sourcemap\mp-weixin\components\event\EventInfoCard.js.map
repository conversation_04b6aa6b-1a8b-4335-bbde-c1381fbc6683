{"version": 3, "file": "EventInfoCard.js", "sources": ["components/event/EventInfoCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRJbmZvQ2FyZC52dWU"], "sourcesContent": ["<template>\n  <view class=\"info-card\">\n    <view class=\"card-header\">\n      <view :class=\"['status-tag-detail', getStatusClass(localEvent.status)]\">\n        <image class=\"status-bg-image\" :src=\"detailBgUrl\" mode=\"aspectFit\"></image>\n        <text class=\"status-text\">{{ formatEventStatus(localEvent.status) }}</text>\n      </view>\n      <view class=\"event-title-section\">\n        <text class=\"event-title\">{{ localEvent.title || '' }}</text>\n      </view>\n    </view>\n\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailTimeIconUrl\" mode=\"aspectFit\"></image>\n      <text class=\"info-text\">{{ formatEventTime }}</text>\n    </view>\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailLocationIconUrl\" mode=\"aspectFit\"></image>\n      <text class=\"info-text\">{{ localEvent.location || '' }}</text>\n    </view>\n    <view class=\"info-row\">\n      <image class=\"info-icon\" :src=\"detailUserIconUrl\" mode=\"aspectFit\"></image>\n      <rich-text :nodes=\"remainingSpotsNodes\" class=\"info-text\"></rich-text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { computed, ref, onMounted } from 'vue'\nimport { formatEventStatus, getStatusClass } from '@/utils/tools.js'\nimport { formatDate } from '@/utils/date.js'\n\nconst props = defineProps({\n  eventDetail: { type: Object, required: true }\n})\n\n// 静态资源 URL（不再使用本地兜底）\nconst detailBgUrl = ref('')\nconst detailTimeIconUrl = ref('')\nconst detailLocationIconUrl = ref('')\nconst detailUserIconUrl = ref('')\n\n// 组件挂载时读取静态资源配置\nonMounted(() => {\n  const assets = uni.getStorageSync('staticAssets')\n  \n  detailBgUrl.value = assets?.detail_bg || ''\n  detailTimeIconUrl.value = assets?.detail_icon_time || ''\n  detailLocationIconUrl.value = assets?.detail_icon_location || ''\n  detailUserIconUrl.value = assets?.detail_icon_user || ''\n})\n\nconst localEvent = computed(() => props.eventDetail || {})\n\nconst formatEventTime = computed(() => {\n  if (!localEvent.value?.startTime || !localEvent.value?.endTime) {\n    return '时间待定'\n  }\n  try {\n    const startTime = formatDate(localEvent.value.startTime, 'YYYY-MM-DD HH:mm')\n    const endTime = formatDate(localEvent.value.endTime, 'YYYY-MM-DD HH:mm')\n    return `${startTime} 至 ${endTime}`\n  } catch (error) {\n    console.warn('时间格式化失败:', error)\n    return '时间格式错误'\n  }\n})\n\nconst remainingSpotsNodes = computed(() => {\n  if (!localEvent.value) {\n    return [{ type: 'text', text: '加载中...' }]\n  }\n\n  const max = Number(localEvent.value.maxParticipants) || 0\n  if (max === 0) {\n    return [{ type: 'text', text: '剩余名额: 不限人数' }]\n  }\n\n  const registered = Number(localEvent.value.registeredCount) || 0\n  const remaining = Math.max(0, max - registered)\n\n  return [\n    {\n      type: 'node',\n      name: 'span',\n      children: [\n        { type: 'text', text: '剩余名额: ' },\n        {\n          type: 'node',\n          name: 'span',\n          attrs: { style: 'color: #023F98;' },\n          children: [{ type: 'text', text: String(remaining) }]\n        },\n        { type: 'text', text: `/${max}` }\n      ]\n    }\n  ]\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.info-card {\n  background: #FFFFFF;\n  margin: 0 30rpx;\n  margin-top: -60rpx;\n  border-radius: 16rpx 16rpx 16rpx 16rpx;\n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);\n  padding: 30rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.info-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n}\n\n.status-tag-detail {\n  width: 90rpx;\n  height: 40rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n  font-size: 22rpx;\n  border-radius: 12rpx;\n  margin-right: 16rpx;\n  margin-top: 15rpx;\n  flex-shrink: 0;\n  &.ended {\n    background-color: #909399;\n  }\n  &.ended .status-bg-image {\n    display: none;\n  }\n}\n\n.event-title-section {\n  flex: 1;\n  min-width: 0;\n  margin-left: 16rpx;\n}\n\n.event-title {\n  white-space: normal;\n  word-break: break-word;\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n  font-weight: normal;\n  font-size: 32rpx;\n  color: #23232A;\n  line-height: 1.5;\n}\n\n.info-row {\n  display: flex;\n  align-items: center;\n  margin-top: 24rpx;\n}\n\n.info-text {\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\n  font-size: 26rpx;\n  color: #606266;\n  margin-left: 16rpx;\n}\n\n.status-bg-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.status-text {\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n  font-weight: normal;\n  font-size: 22rpx;\n  color: #023F98;\n  position: relative;\n  z-index: 2;\n}\n</style>\n\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventInfoCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni", "computed", "formatDate"], "mappings": ";;;;;;;;;;AAgCA,UAAM,QAAQ;AAKd,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAChC,UAAM,wBAAwBA,cAAG,IAAC,EAAE;AACpC,UAAM,oBAAoBA,cAAG,IAAC,EAAE;AAGhCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,kBAAY,SAAQ,iCAAQ,cAAa;AACzC,wBAAkB,SAAQ,iCAAQ,qBAAoB;AACtD,4BAAsB,SAAQ,iCAAQ,yBAAwB;AAC9D,wBAAkB,SAAQ,iCAAQ,qBAAoB;AAAA,IACxD,CAAC;AAED,UAAM,aAAaC,cAAAA,SAAS,MAAM,MAAM,eAAe,CAAA,CAAE;AAEzD,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;;AACrC,UAAI,GAAC,gBAAW,UAAX,mBAAkB,cAAa,GAAC,gBAAW,UAAX,mBAAkB,UAAS;AAC9D,eAAO;AAAA,MACR;AACD,UAAI;AACF,cAAM,YAAYC,WAAAA,WAAW,WAAW,MAAM,WAAW,kBAAkB;AAC3E,cAAM,UAAUA,WAAAA,WAAW,WAAW,MAAM,SAAS,kBAAkB;AACvE,eAAO,GAAG,SAAS,MAAM,OAAO;AAAA,MACjC,SAAQ,OAAO;AACdF,sBAAAA,MAAA,MAAA,QAAA,4CAAa,YAAY,KAAK;AAC9B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,sBAAsBC,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,SAAQ,CAAE;AAAA,MACzC;AAED,YAAM,MAAM,OAAO,WAAW,MAAM,eAAe,KAAK;AACxD,UAAI,QAAQ,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,aAAY,CAAE;AAAA,MAC7C;AAED,YAAM,aAAa,OAAO,WAAW,MAAM,eAAe,KAAK;AAC/D,YAAM,YAAY,KAAK,IAAI,GAAG,MAAM,UAAU;AAE9C,aAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,YACR,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO,EAAE,OAAO,kBAAmB;AAAA,cACnC,UAAU,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,SAAS,GAAG;AAAA,YACrD;AAAA,YACD,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,GAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;AChGD,GAAG,gBAAgB,SAAS;"}