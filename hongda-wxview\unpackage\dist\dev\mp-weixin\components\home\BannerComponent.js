"use strict";
const common_vendor = require("../../common/vendor.js");
const api_platform_nav = require("../../api/platform/nav.js");
const utils_navigation = require("../../utils/navigation.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_up_swiper2 = common_vendor.resolveComponent("up-swiper");
  _easycom_up_swiper2();
}
const _easycom_up_swiper = () => "../../uni_modules/uview-plus/components/u-swiper/u-swiper.js";
if (!Math) {
  _easycom_up_swiper();
}
const _sfc_main = {
  __name: "BannerComponent",
  setup(__props) {
    const bannerList = common_vendor.ref([]);
    const swiperList = common_vendor.computed(
      () => bannerList.value.map((item) => ({
        ...item,
        url: utils_image.getFullImageUrl(item.iconUrl)
        // 使用工具函数确保图片URL是完整的
      }))
    );
    const fetchBannerData = async () => {
      try {
        const res = await api_platform_nav.getNavList("HOME_BANNER");
        if (res.data) {
          bannerList.value = res.data;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取轮播图失败:", error);
      }
    };
    const handleBannerClick = (index) => {
      const bannerItem = bannerList.value[index];
      if (bannerItem) {
        utils_navigation.navigateTo(bannerItem);
      }
    };
    common_vendor.onMounted(() => {
      fetchBannerData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: swiperList.value.length > 0
      }, swiperList.value.length > 0 ? {
        b: common_vendor.o(handleBannerClick),
        c: common_vendor.p({
          list: swiperList.value,
          keyName: "url",
          circular: true,
          autoplay: true,
          interval: 3e3,
          duration: 500,
          indicator: true,
          indicatorActiveColor: "#FFFFFF",
          indicatorInactiveColor: "rgba(255, 255, 255, 0.5)",
          indicatorMode: "dot",
          imgMode: "aspectFill",
          height: "296rpx",
          radius: "16rpx"
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f2b3e529"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
