{"version": 3, "file": "util.js", "sources": ["uni_modules/uview-plus/components/u-icon/util.js"], "sourcesContent": ["import config from '../../libs/config/config';\r\n// 定义高阶函数\r\nfunction once(fn) {\r\n    let called = false;\r\n    let result;\r\n\r\n    return function(...args) {\r\n        if (!called) {\r\n            result = fn.apply(this, args);\r\n            called = true;\r\n        }\r\n        return result;\r\n    };\r\n}\r\n\r\n// 使用高阶函数\r\nconst loadFont = once(() => {\r\n    // console.log('这个函数只能执行一次');\r\n    // #ifdef APP-NVUE\r\n    // nvue通过weex的dom模块引入字体，相关文档地址如下：\r\n    // https://weex.apache.org/zh/docs/modules/dom.html#addrule\r\n    const domModule = weex.requireModule('dom');\r\n    domModule.addRule('fontFace', {\r\n        'fontFamily': \"uicon-iconfont\",\r\n        'src': `url('${config.iconUrl}')`\r\n    });\r\n    if (config.customIcon.family) {\r\n        domModule.addRule('fontFace', {\r\n            'fontFamily': config.customIcon.family,\r\n            'src': `url('${config.customIcon.url}')`\r\n        });\r\n    }\r\n    // #endif\r\n    // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\r\n    uni.loadFontFace({\r\n        global: true, // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。\r\n        family: 'uicon-iconfont',\r\n        source: 'url(\"' + config.iconUrl + '\")',\r\n        success() {\r\n            // console.log('内置字体图标加载成功');\r\n        },\r\n        fail() {\r\n            // console.error('内置字体图标加载出错');\r\n        }\r\n    });\r\n    if (config.customIcon.family) {\r\n        uni.loadFontFace({\r\n            global: true, // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。\r\n            family: config.customIcon.family,\r\n            source: 'url(\"' + config.customIcon.url + '\")',\r\n            success() {\r\n                // console.log('扩展字体图标加载成功');\r\n            },\r\n            fail() {\r\n                // console.error('扩展字体图标加载出错');\r\n            }\r\n        });\r\n    }\r\n    // #endif\r\n    // #ifdef APP-NVUE\r\n    // if (this.customFontFamily) {\r\n    //     domModule.addRule('fontFace', {\r\n    //         'fontFamily': `${this.customPrefix}-${this.customFontFamily}`,\r\n    //         'src': `url('${this.customFontUrl}')`\r\n    //     })\r\n    // }\r\n    // #endif\r\n    return true;\r\n});\r\n\r\nlet fontUtil = {\r\n    loadFont\r\n}\r\n\r\nexport default fontUtil\r\n"], "names": ["uni", "config"], "mappings": ";;;AAEA,SAAS,KAAK,IAAI;AACd,MAAI,SAAS;AACb,MAAI;AAEJ,SAAO,YAAY,MAAM;AACrB,QAAI,CAAC,QAAQ;AACT,eAAS,GAAG,MAAM,MAAM,IAAI;AAC5B,eAAS;AAAA,IACZ;AACD,WAAO;AAAA,EACf;AACA;AAGA,MAAM,WAAW,KAAK,MAAM;AAkBxBA,gBAAAA,MAAI,aAAa;AAAA,IACb,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ,UAAUC,gDAAO,UAAU;AAAA,IACnC,UAAU;AAAA,IAET;AAAA,IACD,OAAO;AAAA,IAEN;AAAA,EACT,CAAK;AACD,MAAIA,yCAAM,OAAC,WAAW,QAAQ;AAC1BD,kBAAAA,MAAI,aAAa;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,QAAQC,yCAAAA,OAAO,WAAW;AAAA,MAC1B,QAAQ,UAAUA,yCAAAA,OAAO,WAAW,MAAM;AAAA,MAC1C,UAAU;AAAA,MAET;AAAA,MACD,OAAO;AAAA,MAEN;AAAA,IACb,CAAS;AAAA,EACJ;AAUD,SAAO;AACX,CAAC;AAEE,IAAC,WAAW;AAAA,EACX;AACJ;;"}