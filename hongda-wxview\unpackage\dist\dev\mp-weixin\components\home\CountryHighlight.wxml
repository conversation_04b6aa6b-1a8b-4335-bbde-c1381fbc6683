<view wx:if="{{a}}" class="highlight-container data-v-edc8cefd"><view class="section-header data-v-edc8cefd"><text class="title-main data-v-edc8cefd">出海</text><text class="title-gradient data-v-edc8cefd">国别</text></view><scroll-view class="country-selector-scroll data-v-edc8cefd" scroll-x show-scrollbar="{{false}}"><view class="country-selector-inner data-v-edc8cefd"><view wx:for="{{b}}" wx:for-item="country" wx:key="e" class="{{['country-card', 'data-v-edc8cefd', country.f && 'active']}}" bindtap="{{country.g}}"><image class="country-card-bg data-v-edc8cefd" src="{{country.a}}" mode="aspectFill"></image><view class="{{['corner-badge', 'data-v-edc8cefd', country.c && 'is-gold']}}" style="{{country.d}}"><text class="badge-text data-v-edc8cefd">{{country.b}}</text></view></view></view></scroll-view><view class="tabs-wrapper data-v-edc8cefd"><scroll-view class="tabs data-v-edc8cefd" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{c}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', 'data-v-edc8cefd', tab.d && 'active']}}" bindtap="{{tab.e}}" style="{{tab.f}}"><image class="tab-icon data-v-edc8cefd" src="{{tab.a}}"></image><text class="tab-text data-v-edc8cefd">{{tab.b}}</text></view></scroll-view></view><view wx:if="{{d}}" class="content-loading data-v-edc8cefd"><uni-load-more wx:if="{{e}}" class="data-v-edc8cefd" u-i="edc8cefd-0" bind:__l="__l" u-p="{{e}}"/></view><view wx:elif="{{f}}" class="content-display-area data-v-edc8cefd"><view class="content-header data-v-edc8cefd"><text class="content-title data-v-edc8cefd">{{g}}</text><view class="more-link data-v-edc8cefd" bindtap="{{i}}"><text class="data-v-edc8cefd">更多</text><uni-icons wx:if="{{h}}" class="data-v-edc8cefd" u-i="edc8cefd-1" bind:__l="__l" u-p="{{h}}"></uni-icons></view></view><view class="summary-content data-v-edc8cefd"><rich-text class="data-v-edc8cefd" nodes="{{j}}"></rich-text></view></view></view>