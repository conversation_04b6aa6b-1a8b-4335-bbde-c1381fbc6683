{"version": 3, "file": "swipeAction.js", "sources": ["uni_modules/uview-plus/components/u-swipe-action/swipeAction.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:00:42\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeAction.js\r\n */\r\nexport default {\r\n    // swipe-action组件\r\n    swipeAction: {\r\n        autoClose: true\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,WAAW;AAAA,EACd;AACL;;"}