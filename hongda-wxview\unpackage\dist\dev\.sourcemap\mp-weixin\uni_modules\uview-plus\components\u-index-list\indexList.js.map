{"version": 3, "file": "indexList.js", "sources": ["uni_modules/uview-plus/components/u-index-list/indexList.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:13:35\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexList.js\r\n */\r\nexport default {\r\n    // indexList 组件\r\n    indexList: {\r\n        inactiveColor: '#606266',\r\n        activeColor: '#5677fc',\r\n        indexList: [],\r\n        sticky: true,\r\n        customNavHeight: 0,\r\n        safeBottomFix: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW,CAAE;AAAA,IACb,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,eAAe;AAAA,EAClB;AACL;;"}