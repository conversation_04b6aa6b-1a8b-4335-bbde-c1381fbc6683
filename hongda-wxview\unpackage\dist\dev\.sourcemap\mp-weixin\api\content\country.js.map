{"version": 3, "file": "country.js", "sources": ["api/content/country.js"], "sourcesContent": ["// 引入封装好的 http 对象\r\nimport http from '@/utils/request.js';\r\n\r\n/**\r\n * 获取国别列表\r\n * @param {Object} params - 查询参数\r\n * @param {string} params.continent - 大洲 (例如: 'ASIA', 'EUROPE', 'ALL')\r\n * @param {string} params.keyword - 搜索关键词\r\n */\r\nexport function getCountryList(params) {\r\n\t// 定义相对于 baseURL ('/api/v1') 的接口路径\r\n\tconst url = '/country/list';\r\n\r\n\t// 直接使用封装好的 http.get 方法，它会自动处理参数拼接\r\n\treturn http.get(url, params);\r\n}\r\n\r\n/**\r\n * 【新增】根据ID获取国别详情\r\n * (请将此函数完整地复制并添加到您的 api/pages_country.js 文件中)\r\n * @param {number | string} id - 国家ID\r\n */\r\nexport function getCountryDetail(id) {\r\n\tconst url = `/country/${id}`;\r\n\treturn http.get(url);\r\n}"], "names": ["http"], "mappings": ";;AASO,SAAS,eAAe,QAAQ;AAEtC,QAAM,MAAM;AAGZ,SAAOA,mBAAK,IAAI,KAAK,MAAM;AAC5B;AAOO,SAAS,iBAAiB,IAAI;AACpC,QAAM,MAAM,YAAY,EAAE;AAC1B,SAAOA,cAAI,KAAC,IAAI,GAAG;AACpB;;;"}