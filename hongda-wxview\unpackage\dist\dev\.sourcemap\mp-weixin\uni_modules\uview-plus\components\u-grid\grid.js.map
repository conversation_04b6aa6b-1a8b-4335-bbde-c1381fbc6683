{"version": 3, "file": "grid.js", "sources": ["uni_modules/uview-plus/components/u-grid/grid.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:05:57\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/grid.js\r\n */\r\nexport default {\r\n    // grid组件\r\n    grid: {\r\n        col: 3,\r\n        border: false,\r\n        align: 'left'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,EACV;AACL;;"}