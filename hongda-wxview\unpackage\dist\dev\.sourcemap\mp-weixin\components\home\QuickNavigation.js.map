{"version": 3, "file": "QuickNavigation.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9RdWlja05hdmlnYXRpb24udnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"navList.length > 0\" class=\"nav-container\">\r\n    <view class=\"nav-grid\">\r\n      <view\r\n          v-for=\"item in navList\"\r\n          :key=\"item.id\"\r\n          class=\"nav-item\"\r\n          @click=\"handleNavClick(item)\"\r\n      >\r\n        <view class=\"nav-icon-wrapper\">\r\n          <image class=\"nav-icon\" :src=\"baseUrl + item.iconUrl\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <text class=\"nav-text\">{{ item.title }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n// --- script 部分完全保持不变 ---\r\nimport { ref, onMounted } from 'vue';\r\nimport { getNavList } from '@/api/platform/nav.js';\r\nimport { IMAGE_BASE_URL } from '@/utils/config.js';\r\nimport { navigateTo } from '@/utils/navigation.js';\r\n\r\nconst baseUrl = IMAGE_BASE_URL;\r\nconst navList = ref([]);\r\n\r\nconst fetchNavData = async () => {\r\n  try {\r\n    const res = await getNavList('HOME_QUICK_NAV');\r\n    if (res.data) {\r\n      navList.value = res.data.slice(0, 8);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取快捷导航失败:', error);\r\n  }\r\n};\r\n\r\nconst handleNavClick = (navItem) => {\r\n  navigateTo(navItem);\r\n};\r\n\r\nonMounted(() => {\r\n  fetchNavData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 样式已根据“全宽白底”要求进行修改 --- */\r\n\r\n.nav-container {\r\n  /* 核心修改1：容器铺满全宽，背景为纯白，并有合适的内外边距 */\r\n  width: 100%;\r\n  background-color: #ffffff;\r\n  padding: 32rpx 0; /* 上下内边距，左右为0 */\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  row-gap: 36rpx;\r\n  column-gap: 20rpx;\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n.nav-icon-wrapper {\r\n  /* 核心修改2：因为容器宽度变化，图标大小进行适配 */\r\n  width: 90rpx;\r\n  height: 90rpx;\r\n  border-radius: 28rpx; /* 保持原有的圆角矩形 */\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  overflow: hidden;\r\n  background-color: #f8fafc; /* 保持原有的淡色背景 */\r\n\r\n  /* 保持原有的边框和阴影效果 */\r\n  border: 2rpx solid rgba(0, 122, 255, 0.04);\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.nav-item:active .nav-icon {\r\n  transform: scale(0.9);\r\n}\r\n\r\n.nav-text {\r\n  font-size: 24rpx;\r\n  color: #23232A;\r\n  font-weight: 500;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n/* 核心修改3：移除了与卡片式布局相关的 @media 响应式设计，因为它不再适用 */\r\n\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["IMAGE_BASE_URL", "ref", "getNavList", "uni", "navigateTo", "onMounted"], "mappings": ";;;;;;;;AAyBA,UAAM,UAAUA,aAAAA;AAChB,UAAM,UAAUC,cAAAA,IAAI,CAAA,CAAE;AAEtB,UAAM,eAAe,YAAY;AAC/B,UAAI;AACF,cAAM,MAAM,MAAMC,4BAAW,gBAAgB;AAC7C,YAAI,IAAI,MAAM;AACZ,kBAAQ,QAAQ,IAAI,KAAK,MAAM,GAAG,CAAC;AAAA,QACpC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,6CAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,YAAY;AAClCC,uBAAU,WAAC,OAAO;AAAA,IACpB;AAEAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;AC5CD,GAAG,gBAAgB,SAAS;"}