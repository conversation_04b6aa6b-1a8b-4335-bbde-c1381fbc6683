{"version": 3, "file": "platform.js", "sources": ["uni_modules/uview-plus/libs/function/platform.js"], "sourcesContent": ["/**\r\n * 注意：\r\n * 此部分内容，在vue-cli模式下，需要在vue.config.js加入如下内容才有效：\r\n * module.exports = {\r\n *     transpileDependencies: ['uview-v2']\r\n * }\r\n */\r\n\r\nlet platform = 'none'\r\n\r\n// #ifdef VUE3\r\nplatform = 'vue3'\r\n// #endif\r\n\r\n// #ifdef VUE2\r\nplatform = 'vue2'\r\n// #endif\r\n\r\n// #ifdef APP-PLUS\r\nplatform = 'plus'\r\n// #endif\r\n\r\n// #ifdef APP-NVUE\r\nplatform = 'nvue'\r\n// #endif\r\n\r\n// #ifdef H5\r\nplatform = 'h5'\r\n// #endif\r\n\r\n// #ifdef MP\r\nplatform = 'mp'\r\n// #endif\r\n\r\n// #ifdef MP-WEIXIN\r\nplatform = 'weixin'\r\n// #endif\r\n\r\n// #ifdef MP-ALIPAY\r\nplatform = 'alipay'\r\n// #endif\r\n\r\n// #ifdef MP-BAIDU\r\nplatform = 'baidu'\r\n// #endif\r\n\r\n// #ifdef MP-TOUTIAO\r\nplatform = 'toutiao'\r\n// #endif\r\n\r\n// #ifdef MP-QQ\r\nplatform = 'qq'\r\n// #endif\r\n\r\n// #ifdef MP-KUAISHOU\r\nplatform = 'kuaishou'\r\n// #endif\r\n\r\n// #ifdef MP-360\r\nplatform = '360'\r\n// #endif\r\n\r\n// #ifdef QUICKAPP-WEBVIEW\r\nplatform = 'quickapp-webview'\r\n// #endif\r\n\r\n// #ifdef QUICKAPP-WEBVIEW-HUAWEI\r\nplatform = 'quickapp-webview-huawei'\r\n// #endif\r\n\r\n// #ifdef QUICKAPP-WEBVIEW-UNION\r\nplatform = 'quckapp-webview-union'\r\n// #endif\r\n\r\nexport default platform\r\n"], "names": [], "mappings": ";AAQA,IAAI,WAAW;AAGf,WAAW;AAoBX,WAAW;AAIX,WAAW;AAuCX,MAAe,aAAA;;"}