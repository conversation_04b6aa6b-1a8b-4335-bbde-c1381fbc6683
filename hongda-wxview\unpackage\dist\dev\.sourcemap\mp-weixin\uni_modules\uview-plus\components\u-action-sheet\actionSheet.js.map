{"version": 3, "file": "actionSheet.js", "sources": ["uni_modules/uview-plus/components/u-action-sheet/actionSheet.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:44:35\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/actionSheet.js\r\n */\r\nexport default {\r\n    // action-sheet组件\r\n    actionSheet: {\r\n        show: false,\r\n        title: '',\r\n        description: '',\r\n        actions: [],\r\n        index: '',\r\n        cancelText: '',\r\n        closeOnClickAction: true,\r\n        safeAreaInsetBottom: true,\r\n        openType: '',\r\n        closeOnClickOverlay: true,\r\n        round: 0,\r\n        wrapMaxHeight: '600px'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,SAAS,CAAE;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP,eAAe;AAAA,EAClB;AACL;;"}