{"version": 3, "file": "CountryHighlight.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9Db3VudHJ5SGlnaGxpZ2h0LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"!loading && countryList.length > 0\" class=\"highlight-container\">\r\n    <view class=\"section-header\">\r\n      <text class=\"title-main\">出海</text>\r\n      <text class=\"title-gradient\">国别</text>\r\n    </view>\r\n\r\n    <scroll-view class=\"country-selector-scroll\" scroll-x :show-scrollbar=\"false\">\r\n      <view class=\"country-selector-inner\">\r\n        <view\r\n            v-for=\"country in countryList\"\r\n            :key=\"country.id\"\r\n            class=\"country-card\"\r\n            :class=\"{ active: selectedCountryId === country.id }\"\r\n            @click=\"selectCountry(country.id)\"\r\n        >\r\n          <!-- 1. 直接使用后端返回的完整URL -->\r\n          <image class=\"country-card-bg\" :src=\"country.listCoverUrl\" mode=\"aspectFill\"></image>\r\n\r\n          <!-- 2. 【关键修改】同时绑定 :class 和 :style -->\r\n          <view\r\n              class=\"corner-badge\"\r\n              :class=\"{ 'is-gold': selectedCountryId === country.id }\"\r\n              :style=\"selectedCountryId === country.id ? goldBadgeStyle : blueBadgeStyle\"\r\n          >\r\n            <text class=\"badge-text\">{{ country.nameCn }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <view class=\"tabs-wrapper\">\r\n      <scroll-view class=\"tabs\" scroll-x=\"true\" :show-scrollbar=\"false\">\r\n        <view\r\n            v-for=\"tab in tabs\"\r\n            :key=\"tab.id\"\r\n            class=\"tab-item\"\r\n            :class=\"{ active: activeTabId === tab.id }\"\r\n            @click=\"onTabClick(tab.id)\"\r\n            :style=\"activeTabId === tab.id ? activeTabStyle : {}\"\r\n        >\r\n          <image class=\"tab-icon\" :src=\"activeTabId === tab.id ? tab.activeIcon : tab.icon\"></image>\r\n          <text class=\"tab-text\">{{ tab.name }}</text>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <view v-if=\"detailLoading\" class=\"content-loading\">\r\n      <uni-load-more status=\"loading\" />\r\n    </view>\r\n    <view v-else-if=\"selectedCountryDetails\" class=\"content-display-area\">\r\n      <view class=\"content-header\">\r\n        <text class=\"content-title\">{{ selectedContentTitle }}</text>\r\n        <view class=\"more-link\" @click=\"navigateToDetailWithTab\">\r\n          <text>更多</text>\r\n          <uni-icons type=\"right\" size=\"14\" color=\"#888\"></uni-icons>\r\n        </view>\r\n      </view>\r\n      <view class=\"summary-content\">\r\n        <rich-text :nodes=\"selectedContent\"></rich-text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { getCountryList, getCountryDetail } from '@/api/content/country.js';\r\n\r\n// --- 状态定义 ---\r\nconst loading = ref(true);\r\nconst detailLoading = ref(false);\r\nconst countryList = ref([]);\r\nconst selectedCountryId = ref(null);\r\nconst selectedCountryDetails = ref(null);\r\nconst activeTabId = ref('basic');\r\nconst assets = ref({}); // 用于存储从缓存读取的静态资源\r\n\r\n// --- Tabs 静态配置 ---\r\nconst tabsConfig = ref([\r\n  { id: 'basic', name: '基本信息', iconKey: 'icon_tab_basic_normal', activeIconKey: 'icon_tab_basic_active' },\r\n  { id: 'investment', name: '招商政策', iconKey: 'icon_tab_investment_normal', activeIconKey: 'icon_tab_investment_active' },\r\n  { id: 'customs', name: '海关政策', iconKey: 'icon_tab_customs_normal', activeIconKey: 'icon_tab_customs_active' },\r\n  { id: 'tax', name: '税务政策', iconKey: 'icon_tab_tax_normal', activeIconKey: 'icon_tab_tax_active' },\r\n  { id: 'parks', name: '工业园区', iconKey: 'icon_tab_parks_normal', activeIconKey: 'icon_tab_parks_active' },\r\n]);\r\n\r\n// --- 计算属性 ---\r\n\r\n// 动态计算 tabs 列表，自动从缓存替换图标URL\r\nconst tabs = computed(() => {\r\n  return tabsConfig.value.map(tab => ({\r\n    id: tab.id,\r\n    name: tab.name,\r\n    icon: assets.value[tab.iconKey] || '',\r\n    activeIcon: assets.value[tab.activeIconKey] || ''\r\n  }));\r\n});\r\n\r\n// 动态计算背景样式\r\nconst goldBadgeStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_badge_gold ? `url('${assets.value.bg_badge_gold}')` : 'none'\r\n}));\r\nconst blueBadgeStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_badge_blue ? `url('${assets.value.bg_badge_blue}')` : 'none'\r\n}));\r\nconst activeTabStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_tab_active_home ? `url('${assets.value.bg_tab_active_home}')` : 'none'\r\n}));\r\n\r\nconst selectedContentTitle = computed(() => {\r\n  const countryName = selectedCountryDetails.value?.nameCn || '';\r\n  const tabName = tabs.value.find(t => t.id === activeTabId.value)?.name || '';\r\n  return `${countryName} - ${tabName}`;\r\n});\r\n\r\nconst selectedContent = computed(() => {\r\n  if (!selectedCountryDetails.value) return '<p>暂无相关信息。</p>';\r\n  const contentMapping = {\r\n    basic: selectedCountryDetails.value.introduction,\r\n    investment: selectedCountryDetails.value.investmentPolicy,\r\n    customs: selectedCountryDetails.value.customsPolicy,\r\n    tax: selectedCountryDetails.value.taxPolicy,\r\n    parks: '<p>请点击“更多”查看详细的工业园区列表。</p>'\r\n  };\r\n  return contentMapping[activeTabId.value] || '<p>暂无相关信息。</p>';\r\n});\r\n\r\n// --- 方法 ---\r\n\r\nconst fetchCountryDetails = async (id) => {\r\n  detailLoading.value = true;\r\n  selectedCountryDetails.value = null;\r\n  try {\r\n    const res = await getCountryDetail(id);\r\n    selectedCountryDetails.value = res.data;\r\n  } catch (error) {\r\n    console.error(`获取ID为 ${id} 的国别详情失败:`, error);\r\n  } finally {\r\n    detailLoading.value = false;\r\n  }\r\n};\r\n\r\nconst fetchFeaturedCountries = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const params = { pageNum: 1, pageSize: 5 };\r\n    const res = await getCountryList(params);\r\n    if (res.data && res.data.length > 0) {\r\n      countryList.value = res.data;\r\n      const firstCountryId = res.data[0].id;\r\n      selectedCountryId.value = firstCountryId;\r\n      await fetchCountryDetails(firstCountryId);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取推荐国别失败:', error);\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst selectCountry = (id) => {\r\n  if (selectedCountryId.value === id) return;\r\n  selectedCountryId.value = id;\r\n  activeTabId.value = 'basic';\r\n  fetchCountryDetails(id);\r\n};\r\n\r\nconst onTabClick = (tabId) => {\r\n  activeTabId.value = tabId;\r\n};\r\n\r\nconst navigateToDetailWithTab = () => {\r\n  if (selectedCountryId.value) {\r\n    uni.navigateTo({\r\n      url: `/pages_sub/pages_country/detail?id=${selectedCountryId.value}&tab=${activeTabId.value}`\r\n    });\r\n  }\r\n};\r\n\r\n// --- 生命周期钩子 ---\r\nonMounted(() => {\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n  fetchFeaturedCountries();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 整体容器 */\r\n.highlight-container {\r\n  padding: 32rpx;\r\n  border-radius: 20rpx;\r\n  background: linear-gradient(to bottom, rgba(2, 63, 152, 0.1), rgba(2, 63, 152, 0));\r\n}\r\n\r\n/* 标题 */\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 32rpx;\r\n}\r\n.title-main {\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n  color: #023F98;\r\n}\r\n.title-gradient {\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n  background-image: linear-gradient(to right, #FFAD22, #FFBB87);\r\n  -webkit-background-clip: text;\r\n  background-clip: text;\r\n  color: transparent;\r\n}\r\n\r\n/* 国家选择器 */\r\n.country-selector-scroll {\r\n  width: 100%;\r\n  padding-bottom: 24rpx;\r\n}\r\n.country-selector-inner {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.country-card {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  width: 300rpx;\r\n  height: 192rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  margin-right: 24rpx;\r\n  transition: transform 0.3s ease, outline 0.3s ease;\r\n\r\n  &.active {\r\n    border: 4rpx solid #FFBF51;\r\n  }\r\n}\r\n\r\n.country-card-bg {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.corner-badge {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 150rpx;\r\n  height: 44rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-size: 100% 100%;\r\n  margin-left: -20rpx;\r\n  z-index: 2;\r\n\r\n  .badge-text {\r\n    font-size: 24rpx;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n/* 3. 【关键修改】使用 class 选择器而不是属性选择器 */\r\n.corner-badge .badge-text {\r\n  color: #FFFFFF; /* 默认为白色 */\r\n}\r\n.corner-badge.is-gold .badge-text {\r\n  color: #23232A; /* 当有 is-gold 类时，文字为深色 */\r\n}\r\n\r\n\r\n/* Tab 标签栏及以下样式 */\r\n.tabs-wrapper {\r\n  background-color: #F4F4F4;\r\n  border-radius: 16rpx;\r\n}\r\n.tabs {\r\n  display: flex;\r\n  white-space: nowrap;\r\n}\r\n.tab-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 10rpx;\r\n  padding: 12rpx 24rpx;\r\n  margin-right: 20rpx;\r\n  &:last-child { margin-right: 0; }\r\n  .tab-icon { width: 40rpx; height: 40rpx; }\r\n  .tab-text { font-size: 28rpx; color: #9B9A9A; }\r\n\r\n  &.active {\r\n    background-size: cover;\r\n    background-position: center;\r\n    .tab-text { color: #23232A; font-weight: bold; }\r\n  }\r\n}\r\n\r\n.content-loading {\r\n  padding: 40rpx 0;\r\n}\r\n.content-display-area {\r\n  margin-top: 32rpx;\r\n}\r\n.content-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24rpx;\r\n}\r\n.content-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.more-link {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 26rpx;\r\n  color: #888;\r\n}\r\n.summary-content {\r\n  font-size: 26rpx;\r\n  color: #66666E;\r\n  line-height: 1.7;\r\n\r\n  :deep(strong, b) {\r\n    color: #23232A !important;\r\n  }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "getCountryDetail", "uni", "getCountryList", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;AAsEA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,oBAAoBA,cAAAA,IAAI,IAAI;AAClC,UAAM,yBAAyBA,cAAAA,IAAI,IAAI;AACvC,UAAM,cAAcA,cAAAA,IAAI,OAAO;AAC/B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AAGrB,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,SAAS,MAAM,QAAQ,SAAS,yBAAyB,eAAe,wBAAyB;AAAA,MACvG,EAAE,IAAI,cAAc,MAAM,QAAQ,SAAS,8BAA8B,eAAe,6BAA8B;AAAA,MACtH,EAAE,IAAI,WAAW,MAAM,QAAQ,SAAS,2BAA2B,eAAe,0BAA2B;AAAA,MAC7G,EAAE,IAAI,OAAO,MAAM,QAAQ,SAAS,uBAAuB,eAAe,sBAAuB;AAAA,MACjG,EAAE,IAAI,SAAS,MAAM,QAAQ,SAAS,yBAAyB,eAAe,wBAAyB;AAAA,IACzG,CAAC;AAKD,UAAM,OAAOC,cAAQ,SAAC,MAAM;AAC1B,aAAO,WAAW,MAAM,IAAI,UAAQ;AAAA,QAClC,IAAI,IAAI;AAAA,QACR,MAAM,IAAI;AAAA,QACV,MAAM,OAAO,MAAM,IAAI,OAAO,KAAK;AAAA,QACnC,YAAY,OAAO,MAAM,IAAI,aAAa,KAAK;AAAA,MAChD,EAAC;AAAA,IACJ,CAAC;AAGD,UAAM,iBAAiBA,cAAQ,SAAC,OAAO;AAAA,MACrC,iBAAiB,OAAO,MAAM,gBAAgB,QAAQ,OAAO,MAAM,aAAa,OAAO;AAAA,IACzF,EAAE;AACF,UAAM,iBAAiBA,cAAQ,SAAC,OAAO;AAAA,MACrC,iBAAiB,OAAO,MAAM,gBAAgB,QAAQ,OAAO,MAAM,aAAa,OAAO;AAAA,IACzF,EAAE;AACF,UAAM,iBAAiBA,cAAQ,SAAC,OAAO;AAAA,MACrC,iBAAiB,OAAO,MAAM,qBAAqB,QAAQ,OAAO,MAAM,kBAAkB,OAAO;AAAA,IACnG,EAAE;AAEF,UAAM,uBAAuBA,cAAQ,SAAC,MAAM;;AAC1C,YAAM,gBAAc,4BAAuB,UAAvB,mBAA8B,WAAU;AAC5D,YAAM,YAAU,UAAK,MAAM,KAAK,OAAK,EAAE,OAAO,YAAY,KAAK,MAA/C,mBAAkD,SAAQ;AAC1E,aAAO,GAAG,WAAW,MAAM,OAAO;AAAA,IACpC,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,CAAC,uBAAuB;AAAO,eAAO;AAC1C,YAAM,iBAAiB;AAAA,QACrB,OAAO,uBAAuB,MAAM;AAAA,QACpC,YAAY,uBAAuB,MAAM;AAAA,QACzC,SAAS,uBAAuB,MAAM;AAAA,QACtC,KAAK,uBAAuB,MAAM;AAAA,QAClC,OAAO;AAAA,MACX;AACE,aAAO,eAAe,YAAY,KAAK,KAAK;AAAA,IAC9C,CAAC;AAID,UAAM,sBAAsB,OAAO,OAAO;AACxC,oBAAc,QAAQ;AACtB,6BAAuB,QAAQ;AAC/B,UAAI;AACF,cAAM,MAAM,MAAMC,qCAAiB,EAAE;AACrC,+BAAuB,QAAQ,IAAI;AAAA,MACpC,SAAQ,OAAO;AACdC,4BAAA,MAAA,SAAA,+CAAc,SAAS,EAAE,aAAa,KAAK;AAAA,MAC/C,UAAY;AACR,sBAAc,QAAQ;AAAA,MACvB;AAAA,IACH;AAEA,UAAM,yBAAyB,YAAY;AACzC,cAAQ,QAAQ;AAChB,UAAI;AACF,cAAM,SAAS,EAAE,SAAS,GAAG,UAAU,EAAC;AACxC,cAAM,MAAM,MAAMC,mCAAe,MAAM;AACvC,YAAI,IAAI,QAAQ,IAAI,KAAK,SAAS,GAAG;AACnC,sBAAY,QAAQ,IAAI;AACxB,gBAAM,iBAAiB,IAAI,KAAK,CAAC,EAAE;AACnC,4BAAkB,QAAQ;AAC1B,gBAAM,oBAAoB,cAAc;AAAA,QACzC;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,+CAAA,aAAa,KAAK;AAAA,MACpC,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,OAAO;AAC5B,UAAI,kBAAkB,UAAU;AAAI;AACpC,wBAAkB,QAAQ;AAC1B,kBAAY,QAAQ;AACpB,0BAAoB,EAAE;AAAA,IACxB;AAEA,UAAM,aAAa,CAAC,UAAU;AAC5B,kBAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,0BAA0B,MAAM;AACpC,UAAI,kBAAkB,OAAO;AAC3BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,sCAAsC,kBAAkB,KAAK,QAAQ,YAAY,KAAK;AAAA,QACjG,CAAK;AAAA,MACF;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;AACd,aAAO,QAAQF,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA;AACrD;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvLD,GAAG,gBAAgB,SAAS;"}