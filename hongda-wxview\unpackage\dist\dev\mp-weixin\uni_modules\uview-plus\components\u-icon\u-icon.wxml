<view bindtap="{{s}}" class="{{['u-icon', 'data-v-ac70166d', t]}}"><image wx:if="{{a}}" class="u-icon__img data-v-ac70166d" src="{{b}}" mode="{{c}}" style="{{d + ';' + e}}"></image><text wx:else class="{{['u-icon__icon', 'data-v-ac70166d', g]}}" style="{{h + ';' + i}}" hover-class="{{j}}">{{f}}</text><text wx:if="{{k}}" class="u-icon__label data-v-ac70166d" style="{{'color:' + m + ';' + ('font-size:' + n) + ';' + ('margin-left:' + o) + ';' + ('margin-top:' + p) + ';' + ('margin-right:' + q) + ';' + ('margin-bottom:' + r)}}">{{l}}</text></view>