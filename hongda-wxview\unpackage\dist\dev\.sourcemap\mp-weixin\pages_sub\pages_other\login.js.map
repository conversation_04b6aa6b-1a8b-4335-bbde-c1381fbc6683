{"version": 3, "file": "login.js", "sources": ["pages_sub/pages_other/login.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXGxvZ2luLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"login-page\">\n    <!-- 背景图片 -->\n  <image \n      class=\"background-image\" \n      :src=\"loginBgUrl\" \n      mode=\"aspectFill\"\n    ></image>\n   <!--uview-plus 导航栏 -->\n   <up-navbar\n     title=\"登录\"\n     :autoBack=\"true\"\n     :safeAreaInsetTop=\"true\"\n     :fixed=\"true\"\n     :placeholder=\"true\"\n     bgColor=\"transparent\"\n     :zIndex=\"99\"\n     leftIconColor=\"#333333\"\n     :titleStyle=\"{\n         fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',\n         fontWeight: 'normal',\n         fontSize: '32rpx',\n         color: '#000000',\n         lineHeight: '44rpx'\n     }\"\n   >\n   </up-navbar>\n    <view class=\"content-wrapper\">\n      <view class=\"login-content\">\n    <!--  Logo 区域 -->\n      <view class=\"logo-section\">\n        <image class=\"logo-image\" :src=\"logoHdUrl\" mode=\"aspectFit\"></image>\n      </view>\n        <view class=\"action-section\">\n\t\t\t    <button\n\t\t\t      class=\"login-btn\"\n\t\t\t      :open-type=\"isAgreementChecked ? 'getPhoneNumber' : ''\"\n\t\t\t      @getphonenumber=\"onGetPhoneNumber\"\r\n\t\t\t\t  @click=\"handleLoginClick\"\n\t\t\t    >\n\t\t\t      微信手机号快捷登录\n\t\t\t    </button>\n\t\t\t\t<!-- 自定义的协议勾选区域 -->\n\t\t\t\t<view class=\"agreement-section\" @click=\"toggleAgreement\">\n\t\t\t\t  <!-- 这是我们自定义的复选框 -->\n\t\t\t\t  <view class=\"custom-checkbox\" :class=\"{ 'is-checked': isAgreementChecked }\">\n\t\t\t\t    <!-- 选中时显示的勾 -->\n\t\t\t\t    <view class=\"checkmark\" v-if=\"isAgreementChecked\"></view>\n\t\t\t\t  </view>\n\t\t\t\t  <!-- 协议文本 -->\n\t\t\t\t  <view class=\"agreement-text\">\n\t\t\t\t    <text>我已阅读并同意</text>\n\t\t\t\t    <text class=\"link-text\" @click.stop=\"goToUserAgreement\">《用户协议》</text>\n\t\t\t\t    <text>和</text>\n\t\t\t\t    <text class=\"link-text\" @click.stop=\"goToPrivacyPolicy\">《隐私政策》</text>\n\t\t\t\t  </view>\n\t\t\t\t</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport {ref} from 'vue'\nimport {onLoad} from '@dcloudio/uni-app'\nimport {getPhoneNumberApi, wxLoginApi} from '@/api/data/user.js'\nimport {getLatestPolicyApi, acceptPolicyApi} from '@/pages_sub/pages_other/api/data/policy.js'\n\n// 定义页面名称\ndefineOptions({\n  name: 'LoginPage'\n})\n\nconst isAgreementChecked = ref(false);\n\n// 静态资源 URL（仅暗号读取）\nconst loginBgUrl = ref('')\nconst logoHdUrl = ref('')\n\n// 缓存协议版本信息\nconst policyVersions = ref({\n  userAgreement: null,\n  privacyPolicy: null\n});\n\n// 点击整个协议区域时，切换勾选状态\nconst toggleAgreement = () => {\n  isAgreementChecked.value = !isAgreementChecked.value;\n};\n\n// 获取手机号并执行登录的事件处理函数\nconst onGetPhoneNumber = async (e) => {\n  console.log('=== 开始微信手机号快捷登录流程 ===')\n  console.log('授权事件详情:', e)\n  console.log('e.detail:', e.detail)\n  console.log('e.detail.code:', e.detail.code)\n\n  // 2. 检查微信是否返回了手机号授权码\n  if (!e.detail.code) {\n    console.log('微信未返回授权code');\n    console.log('错误信息:', e.detail.errMsg);\n    console.log('可能原因：1. 用户拒绝授权 2. 配置问题 3. 网络异常');\n    uni.showToast({\n      title: '获取手机号失败，请重试',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  const phoneCode = e.detail.code;\n  console.log('获取到微信手机号授权码:', phoneCode)\n  uni.showLoading({ title: '正在登录...' })\n\n  try {\n    // 3. 清除可能存在的旧token，防止缓存问题\n    uni.removeStorageSync('token')\n    uni.removeStorageSync('userInfo')\n    \n    // 3. 执行基础登录 uni.login 获取 loginCode\n    console.log('开始执行uni.login...')\n    const loginRes = await uni.login()\n    console.log('uni.login结果:', loginRes)\n    const loginCode = loginRes.code\n    console.log('获得loginCode:', loginCode)\n\n    // 4. 调用后端的 /wxlogin 接口\n    const res = await wxLoginApi(loginCode)\n    console.log('后端登录成功:', res)\n\n    // 登录成功，保存 token 和基础用户信息\n    const token = res.token\n    uni.setStorageSync('token', token)\n    uni.setStorageSync('userInfo', res.userInfo)\n\n    // 5. 调用后端的 /getPhoneNumber 接口\n    console.log('使用phoneCode:', phoneCode)\n    console.log('使用token:', token)\n    const phoneRes = await getPhoneNumberApi(phoneCode)\n    console.log('获取手机号成功:', phoneRes)\n\n    // 获取手机号成功，更新本地用户信息\n    console.log('开始更新本地用户信息...')\n    try {\n      const userInfo = uni.getStorageSync('userInfo')\n      console.log('当前userInfo:', userInfo)\n      \n      if (!userInfo) {\n        console.log('userInfo为空，创建新的用户信息对象')\n        const newUserInfo = {\n          phoneNumber: phoneRes.phoneNumber\n        }\n        uni.setStorageSync('userInfo', newUserInfo)\n      } else {\n        console.log('更新现有userInfo的手机号')\n        userInfo.phoneNumber = phoneRes.phoneNumber\n        uni.setStorageSync('userInfo', userInfo)\n      }\n      \n      console.log('用户信息更新完成')\n    } catch (updateError) {\n      console.error('更新用户信息时出错:', updateError)\n      throw updateError\n    }\n    \n    console.log('登录流程完全成功，最终保存的数据:')\n    console.log('- token:', uni.getStorageSync('token'))\n    console.log('- userInfo:', uni.getStorageSync('userInfo'))\n    \n    console.log('准备隐藏加载提示...')\n    uni.hideLoading()\n    \n    console.log('准备显示成功提示...')\n    uni.showToast({ title: '登录成功', icon: 'success' })\n\n    // 登录成功后上报协议同意记录\n    console.log('开始上报协议同意记录...')\n    try {\n      await reportPolicyAcceptance()\n    } catch (error) {\n      console.error('协议同意记录上报异常:', error)\n    }\n\n    // 延迟后返回上一页，确保数据保存完成\n    console.log('设置延迟返回定时器...')\n    setTimeout(() => {\n      console.log('定时器触发，准备返回上一页...')\n      \n      // 检查是否有指定的返回页面\n      try {\n        const loginBackPage = uni.getStorageSync('loginBackPage');\n        if (loginBackPage) {\n          console.log('检测到指定的返回页面:', loginBackPage);\n          // 不清除标记，让目标页面自己处理\n          uni.navigateBack({\n            success: () => {\n              console.log('成功返回上一页')\n            },\n            fail: (err) => {\n              console.error('返回上一页失败，尝试直接跳转到指定页面:', err);\n              // 如果无法返回，直接跳转到指定页面\n              if (loginBackPage.startsWith('/pages/')) {\n                uni.redirectTo({\n                  url: loginBackPage,\n                  fail: () => {\n                    // 如果重定向也失败，跳转到首页\n                    uni.switchTab({ url: '/pages/index/index' });\n                  }\n                });\n              }\n            }\n          });\n        } else {\n          // 正常返回上一页\n          uni.navigateBack({\n            success: () => {\n              console.log('成功返回上一页')\n            },\n            fail: (err) => {\n              console.error('返回上一页失败，跳转到首页:', err);\n              // 如果无法返回，跳转到首页\n              uni.switchTab({ url: '/pages/index/index' });\n            }\n          });\n        }\n      } catch (e) {\n        console.warn('检查返回页面标记失败:', e);\n        // 出错时正常返回\n        uni.navigateBack({\n          fail: (err) => {\n            console.error('返回上一页失败，跳转到首页:', err);\n            uni.switchTab({ url: '/pages/index/index' });\n          }\n        });\n      }\n    }, 2000) // 增加延迟时间，确保用户看到成功提示\n    \n    console.log('登录方法执行完成，等待定时器触发返回...')\n\n  } catch (error) {\n    console.error('登录过程中发生错误:', error)\n    console.error('错误详情:', {\n      message: error.message,\n      stack: error.stack,\n      name: error.name\n    })\n    \n    uni.hideLoading()\n    \n    const errorMessage = error.message || '网络请求失败'\n    console.error('显示错误提示:', errorMessage)\n    \n    uni.showToast({\n      title: errorMessage,\n      icon: 'none',\n      duration: 3000\n    })\n  }\n}\r\n\r\n\nconst handleLoginClick = () => {\n  // 这个函数只在协议未勾选时需要给出提示\n  // 因为勾选后，按钮的 open-type 生效，@click 事件可能不会触发或被 open-type 覆盖\n  if (!isAgreementChecked.value) {\n    console.log('用户未同意协议');\n    uni.showToast({\n      title: '请先同意用户协议和隐私政策',\n      icon: 'none'\n    });\n  }\n};\n\n// 跳转到用户协议\nconst goToUserAgreement = () => {\n  console.log('点击用户协议链接')\n  uni.navigateTo({\n    url: '/pages_sub/pages_other/policy?type=user_agreement',\n    success: () => {\n      console.log('成功跳转到用户协议页面')\n    },\n    fail: (err) => {\n      console.error('跳转用户协议页面失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none',\n        duration: 2000\n      })\n    }\n  })\n}\n\n// 跳转到隐私政策\nconst goToPrivacyPolicy = () => {\n  console.log('点击隐私政策链接')\n  uni.navigateTo({\n    url: '/pages_sub/pages_other/policy?type=privacy_policy',\n    success: () => {\n      console.log('成功跳转到隐私政策页面')\n    },\n    fail: (err) => {\n      console.error('跳转隐私政策页面失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none',\n        duration: 2000\n      })\n    }\n  })\n}\n\nconst cancelLogin = () => {\n  uni.navigateBack() // 返回上一页\n}\n\n// 加载协议版本信息\nconst loadPolicyVersions = async () => {\n  try {\n    console.log('开始加载协议版本信息...')\n    \n    // 并行加载两个协议的版本信息\n    const [userAgreementRes, privacyPolicyRes] = await Promise.all([\n      getLatestPolicyApi('user_agreement'),\n      getLatestPolicyApi('privacy_policy')\n    ])\n    \n    if (userAgreementRes && userAgreementRes.code === 200 && userAgreementRes.data) {\n      policyVersions.value.userAgreement = userAgreementRes.data.version\n      console.log('用户协议版本:', userAgreementRes.data.version)\n    }\n    \n    if (privacyPolicyRes && privacyPolicyRes.code === 200 && privacyPolicyRes.data) {\n      policyVersions.value.privacyPolicy = privacyPolicyRes.data.version\n      console.log('隐私政策版本:', privacyPolicyRes.data.version)\n    }\n    \n    console.log('协议版本信息加载完成:', policyVersions.value)\n  } catch (error) {\n    console.error('加载协议版本信息失败:', error)\n    // 设置默认版本以防加载失败\n    policyVersions.value.userAgreement = '1.0.0'\n    policyVersions.value.privacyPolicy = '1.0.0'\n  }\n}\n\n// 上报协议同意记录\nconst reportPolicyAcceptance = async () => {\n  try {\n    console.log('=== 开始上报协议同意记录 ===')\n    console.log('当前协议版本信息:', policyVersions.value)\n    \n    // 如果协议版本信息为空，先尝试重新加载\n    if (!policyVersions.value.userAgreement || !policyVersions.value.privacyPolicy) {\n      console.log('协议版本信息不完整，尝试重新加载...')\n      await loadPolicyVersions()\n      console.log('重新加载后的协议版本信息:', policyVersions.value)\n    }\n    \n    const reports = []\n    \n    // 上报用户协议同意\n    if (policyVersions.value.userAgreement) {\n      console.log('准备上报用户协议，版本:', policyVersions.value.userAgreement)\n      reports.push(\n        acceptPolicyApi('user_agreement', policyVersions.value.userAgreement)\n      )\n    } else {\n      console.warn('用户协议版本为空，使用默认版本1.0.0')\n      reports.push(\n        acceptPolicyApi('user_agreement', '1.0.0')\n      )\n    }\n    \n    // 上报隐私政策同意\n    if (policyVersions.value.privacyPolicy) {\n      console.log('准备上报隐私政策，版本:', policyVersions.value.privacyPolicy)\n      reports.push(\n        acceptPolicyApi('privacy_policy', policyVersions.value.privacyPolicy)\n      )\n    } else {\n      console.warn('隐私政策版本为空，使用默认版本1.0.0')\n      reports.push(\n        acceptPolicyApi('privacy_policy', '1.0.0')\n      )\n    }\n    \n    if (reports.length === 0) {\n      console.error('无法创建上报请求')\n      return\n    }\n    \n    console.log(`准备并行执行${reports.length}个上报请求...`)\n    \n    // 并行执行上报\n    const results = await Promise.all(reports)\n    console.log('协议同意记录上报原始结果:', results)\n    \n    // 检查上报结果\n    let successCount = 0\n    results.forEach((result, index) => {\n      const type = index === 0 ? '用户协议' : '隐私政策'\n      console.log(`${type}上报结果:`, result)\n      \n      if (result && result.code === 200) {\n        successCount++\n        console.log(`${type}同意记录上报成功`)\n      } else {\n        console.error(`${type}同意记录上报失败:`, result)\n      }\n    })\n    \n    if (successCount === results.length) {\n      console.log('所有协议同意记录上报成功')\n    } else {\n      console.warn(`部分协议同意记录上报失败，成功数量: ${successCount}/${results.length}`)\n    }\n    \n  } catch (error) {\n    console.error('上报协议同意记录过程中发生异常:', error)\n    console.error('错误详情:', {\n      message: error.message,\n      stack: error.stack\n    })\n  }\n}\n\n// 生命周期钩子\nonLoad(() => {\n  // 页面加载时读取静态资源配置\n  try {\n    const assets = uni.getStorageSync('staticAssets')\n    loginBgUrl.value = assets?.['login-bg'] || ''\n    logoHdUrl.value = assets?.['logo-hd'] || ''\n  } catch (e) {}\n\n  // 页面加载时预加载协议版本信息\n  loadPolicyVersions()\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.login-page {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  min-height: 100vh;\n}\n\n.background-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -1;\n}\n\n\n\n.content-wrapper {\nflex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;; // 可以改回垂直居中\n  align-items: center;\n  padding: 0 60rpx;\n  position: relative;\n  padding-top: 200rpx;\n  z-index: 1;\n}\n\n.login-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%; // Ensure content takes full width within wrapper\n  max-width: 600rpx; // 限制最大宽度，避免在大屏幕上过宽\n}\n\n\n.action-section {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  /* 针对未选中时的圆圈样式 */\n    :deep(.up-checkbox__icon-wrap) {\n      width: 24rpx !important;\n      height: 24rpx !important;\n      border: 2rpx solid #9B9A9A !important;\n      \n      /* 关键: 组件默认可能会有一个背景色，我们把它设置为透明来显示出边框 */\n      background-color: transparent !important; \n    }\n  \n    /* 针对选中时的样式，确保边框和背景色都正确 \n      当选中时，uview会给 icon-wrap 加上 --checked 后缀的类\n    */\n    :deep(.up-checkbox__icon-wrap--checked) {\n      border-color: #023F98 !important; /* 选中时，边框颜色变为主题色 */\n      background-color: #023F98 !important; /* 选中时，背景色也变为主题色 */\n    }\n}\n\n.login-btn {\nwidth: 652rpx;\n  height: 76rpx;\n  background: #023F98;\n  border-radius: 8rpx;\n  color: #ffffff;\n  border: none;\n  line-height: 76rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  text-align: center;\n  margin: 0; // 清除默认的margin\n\n  &::after { // 移除按钮默认边框\n    border: none;\n  }\n}\n\n/* --- 自定义协议区域 --- */\n.agreement-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 32rpx; // 与上方按钮的间距\n}\n\n.custom-checkbox {\n  /* 这是未选中时的圆圈样式 */\n  width: 24rpx;\n  height: 24rpx;\n  border: 2rpx solid #9B9A9A;\n  border-radius: 50%; // 圆形\n  margin-right: 16rpx; // 与文字的间距\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s; // 添加一个过渡效果\n  position: relative; // 添加相对定位\n\n  /* 这是选中时的样式 */\n  &.is-checked {\n    border-color: #023F98; // 选中时边框变为主题色\n  }\n}\n\n.checkmark {\n  /* 这是里面的小圆点 - 修复对齐问题 */\n  width: 14rpx;\n  height: 14rpx;\n  background-color: #023F98; // 选中后的填充颜色\n  border-radius: 50%;\n  position: absolute; // 使用绝对定位确保居中\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%); // 完全居中\n}\n\n.agreement-text {\n  font-size: 24rpx;\n  color: #666666;\n\n  .link-text {\n    color: #023F98;\n    text-decoration: none;\n  }\n}\n\n.cancel-text {\n  font-size: 28rpx;\n  color: #999999;\n  margin-top: 40rpx;\n  cursor: pointer;\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "wxLoginApi", "getPhoneNumberApi", "e", "getLatestPolicyApi", "acceptPolicyApi", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;AA0EA,UAAM,qBAAqBA,cAAAA,IAAI,KAAK;AAGpC,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,YAAYA,cAAG,IAAC,EAAE;AAGxB,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB,eAAe;AAAA,MACf,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,kBAAkB,MAAM;AAC5B,yBAAmB,QAAQ,CAAC,mBAAmB;AAAA,IACjD;AAGA,UAAM,mBAAmB,OAAO,MAAM;AACpCC,oBAAAA,MAAY,MAAA,OAAA,yCAAA,uBAAuB;AACnCA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,WAAW,CAAC;AACxBA,gFAAY,aAAa,EAAE,MAAM;AACjCA,oBAAA,MAAA,MAAA,OAAA,yCAAY,kBAAkB,EAAE,OAAO,IAAI;AAG3C,UAAI,CAAC,EAAE,OAAO,MAAM;AAClBA,sBAAAA,6DAAY,aAAa;AACzBA,4BAAA,MAAA,OAAA,0CAAY,SAAS,EAAE,OAAO,MAAM;AACpCA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,gCAAgC;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,YAAM,YAAY,EAAE,OAAO;AAC3BA,oBAAAA,MAAY,MAAA,OAAA,0CAAA,gBAAgB,SAAS;AACrCA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAS,CAAE;AAEpC,UAAI;AAEFA,sBAAG,MAAC,kBAAkB,OAAO;AAC7BA,sBAAG,MAAC,kBAAkB,UAAU;AAGhCA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,kBAAkB;AAC9B,cAAM,WAAW,MAAMA,cAAG,MAAC,MAAO;AAClCA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,gBAAgB,QAAQ;AACpC,cAAM,YAAY,SAAS;AAC3BA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,gBAAgB,SAAS;AAGrC,cAAM,MAAM,MAAMC,cAAU,WAAC,SAAS;AACtCD,sBAAAA,6DAAY,WAAW,GAAG;AAG1B,cAAM,QAAQ,IAAI;AAClBA,4BAAI,eAAe,SAAS,KAAK;AACjCA,sBAAAA,MAAI,eAAe,YAAY,IAAI,QAAQ;AAG3CA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,gBAAgB,SAAS;AACrCA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,YAAY,KAAK;AAC7B,cAAM,WAAW,MAAME,cAAiB,kBAAC,SAAS;AAClDF,sBAAAA,MAAY,MAAA,OAAA,0CAAA,YAAY,QAAQ;AAGhCA,sBAAAA,6DAAY,eAAe;AAC3B,YAAI;AACF,gBAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9CA,wBAAAA,MAAA,MAAA,OAAA,0CAAY,eAAe,QAAQ;AAEnC,cAAI,CAAC,UAAU;AACbA,0BAAAA,MAAA,MAAA,OAAA,0CAAY,uBAAuB;AACnC,kBAAM,cAAc;AAAA,cAClB,aAAa,SAAS;AAAA,YACvB;AACDA,gCAAI,eAAe,YAAY,WAAW;AAAA,UAClD,OAAa;AACLA,0BAAAA,MAAY,MAAA,OAAA,0CAAA,kBAAkB;AAC9B,qBAAS,cAAc,SAAS;AAChCA,gCAAI,eAAe,YAAY,QAAQ;AAAA,UACxC;AAEDA,wBAAAA,6DAAY,UAAU;AAAA,QACvB,SAAQ,aAAa;AACpBA,wBAAAA,MAAA,MAAA,SAAA,0CAAc,cAAc,WAAW;AACvC,gBAAM;AAAA,QACP;AAEDA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,mBAAmB;AAC/BA,mFAAY,YAAYA,oBAAI,eAAe,OAAO,CAAC;AACnDA,4BAAA,MAAA,OAAA,0CAAY,eAAeA,oBAAI,eAAe,UAAU,CAAC;AAEzDA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,aAAa;AACzBA,sBAAAA,MAAI,YAAa;AAEjBA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,aAAa;AACzBA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAGhDA,sBAAAA,6DAAY,eAAe;AAC3B,YAAI;AACF,gBAAM,uBAAwB;AAAA,QAC/B,SAAQ,OAAO;AACdA,wBAAAA,MAAc,MAAA,SAAA,0CAAA,eAAe,KAAK;AAAA,QACnC;AAGDA,sBAAAA,6DAAY,cAAc;AAC1B,mBAAW,MAAM;AACfA,wBAAAA,6DAAY,kBAAkB;AAG9B,cAAI;AACF,kBAAM,gBAAgBA,cAAAA,MAAI,eAAe,eAAe;AACxD,gBAAI,eAAe;AACjBA,4BAAA,MAAA,MAAA,OAAA,0CAAY,eAAe,aAAa;AAExCA,4BAAAA,MAAI,aAAa;AAAA,gBACf,SAAS,MAAM;AACbA,gCAAAA,MAAA,MAAA,OAAA,0CAAY,SAAS;AAAA,gBACtB;AAAA,gBACD,MAAM,CAAC,QAAQ;AACbA,gCAAc,MAAA,MAAA,SAAA,0CAAA,wBAAwB,GAAG;AAEzC,sBAAI,cAAc,WAAW,SAAS,GAAG;AACvCA,kCAAAA,MAAI,WAAW;AAAA,sBACb,KAAK;AAAA,sBACL,MAAM,MAAM;AAEVA,sCAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,sBAC5C;AAAA,oBACnB,CAAiB;AAAA,kBACF;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACX,OAAe;AAELA,4BAAAA,MAAI,aAAa;AAAA,gBACf,SAAS,MAAM;AACbA,gCAAAA,MAAA,MAAA,OAAA,0CAAY,SAAS;AAAA,gBACtB;AAAA,gBACD,MAAM,CAAC,QAAQ;AACbA,gCAAc,MAAA,MAAA,SAAA,0CAAA,kBAAkB,GAAG;AAEnCA,gCAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,gBAC5C;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACF,SAAQG,IAAG;AACVH,0BAAA,MAAA,MAAA,QAAA,0CAAa,eAAeG,EAAC;AAE7BH,0BAAAA,MAAI,aAAa;AAAA,cACf,MAAM,CAAC,QAAQ;AACbA,8BAAA,MAAA,MAAA,SAAA,0CAAc,kBAAkB,GAAG;AACnCA,8BAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,cAC5C;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACF,GAAE,GAAI;AAEPA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,uBAAuB;AAAA,MAEpC,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,cAAc,KAAK;AACjCA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,SAAS;AAAA,UACrB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,UACb,MAAM,MAAM;AAAA,QAClB,CAAK;AAEDA,sBAAAA,MAAI,YAAa;AAEjB,cAAM,eAAe,MAAM,WAAW;AACtCA,sBAAAA,MAAc,MAAA,SAAA,0CAAA,WAAW,YAAY;AAErCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAG7B,UAAI,CAAC,mBAAmB,OAAO;AAC7BA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,SAAS;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAA,MAAA,OAAA,0CAAY,UAAU;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa;AAAA,QAC1B;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAAA,+DAAc,eAAe,GAAG;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAA,MAAA,OAAA,0CAAY,UAAU;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,0CAAY,aAAa;AAAA,QAC1B;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAAA,+DAAc,eAAe,GAAG;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAOA,UAAM,qBAAqB,YAAY;AACrC,UAAI;AACFA,sBAAAA,6DAAY,eAAe;AAG3B,cAAM,CAAC,kBAAkB,gBAAgB,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC7DI,sCAAAA,mBAAmB,gBAAgB;AAAA,UACnCA,sCAAAA,mBAAmB,gBAAgB;AAAA,QACzC,CAAK;AAED,YAAI,oBAAoB,iBAAiB,SAAS,OAAO,iBAAiB,MAAM;AAC9E,yBAAe,MAAM,gBAAgB,iBAAiB,KAAK;AAC3DJ,wBAAY,MAAA,MAAA,OAAA,0CAAA,WAAW,iBAAiB,KAAK,OAAO;AAAA,QACrD;AAED,YAAI,oBAAoB,iBAAiB,SAAS,OAAO,iBAAiB,MAAM;AAC9E,yBAAe,MAAM,gBAAgB,iBAAiB,KAAK;AAC3DA,wBAAY,MAAA,MAAA,OAAA,0CAAA,WAAW,iBAAiB,KAAK,OAAO;AAAA,QACrD;AAEDA,sBAAY,MAAA,MAAA,OAAA,0CAAA,eAAe,eAAe,KAAK;AAAA,MAChD,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,0CAAA,eAAe,KAAK;AAElC,uBAAe,MAAM,gBAAgB;AACrC,uBAAe,MAAM,gBAAgB;AAAA,MACtC;AAAA,IACH;AAGA,UAAM,yBAAyB,YAAY;AACzC,UAAI;AACFA,sBAAAA,MAAY,MAAA,OAAA,0CAAA,oBAAoB;AAChCA,sBAAY,MAAA,MAAA,OAAA,0CAAA,aAAa,eAAe,KAAK;AAG7C,YAAI,CAAC,eAAe,MAAM,iBAAiB,CAAC,eAAe,MAAM,eAAe;AAC9EA,wBAAAA,MAAY,MAAA,OAAA,0CAAA,qBAAqB;AACjC,gBAAM,mBAAoB;AAC1BA,wBAAA,MAAA,MAAA,OAAA,0CAAY,iBAAiB,eAAe,KAAK;AAAA,QAClD;AAED,cAAM,UAAU,CAAE;AAGlB,YAAI,eAAe,MAAM,eAAe;AACtCA,wBAAY,MAAA,MAAA,OAAA,0CAAA,gBAAgB,eAAe,MAAM,aAAa;AAC9D,kBAAQ;AAAA,YACNK,sCAAAA,gBAAgB,kBAAkB,eAAe,MAAM,aAAa;AAAA,UACrE;AAAA,QACP,OAAW;AACLL,wBAAAA,MAAA,MAAA,QAAA,0CAAa,sBAAsB;AACnC,kBAAQ;AAAA,YACNK,sCAAe,gBAAC,kBAAkB,OAAO;AAAA,UAC1C;AAAA,QACF;AAGD,YAAI,eAAe,MAAM,eAAe;AACtCL,wBAAY,MAAA,MAAA,OAAA,0CAAA,gBAAgB,eAAe,MAAM,aAAa;AAC9D,kBAAQ;AAAA,YACNK,sCAAAA,gBAAgB,kBAAkB,eAAe,MAAM,aAAa;AAAA,UACrE;AAAA,QACP,OAAW;AACLL,wBAAAA,MAAA,MAAA,QAAA,0CAAa,sBAAsB;AACnC,kBAAQ;AAAA,YACNK,sCAAe,gBAAC,kBAAkB,OAAO;AAAA,UAC1C;AAAA,QACF;AAED,YAAI,QAAQ,WAAW,GAAG;AACxBL,wBAAAA,+DAAc,UAAU;AACxB;AAAA,QACD;AAEDA,4BAAY,MAAA,OAAA,0CAAA,SAAS,QAAQ,MAAM,UAAU;AAG7C,cAAM,UAAU,MAAM,QAAQ,IAAI,OAAO;AACzCA,sBAAAA,MAAA,MAAA,OAAA,0CAAY,iBAAiB,OAAO;AAGpC,YAAI,eAAe;AACnB,gBAAQ,QAAQ,CAAC,QAAQ,UAAU;AACjC,gBAAM,OAAO,UAAU,IAAI,SAAS;AACpCA,8BAAA,MAAA,OAAA,0CAAY,GAAG,IAAI,SAAS,MAAM;AAElC,cAAI,UAAU,OAAO,SAAS,KAAK;AACjC;AACAA,0BAAA,MAAA,MAAA,OAAA,0CAAY,GAAG,IAAI,UAAU;AAAA,UACrC,OAAa;AACLA,gCAAc,MAAA,SAAA,0CAAA,GAAG,IAAI,aAAa,MAAM;AAAA,UACzC;AAAA,QACP,CAAK;AAED,YAAI,iBAAiB,QAAQ,QAAQ;AACnCA,wBAAAA,MAAY,MAAA,OAAA,0CAAA,cAAc;AAAA,QAChC,OAAW;AACLA,wBAAAA,MAAA,MAAA,QAAA,0CAAa,sBAAsB,YAAY,IAAI,QAAQ,MAAM,EAAE;AAAA,QACpE;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,oBAAoB,KAAK;AACvCA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,SAAS;AAAA,UACrB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACF;AAAA,IACH;AAGAM,kBAAAA,OAAO,MAAM;AAEX,UAAI;AACF,cAAM,SAASN,cAAAA,MAAI,eAAe,cAAc;AAChD,mBAAW,SAAQ,iCAAS,gBAAe;AAC3C,kBAAU,SAAQ,iCAAS,eAAc;AAAA,MAC7C,SAAW,GAAG;AAAA,MAAE;AAGd,yBAAoB;AAAA,IACtB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpbD,GAAG,WAAW,eAAe;"}