{"version": 3, "file": "calc.js", "sources": ["uni_modules/uview-plus/libs/function/calc.js"], "sourcesContent": ["// 浮点数加法\r\nexport function add (arg1, arg2) {\r\n\tvar r1, r2, m\r\n\t\ttry {\r\n\t\t\tr1 = arg1.toString().split('.')[1].length\r\n\t\t} catch (e) {\r\n\t\t\tr1 = 0\r\n\t\t}\r\n\t\ttry {\r\n\t\t\tr2 = arg2.toString().split('.')[1].length\r\n\t\t} catch (e) {\r\n\t\t\tr2 = 0\r\n\t\t}\r\n\t\tm = Math.pow(10, Math.max(r1, r2))\r\n\treturn (arg1 * m + arg2 * m) / m\r\n}\r\n// 浮点数减法\r\nexport function sub (arg1, arg2) {\r\n\tvar r1, r2, m, n\r\n\t\ttry {\r\n\t\t  r1 = arg1.toString().split('.')[1].length\r\n\t\t} catch (e) {\r\n\t\t  r1 = 0\r\n\t\t}\r\n\t\ttry {\r\n\t\t  r2 = arg2.toString().split('.')[1].length\r\n\t\t} catch (e) {\r\n\t\t  r2 = 0\r\n\t\t}\r\n\t\tm = Math.pow(10, Math.max(r1, r2))\r\n\t\tn = (r1 >= r2) ? r1 : r2\r\n\treturn Math.abs(((arg1 * m - arg2 * m) / m).toFixed(n))\r\n}\r\n//浮点乘法\r\nexport function mul (a, b) {\r\n\tvar c = 0,\r\n\t\td = a.toString(),\r\n\t\te = b.toString();\r\n\ttry {\r\n\t\tc += d.split(\".\")[1].length;\r\n\t} catch (f) {}\r\n\ttry {\r\n\t\tc += e.split(\".\")[1].length;\r\n\t} catch (f) {}\r\n\treturn Number(d.replace(\".\", \"\")) * Number(e.replace(\".\", \"\")) / Math.pow(10, c);\r\n}\r\n//浮点除法\r\nexport function div (a, b) {\r\n\tvar c, d, e = 0,\r\n\t\tf = 0;\r\n\ttry {\r\n\t\te = a.toString().split(\".\")[1].length;\r\n\t} catch (g) {}\r\n\ttry {\r\n\t\tf = b.toString().split(\".\")[1].length;\r\n\t} catch (g) {}\r\n\treturn c = Number(a.toString().replace(\".\", \"\")), d = Number(b.toString().replace(\".\", \"\")), xyutil.mul(c / d, Math.pow(10, f - e));\r\n}\r\nexport default {\r\n\tadd,\r\n\tsub,\r\n\tmul,\r\n\tdiv\r\n}\r\n"], "names": [], "mappings": ";AACO,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI;AACX,MAAI;AACH,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACnC,SAAQ,GAAG;AACX,SAAK;AAAA,EACL;AACD,MAAI;AACH,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACnC,SAAQ,GAAG;AACX,SAAK;AAAA,EACL;AACD,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAClC,UAAQ,OAAO,IAAI,OAAO,KAAK;AAChC;AAEO,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI,GAAG;AACd,MAAI;AACF,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAQ,GAAG;AACV,SAAK;AAAA,EACN;AACD,MAAI;AACF,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAQ,GAAG;AACV,SAAK;AAAA,EACN;AACD,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACjC,MAAK,MAAM,KAAM,KAAK;AACvB,SAAO,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvD;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,IAAI,GACP,IAAI,EAAE,SAAU,GAChB,IAAI,EAAE;AACP,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACvB,SAAU,GAAG;AAAA,EAAE;AACd,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACvB,SAAU,GAAG;AAAA,EAAE;AACd,SAAO,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;AAChF;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,GAAG,GAAG,IAAI,GACb,IAAI;AACL,MAAI;AACH,QAAI,EAAE,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACjC,SAAU,GAAG;AAAA,EAAE;AACd,MAAI;AACH,QAAI,EAAE,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACjC,SAAU,GAAG;AAAA,EAAE;AACd,SAAO,IAAI,OAAO,EAAE,SAAU,EAAC,QAAQ,KAAK,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE,SAAU,EAAC,QAAQ,KAAK,EAAE,CAAC,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;AACnI;AACA,MAAe,OAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;"}