{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-empty/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 内置图标名称，或图片路径，建议绝对路径\r\n        icon: {\r\n            type: String,\r\n            default: () => defProps.empty.icon\r\n        },\r\n        // 提示文字\r\n        text: {\r\n            type: String,\r\n            default: () => defProps.empty.text\r\n        },\r\n        // 文字颜色\r\n        textColor: {\r\n            type: String,\r\n            default: () => defProps.empty.textColor\r\n        },\r\n        // 文字大小\r\n        textSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.empty.textSize\r\n        },\r\n        // 图标的颜色\r\n        iconColor: {\r\n            type: String,\r\n            default: () => defProps.empty.iconColor\r\n        },\r\n        // 图标的大小\r\n        iconSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.empty.iconSize\r\n        },\r\n        // 选择预置的图标类型\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.empty.mode\r\n        },\r\n        //  图标宽度，单位px\r\n        width: {\r\n            type: [String, Number],\r\n            default: () => defProps.empty.width\r\n        },\r\n        // 图标高度，单位px\r\n        height: {\r\n            type: [String, Number],\r\n            default: () => defProps.empty.height\r\n        },\r\n        // 是否显示组件\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.empty.show\r\n        },\r\n        // 组件距离上一个元素之间的距离，默认px单位\r\n        marginTop: {\r\n            type: [String, Number],\r\n            default: () => defProps.empty.marginTop\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}