{"version": 3, "file": "checkboxGroup.js", "sources": ["uni_modules/uview-plus/components/u-checkbox-group/checkboxGroup.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:54:47\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkboxGroup.js\r\n */\r\nexport default {\r\n    // checkbox-group组件\r\n    checkboxGroup: {\r\n        name: '',\r\n        value: [],\r\n        shape: 'square',\r\n        disabled: false,\r\n        activeColor: '#2979ff',\r\n        inactiveColor: '#c8c9cc',\r\n        size: 18,\r\n        placement: 'row',\r\n        labelSize: 14,\r\n        labelColor: '#303133',\r\n        labelDisabled: false,\r\n        iconColor: '#ffffff',\r\n        iconSize: 12,\r\n        iconPlacement: 'left',\r\n        borderBottom: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,gBAAA;AAAA;AAAA,EAEX,eAAe;AAAA,IACX,MAAM;AAAA,IACN,OAAO,CAAE;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,EACjB;AACL;;"}