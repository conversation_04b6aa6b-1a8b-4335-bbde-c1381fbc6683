{"version": 3, "file": "tabbarItem.js", "sources": ["uni_modules/uview-plus/components/u-tabbar-item/tabbarItem.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:22:55\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbarItem.js\r\n */\r\nexport default {\r\n    //\r\n    tabbarItem: {\r\n        name: null,\r\n        icon: '',\r\n        badge: null,\r\n        dot: false,\r\n        text: '',\r\n        badgeStyle: 'top: 6px;right:2px;'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,EACf;AACL;;"}