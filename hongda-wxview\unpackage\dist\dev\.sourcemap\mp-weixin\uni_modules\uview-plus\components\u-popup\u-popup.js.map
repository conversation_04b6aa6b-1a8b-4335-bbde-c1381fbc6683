{"version": 3, "file": "u-popup.js", "sources": ["uni_modules/uview-plus/components/u-popup/u-popup.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBvcHVwL3UtcG9wdXAudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"u-popup\" :class=\"[customClass]\"\r\n\t\t:style=\"{width: show == false ? '0px' : '',\r\n\t\t\theight: show == false ? '0px' : ''}\">\r\n\t\t<view class=\"u-popup__trigger\">\r\n\t\t\t<slot name=\"trigger\">\r\n\t\t\t</slot>\r\n\t\t\t<view @click=\"open\"\r\n\t\t\t\tclass=\"u-popup__trigger__cover\"></view>\r\n\t\t</view>\r\n\t\t<u-overlay\r\n\t\t\t:show=\"show\"\r\n\t\t\t@click=\"overlayClick\"\r\n\t\t\tv-if=\"overlay\"\r\n\t\t\t:zIndex=\"zIndex\"\r\n\t\t\t:duration=\"overlayDuration\"\r\n\t\t\t:customStyle=\"overlayStyle\"\r\n\t\t\t:opacity=\"overlayOpacity\"\r\n\t\t></u-overlay>\r\n\t\t<u-transition\r\n\t\t\t:show=\"show\"\r\n\t\t\t:customStyle=\"transitionStyle\"\r\n\t\t\t:mode=\"position\"\r\n\t\t\t:duration=\"duration\"\r\n\t\t\t@afterEnter=\"afterEnter\"\r\n\t\t\t@click=\"clickHandler\"\r\n\t\t>\r\n\t\t\t<!-- @click.stop不能去除，去除会导致居中模式下点击内容区域触发关闭弹窗 -->\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-popup__content\"\r\n\t\t\t\t:style=\"[contentStyle]\"\r\n\t\t\t\************=\"noop\"\r\n\t\t\t\************************=\"noop\"\r\n\t\t\t>\r\n\t\t\t\t<u-status-bar v-if=\"safeAreaInsetTop\"></u-status-bar>\r\n\t\t\t\t<slot></slot>\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-if=\"closeable\"\r\n\t\t\t\t\**********=\"close\"\r\n\t\t\t\t\tclass=\"u-popup__content__close\"\r\n\t\t\t\t\t:class=\"['u-popup__content__close--' + closeIconPos]\"\r\n\t\t\t\t\thover-class=\"u-popup__content__close--hover\"\r\n\t\t\t\t\thover-stay-time=\"150\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\tname=\"close\"\r\n\t\t\t\t\t\tcolor=\"#909399\"\r\n\t\t\t\t\t\tsize=\"18\"\r\n\t\t\t\t\t\tbold\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-safe-bottom v-if=\"safeAreaInsetBottom\"></u-safe-bottom>\r\n\t\t\t</view>\r\n\t\t\t<slot name=\"bottom\"></slot>\r\n\t\t</u-transition>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle, deepMerge, sleep, getWindowInfo } from '../../libs/function/index';\r\n\t/**\r\n\t * popup 弹窗\r\n\t * @description 弹出层容器，用于展示弹窗、信息提示等内容，支持上、下、左、右和中部弹出。组件只提供容器，内部内容由用户自定义\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/popup.html\r\n\t * @property {Boolean}\t\t\tshow\t\t\t\t是否展示弹窗 (默认 false )\r\n\t * @property {Boolean}\t\t\toverlay\t\t\t\t是否显示遮罩 （默认 true ）\r\n\t * @property {String}\t\t\tmode\t\t\t\t弹出方向（默认 'bottom' ）\r\n\t * @property {String | Number}\tduration\t\t\t动画时长，单位ms （默认 300 ）\r\n\t * @property {String | Number}\toverlayDuration\t\t遮罩层动画时长，单位ms （默认 350 ）\r\n\t * @property {Boolean}\t\t\tcloseable\t\t\t是否显示关闭图标（默认 false ）\r\n\t * @property {Object | String}\toverlayStyle\t\t自定义遮罩的样式\r\n\t * @property {String | Number}\toverlayOpacity\t\t遮罩透明度，0-1之间（默认 0.5）\r\n\t * @property {Boolean}\t\t\tcloseOnClickOverlay\t点击遮罩是否关闭弹窗 （默认  true ）\r\n\t * @property {String | Number}\tzIndex\t\t\t\t层级 （默认 10075 ）\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetBottom\t是否为iPhoneX留出底部安全距离 （默认 true ）\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否留出顶部安全距离（状态栏高度） （默认 false ）\r\n\t * @property {String}\t\t\tcloseIconPos\t\t自定义关闭图标位置（默认 'top-right' ）\r\n\t * @property {String | Number}\tround\t\t\t\t圆角值（默认 0）\r\n\t * @property {String }\t        bgColor\t\t\t\t背景色值（默认 '' ）\r\n\t * @property {Boolean}\t\t\tzoom\t\t\t\t当mode=center时 是否开启缩放（默认 true ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\r\n\t * @pages_event {Function} open 弹出层打开\r\n\t * @pages_event {Function} close 弹出层收起\r\n\t * @example <u-popup v-model:show=\"show\"><text>出淤泥而不染，濯清涟而不妖</text></u-popup>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-popup',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\toverlayDuration: this.duration + 50\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshow(newValue, oldValue) {\r\n\t\t\t\tif (newValue === true) {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tconst children = this.$children\r\n\t\t\t\t\tthis.retryComputedComponentRect(children)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttransitionStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t}\r\n\t\t\t\tstyle[this.mode] = 0\r\n\t\t\t\tif (this.mode === 'left') {\r\n\t\t\t\t\treturn deepMerge(style, {\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'right') {\r\n\t\t\t\t\treturn deepMerge(style, {\r\n\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'top') {\r\n\t\t\t\t\treturn deepMerge(style, {\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'bottom') {\r\n\t\t\t\t\treturn deepMerge(style, {\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (this.mode === 'center') {\r\n\t\t\t\t\treturn deepMerge(style, {\r\n\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t'justify-content': 'center',\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tbottom: 0\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcontentStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\t// 通过设备信息的safeAreaInsets值来判断是否需要预留顶部状态栏和底部安全局的位置\r\n\t\t\t\t// 不使用css方案，是因为nvue不支持css的iPhoneX安全区查询属性\r\n\t\t\t\tconst {\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = getWindowInfo()\r\n\t\t\t\tif (this.mode !== 'center') {\r\n\t\t\t\t\tstyle.flex = 1\r\n\t\t\t\t}\r\n\t\t\t\t// 背景色，一般用于设置为transparent，去除默认的白色背景\r\n\t\t\t\tif (this.bgColor) {\r\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\t}\r\n\t\t\t\tif(this.round) {\r\n\t\t\t\t\tconst value = addUnit(this.round)\r\n\t\t\t\t\tif(this.mode === 'top') {\r\n\t\t\t\t\t\tstyle.borderBottomLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderBottomRightRadius = value\r\n\t\t\t\t\t} else if(this.mode === 'bottom') {\r\n\t\t\t\t\t\tstyle.borderTopLeftRadius = value\r\n\t\t\t\t\t\tstyle.borderTopRightRadius = value\r\n\t\t\t\t\t} else if(this.mode === 'center') {\r\n\t\t\t\t\t\tstyle.borderRadius = value\r\n\t\t\t\t\t} \r\n\t\t\t\t}\r\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\r\n\t\t\t},\r\n\t\t\tposition() {\r\n\t\t\t\tif (this.mode === 'center') {\r\n\t\t\t\t\treturn this.zoom ? 'fade-zoom' : 'fade'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'left') {\r\n\t\t\t\t\treturn 'slide-left'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'right') {\r\n\t\t\t\t\treturn 'slide-right'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'bottom') {\r\n\t\t\t\t\treturn 'slide-up'\r\n\t\t\t\t}\r\n\t\t\t\tif (this.mode === 'top') {\r\n\t\t\t\t\treturn 'slide-down'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\temits: [\"open\", \"close\", \"click\", \"update:show\"],\r\n\t\tmethods: {\r\n\t\t\t// 点击遮罩\r\n\t\t\toverlayClick() {\r\n\t\t\t\tif (this.closeOnClickOverlay) {\r\n\t\t\t\t\tthis.$emit('update:show', false)\r\n\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topen(e) {\r\n\t\t\t\tthis.$emit('update:show', true)\r\n\t\t\t},\r\n\t\t\tclose(e) {\r\n\t\t\t\tthis.$emit('update:show', false)\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t},\r\n\t\t\tafterEnter() {\r\n\t\t\t\tthis.$emit('open')\r\n\t\t\t},\r\n\t\t\tclickHandler() {\r\n\t\t\t\t// 由于中部弹出时，其u-transition占据了整个页面相当于遮罩，此时需要发出遮罩点击事件，是否无法通过点击遮罩关闭弹窗\r\n\t\t\t\tif(this.mode === 'center') {\r\n\t\t\t\t\tthis.overlayClick()\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tretryComputedComponentRect(children) {\r\n\t\t\t\t// 组件内部需要计算节点的组件\r\n\t\t\t\tconst names = ['u-calendar-month', 'u-album', 'u-collapse-item', 'u-dropdown', 'u-index-item', 'u-index-list',\r\n\t\t\t\t\t'u-line-progress', 'u-list-item', 'u-rate', 'u-read-more', 'u-row', 'u-row-notice', 'u-scroll-list',\r\n\t\t\t\t\t'u-skeleton', 'u-slider', 'u-steps-item', 'u-sticky', 'u-subsection', 'u-swipe-action-item', 'u-tabbar',\r\n\t\t\t\t\t'u-tabs', 'u-tooltip'\r\n\t\t\t\t]\r\n\t\t\t\t// 历遍所有的子组件节点\r\n\t\t\t\tfor (let i = 0; i < children.length; i++) {\r\n\t\t\t\t\tconst child = children[i]\r\n\t\t\t\t\t// 拿到子组件的子组件\r\n\t\t\t\t\tconst grandChild = child.$children\r\n\t\t\t\t\t// 判断如果在需要重新初始化的组件数组中名中，并且存在init方法的话，则执行\r\n\t\t\t\t\tif (names.includes(child.$options.name) && typeof child?.init === 'function') {\r\n\t\t\t\t\t\t// 需要进行一定的延时，因为初始化页面需要时间\r\n\t\t\t\t\t\tsleep(50).then(() => {\r\n\t\t\t\t\t\t\tchild.init()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果子组件还有孙组件，进行递归历遍\r\n\t\t\t\t\tif (grandChild.length) {\r\n\t\t\t\t\t\tthis.retryComputedComponentRect(grandChild)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$u-popup-flex:1 !default;\r\n\t$u-popup-content-background-color: #fff !default;\r\n\r\n\t.u-popup {\r\n\t\tflex: $u-popup-flex;\r\n\t\t\r\n\t\t&__trigger {\r\n\t\t\tposition: relative;\r\n\t\t\t&__cover {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\tbackground-color: $u-popup-content-background-color;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&--round-top {\r\n\t\t\t\tborder-top-left-radius: 0;\r\n\t\t\t\tborder-top-right-radius: 0;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-left {\r\n\t\t\t\tborder-top-left-radius: 0;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-right {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 0;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-bottom {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\t\tborder-bottom-right-radius: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&--round-center {\r\n\t\t\t\tborder-top-left-radius: 10px;\r\n\t\t\t\tborder-top-right-radius: 10px;\r\n\t\t\t\tborder-bottom-left-radius: 10px;\r\n\t\t\t\tborder-bottom-right-radius: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close {\r\n\t\t\t\tposition: absolute;\r\n\r\n\t\t\t\t&--hover {\r\n\t\t\t\t\topacity: 0.4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__close--top-left {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--top-right {\r\n\t\t\t\ttop: 15px;\r\n\t\t\t\tright: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--bottom-left {\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t\tleft: 15px;\r\n\t\t\t}\r\n\r\n\t\t\t&__close--bottom-right {\r\n\t\t\t\tright: 15px;\r\n\t\t\t\tbottom: 15px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-popup/u-popup.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "getWindowInfo", "addUnit", "addStyle", "sleep"], "mappings": ";;;;;;AAwFC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,mDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,iBAAiB,KAAK,WAAW;AAAA,IAClC;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,KAAK,UAAU,UAAU;AACxB,UAAI,aAAa,MAAM;AAEtB,cAAM,WAAW,KAAK;AACtB,aAAK,2BAA2B,QAAQ;AAAA,MAEzC;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,kBAAkB;AACjB,YAAM,QAAQ;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,UAAU;AAAA,QACV,SAAS;AAAA,MACV;AACA,YAAM,KAAK,IAAI,IAAI;AACnB,UAAI,KAAK,SAAS,QAAQ;AACzB,eAAOC,0CAAAA,UAAU,OAAO;AAAA,UACvB,QAAQ;AAAA,UACR,KAAK;AAAA,SACL;AAAA,iBACS,KAAK,SAAS,SAAS;AACjC,eAAOA,0CAAAA,UAAU,OAAO;AAAA,UACvB,QAAQ;AAAA,UACR,KAAK;AAAA,SACL;AAAA,iBACS,KAAK,SAAS,OAAO;AAC/B,eAAOA,0CAAAA,UAAU,OAAO;AAAA,UACvB,MAAM;AAAA,UACN,OAAO;AAAA,SACP;AAAA,iBACS,KAAK,SAAS,UAAU;AAClC,eAAOA,0CAAAA,UAAU,OAAO;AAAA,UACvB,MAAM;AAAA,UACN,OAAO;AAAA,SACP;AAAA,iBACS,KAAK,SAAS,UAAU;AAClC,eAAOA,0CAAAA,UAAU,OAAO;AAAA,UACvB,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,SACR;AAAA,MACF;AAAA,IACA;AAAA,IACD,eAAe;AACd,YAAM,QAAQ,CAAC;AAKXC,8DAAc;AAClB,UAAI,KAAK,SAAS,UAAU;AAC3B,cAAM,OAAO;AAAA,MACd;AAEA,UAAI,KAAK,SAAS;AACjB,cAAM,kBAAkB,KAAK;AAAA,MAC9B;AACA,UAAG,KAAK,OAAO;AACd,cAAM,QAAQC,0CAAAA,QAAQ,KAAK,KAAK;AAChC,YAAG,KAAK,SAAS,OAAO;AACvB,gBAAM,yBAAyB;AAC/B,gBAAM,0BAA0B;AAAA,mBACvB,KAAK,SAAS,UAAU;AACjC,gBAAM,sBAAsB;AAC5B,gBAAM,uBAAuB;AAAA,mBACpB,KAAK,SAAS,UAAU;AACjC,gBAAM,eAAe;AAAA,QACtB;AAAA,MACD;AACA,aAAOF,0CAAS,UAAC,OAAOG,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IAClD;AAAA,IACD,WAAW;AACV,UAAI,KAAK,SAAS,UAAU;AAC3B,eAAO,KAAK,OAAO,cAAc;AAAA,MAClC;AACA,UAAI,KAAK,SAAS,QAAQ;AACzB,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,SAAS;AAC1B,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,UAAU;AAC3B,eAAO;AAAA,MACR;AACA,UAAI,KAAK,SAAS,OAAO;AACxB,eAAO;AAAA,MACR;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO,CAAC,QAAQ,SAAS,SAAS,aAAa;AAAA,EAC/C,SAAS;AAAA;AAAA,IAER,eAAe;AACd,UAAI,KAAK,qBAAqB;AAC7B,aAAK,MAAM,eAAe,KAAK;AAC/B,aAAK,MAAM,OAAO;AAAA,MACnB;AAAA,IACA;AAAA,IACD,KAAK,GAAG;AACP,WAAK,MAAM,eAAe,IAAI;AAAA,IAC9B;AAAA,IACD,MAAM,GAAG;AACR,WAAK,MAAM,eAAe,KAAK;AAC/B,WAAK,MAAM,OAAO;AAAA,IAClB;AAAA,IACD,aAAa;AACZ,WAAK,MAAM,MAAM;AAAA,IACjB;AAAA,IACD,eAAe;AAEd,UAAG,KAAK,SAAS,UAAU;AAC1B,aAAK,aAAa;AAAA,MACnB;AACA,WAAK,MAAM,OAAO;AAAA,IAClB;AAAA,IAED,2BAA2B,UAAU;AAEpC,YAAM,QAAQ;AAAA,QAAC;AAAA,QAAoB;AAAA,QAAW;AAAA,QAAmB;AAAA,QAAc;AAAA,QAAgB;AAAA,QAC9F;AAAA,QAAmB;AAAA,QAAe;AAAA,QAAU;AAAA,QAAe;AAAA,QAAS;AAAA,QAAgB;AAAA,QACpF;AAAA,QAAc;AAAA,QAAY;AAAA,QAAgB;AAAA,QAAY;AAAA,QAAgB;AAAA,QAAuB;AAAA,QAC7F;AAAA,QAAU;AAAA,MACX;AAEA,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAM,QAAQ,SAAS,CAAC;AAExB,cAAM,aAAa,MAAM;AAEzB,YAAI,MAAM,SAAS,MAAM,SAAS,IAAI,KAAK,QAAO,+BAAO,UAAS,YAAY;AAE7EC,0DAAM,EAAE,EAAE,KAAK,MAAM;AACpB,kBAAM,KAAK;AAAA,WACX;AAAA,QACF;AAEA,YAAI,WAAW,QAAQ;AACtB,eAAK,2BAA2B,UAAU;AAAA,QAC3C;AAAA,MACD;AAAA,IACD;AAAA,EAED;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpPD,GAAG,gBAAgB,SAAS;"}