{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-loadmore/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 组件状态，loadmore-加载前的状态，loading-加载中的状态，nomore-没有更多的状态\r\n        status: {\r\n            type: String,\r\n            default: () => defProps.loadmore.status\r\n        },\r\n        // 组件背景色\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.loadmore.bgColor\r\n        },\r\n        // 是否显示加载中的图标\r\n        icon: {\r\n            type: Boolean,\r\n            default: () => defProps.loadmore.icon\r\n        },\r\n        // 字体大小\r\n        fontSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadmore.fontSize\r\n        },\r\n\t\t    // 图标大小\r\n        iconSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadmore.iconSize\r\n        },\r\n        // 字体颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.loadmore.color\r\n        },\r\n        // 加载中状态的图标，spinner-花朵状图标，circle-圆圈状，semicircle-半圆\r\n        loadingIcon: {\r\n            type: String,\r\n            default: () => defProps.loadmore.loadingIcon\r\n        },\r\n        // 加载前的提示语\r\n        loadmoreText: {\r\n            type: String,\r\n            default: () => defProps.loadmore.loadmoreText\r\n        },\r\n        // 加载中提示语\r\n        loadingText: {\r\n            type: String,\r\n            default: () => defProps.loadmore.loadingText\r\n        },\r\n        // 没有更多的提示语\r\n        nomoreText: {\r\n            type: String,\r\n            default: () => defProps.loadmore.nomoreText\r\n        },\r\n        // 在“没有更多”状态下，是否显示粗点\r\n        isDot: {\r\n            type: Boolean,\r\n            default: () => defProps.loadmore.isDot\r\n        },\r\n        // 加载中图标的颜色\r\n        iconColor: {\r\n            type: String,\r\n            default: () => defProps.loadmore.iconColor\r\n        },\r\n        // 上边距\r\n        marginTop: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadmore.marginTop\r\n        },\r\n        // 下边距\r\n        marginBottom: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadmore.marginBottom\r\n        },\r\n        // 高度，单位px\r\n        height: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadmore.height\r\n        },\r\n        // 是否显示左边分割线\r\n        line: {\r\n            type: Boolean,\r\n            default: () => defProps.loadmore.line\r\n        },\r\n        // 线条颜色\r\n        lineColor: {\r\n            type: String,\r\n            default: () => defProps.loadmore.lineColor\r\n        },\r\n        // 是否虚线，true-虚线，false-实线\r\n        dashed: {\r\n            type: Boolean,\r\n            default: () => defProps.loadmore.dashed\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}