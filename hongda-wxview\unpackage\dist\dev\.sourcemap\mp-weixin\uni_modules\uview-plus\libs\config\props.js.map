{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/libs/config/props.js"], "sourcesContent": ["/**\r\n * 此文件的作用为统一配置所有组件的props参数\r\n * 借此用户可以全局覆盖组件的props默认值\r\n * 无需在每个引入组件的页面中都配置一次\r\n */\r\nimport config from './config'\r\n// 各个需要fixed的地方的z-index配置文件\r\nimport zIndex from './zIndex.js'\r\n// 关于颜色的配置，特殊场景使用\r\nimport color from './color.js'\r\n// http\r\nimport http from '../function/http.js'\r\nimport { shallowMerge } from '../function/index.js'\r\n// 组件props\r\nimport ActionSheet from '../../components/u-action-sheet/actionSheet'\r\nimport Album from '../../components/u-album/album'\r\nimport Alert from '../../components/u-alert/alert'\r\nimport Avatar from '../../components/u-avatar/avatar'\r\nimport AvatarGroup from '../../components/u-avatar-group/avatarGroup'\r\nimport Backtop from '../../components/u-back-top/backtop'\r\nimport Badge from '../../components/u-badge/badge'\r\nimport Button from '../../components/u-button/button'\r\nimport Calendar from '../../components/u-calendar/calendar'\r\nimport CarKeyboard from '../../components/u-car-keyboard/carKeyboard'\r\nimport Card from '../../components/u-card/card'\r\nimport Cell from '../../components/u-cell/cell'\r\nimport CellGroup from '../../components/u-cell-group/cellGroup'\r\nimport Checkbox from '../../components/u-checkbox/checkbox'\r\nimport CheckboxGroup from '../../components/u-checkbox-group/checkboxGroup'\r\nimport CircleProgress from '../../components/u-circle-progress/circleProgress'\r\nimport Code from '../../components/u-code/code'\r\nimport CodeInput from '../../components/u-code-input/codeInput'\r\nimport Col from '../../components/u-col/col'\r\nimport Collapse from '../../components/u-collapse/collapse'\r\nimport CollapseItem from '../../components/u-collapse-item/collapseItem'\r\nimport ColumnNotice from '../../components/u-column-notice/columnNotice'\r\nimport CountDown from '../../components/u-count-down/countDown'\r\nimport CountTo from '../../components/u-count-to/countTo'\r\nimport DatetimePicker from '../../components/u-datetime-picker/datetimePicker'\r\nimport Divider from '../../components/u-divider/divider'\r\nimport Empty from '../../components/u-empty/empty'\r\nimport Form from '../../components/u-form/form'\r\nimport GormItem from '../../components/u-form-item/formItem'\r\nimport Gap from '../../components/u-gap/gap'\r\nimport Grid from '../../components/u-grid/grid'\r\nimport GridItem from '../../components/u-grid-item/gridItem'\r\nimport Icon from '../../components/u-icon/icon'\r\nimport Image from '../../components/u-image/image'\r\nimport IndexAnchor from '../../components/u-index-anchor/indexAnchor'\r\nimport IndexList from '../../components/u-index-list/indexList'\r\nimport Input from '../../components/u-input/input'\r\nimport Keyboard from '../../components/u-keyboard/keyboard'\r\nimport Line from '../../components/u-line/line'\r\nimport LineProgress from '../../components/u-line-progress/lineProgress'\r\nimport Link from '../../components/u-link/link'\r\nimport List from '../../components/u-list/list'\r\nimport ListItem from '../../components/u-list-item/listItem'\r\nimport LoadingIcon from '../../components/u-loading-icon/loadingIcon'\r\nimport LoadingPage from '../../components/u-loading-page/loadingPage'\r\nimport Loadmore from '../../components/u-loadmore/loadmore'\r\nimport Modal from '../../components/u-modal/modal'\r\nimport Navbar from '../../components/u-navbar/navbar'\r\nimport NoNetwork from '../../components/u-no-network/noNetwork'\r\nimport NoticeBar from '../../components/u-notice-bar/noticeBar'\r\nimport Notify from '../../components/u-notify/notify'\r\nimport NumberBox from '../../components/u-number-box/numberBox'\r\nimport NumberKeyboard from '../../components/u-number-keyboard/numberKeyboard'\r\nimport Overlay from '../../components/u-overlay/overlay'\r\nimport Parse from '../../components/u-parse/parse'\r\nimport Picker from '../../components/u-picker/picker'\r\nimport Popup from '../../components/u-popup/popup'\r\nimport Radio from '../../components/u-radio/radio'\r\nimport RadioGroup from '../../components/u-radio-group/radioGroup'\r\nimport Rate from '../../components/u-rate/rate'\r\nimport ReadMore from '../../components/u-read-more/readMore'\r\nimport Row from '../../components/u-row/row'\r\nimport RowNotice from '../../components/u-row-notice/rowNotice'\r\nimport ScrollList from '../../components/u-scroll-list/scrollList'\r\nimport Search from '../../components/u-search/search'\r\nimport Section from '../../components/u-section/section'\r\nimport Skeleton from '../../components/u-skeleton/skeleton'\r\nimport Slider from '../../components/u-slider/slider'\r\nimport StatusBar from '../../components/u-status-bar/statusBar'\r\nimport Steps from '../../components/u-steps/steps'\r\nimport StepsItem from '../../components/u-steps-item/stepsItem'\r\nimport Sticky from '../../components/u-sticky/sticky'\r\nimport Subsection from '../../components/u-subsection/subsection'\r\nimport SwipeAction from '../../components/u-swipe-action/swipeAction'\r\nimport SwipeActionItem from '../../components/u-swipe-action-item/swipeActionItem'\r\nimport Swiper from '../../components/u-swiper/swiper'\r\nimport SwipterIndicator from '../../components/u-swiper-indicator/swipterIndicator'\r\nimport Switch from '../../components/u-switch/switch'\r\nimport Tabbar from '../../components/u-tabbar/tabbar'\r\nimport TabbarItem from '../../components/u-tabbar-item/tabbarItem'\r\nimport Tabs from '../../components/u-tabs/tabs'\r\nimport Tag from '../../components/u-tag/tag'\r\nimport Text from '../../components/u-text/text'\r\nimport Textarea from '../../components/u-textarea/textarea'\r\nimport Toast from '../../components/u-toast/toast'\r\nimport Toolbar from '../../components/u-toolbar/toolbar'\r\nimport Tooltip from '../../components/u-tooltip/tooltip'\r\nimport Transition from '../../components/u-transition/transition'\r\nimport Upload from '../../components/u-upload/upload'\r\n\r\nconst props = {\r\n    ...ActionSheet,\r\n    ...Album,\r\n    ...Alert,\r\n    ...Avatar,\r\n    ...AvatarGroup,\r\n    ...Backtop,\r\n    ...Badge,\r\n    ...Button,\r\n    ...Calendar,\r\n    ...CarKeyboard,\r\n    ...Card,\r\n    ...Cell,\r\n    ...CellGroup,\r\n    ...Checkbox,\r\n    ...CheckboxGroup,\r\n    ...CircleProgress,\r\n    ...Code,\r\n    ...CodeInput,\r\n    ...Col,\r\n    ...Collapse,\r\n    ...CollapseItem,\r\n    ...ColumnNotice,\r\n    ...CountDown,\r\n    ...CountTo,\r\n    ...DatetimePicker,\r\n    ...Divider,\r\n    ...Empty,\r\n    ...Form,\r\n    ...GormItem,\r\n    ...Gap,\r\n    ...Grid,\r\n    ...GridItem,\r\n    ...Icon,\r\n    ...Image,\r\n    ...IndexAnchor,\r\n    ...IndexList,\r\n    ...Input,\r\n    ...Keyboard,\r\n    ...Line,\r\n    ...LineProgress,\r\n    ...Link,\r\n    ...List,\r\n    ...ListItem,\r\n    ...LoadingIcon,\r\n    ...LoadingPage,\r\n    ...Loadmore,\r\n    ...Modal,\r\n    ...Navbar,\r\n    ...NoNetwork,\r\n    ...NoticeBar,\r\n    ...Notify,\r\n    ...NumberBox,\r\n    ...NumberKeyboard,\r\n    ...Overlay,\r\n    ...Parse,\r\n    ...Picker,\r\n    ...Popup,\r\n    ...Radio,\r\n    ...RadioGroup,\r\n    ...Rate,\r\n    ...ReadMore,\r\n    ...Row,\r\n    ...RowNotice,\r\n    ...ScrollList,\r\n    ...Search,\r\n    ...Section,\r\n    ...Skeleton,\r\n    ...Slider,\r\n    ...StatusBar,\r\n    ...Steps,\r\n    ...StepsItem,\r\n    ...Sticky,\r\n    ...Subsection,\r\n    ...SwipeAction,\r\n    ...SwipeActionItem,\r\n    ...Swiper,\r\n    ...SwipterIndicator,\r\n    ...Switch,\r\n    ...Tabbar,\r\n    ...TabbarItem,\r\n    ...Tabs,\r\n    ...Tag,\r\n    ...Text,\r\n    ...Textarea,\r\n    ...Toast,\r\n    ...Toolbar,\r\n    ...Tooltip,\r\n    ...Transition,\r\n    ...Upload\r\n}\r\n\r\nfunction setConfig(configs) {\r\n\tshallowMerge(config, configs.config || {})\r\n\tshallowMerge(props, configs.props || {})\r\n\tshallowMerge(color, configs.color || {})\r\n\tshallowMerge(zIndex, configs.zIndex || {})\r\n}\r\n\r\n// 初始化自定义配置\r\nif (uni && uni.upuiParams) {\r\n\tconsole.log('setting uview-plus')\r\n\tlet temp = uni.upuiParams()\r\n\tif (temp.httpIns) {\r\n\t\ttemp.httpIns(http)\r\n\t}\r\n\tif (temp.options) {\r\n\t\tsetConfig(temp.options)\r\n\t}\r\n}\r\n\r\nexport default props\r\n"], "names": ["ActionSheet", "Album", "<PERSON><PERSON>", "Avatar", "AvatarGroup", "Backtop", "Badge", "<PERSON><PERSON>", "Calendar", "CarKeyboard", "Card", "Cell", "CellGroup", "Checkbox", "CheckboxGroup", "CircleProgress", "Code", "CodeInput", "Col", "Collapse", "CollapseItem", "ColumnNotice", "CountDown", "<PERSON><PERSON><PERSON>", "DatetimePicker", "Divider", "Empty", "Form", "GormItem", "Gap", "Grid", "GridItem", "Icon", "Image", "IndexAnchor", "IndexList", "Input", "Keyboard", "Line", "LineProgress", "Link", "List", "ListItem", "LoadingIcon", "LoadingPage", "Loadmore", "Modal", "<PERSON><PERSON><PERSON>", "NoNetwork", "NoticeBar", "Notify", "NumberBox", "NumberKeyboard", "Overlay", "Parse", "Picker", "Popup", "Radio", "RadioGroup", "Rate", "ReadMore", "Row", "RowNotice", "ScrollList", "Search", "Section", "Skeleton", "Slide<PERSON>", "StatusBar", "Steps", "StepsItem", "<PERSON>y", "Subsection", "SwipeAction", "SwipeActionItem", "Swiper", "SwipterIndicator", "Switch", "Ta<PERSON><PERSON>", "TabbarItem", "Tabs", "Tag", "Text", "Textarea", "Toast", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Transition", "Upload", "shallowMerge", "config", "color", "zIndex", "uni", "http"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGK,MAAC,QAAQ;AAAA,EACV,GAAGA,0DAAW;AAAA,EACd,GAAGC,8CAAK;AAAA,EACR,GAAGC,8CAAK;AAAA,EACR,GAAGC,gDAAM;AAAA,EACT,GAAGC,0DAAW;AAAA,EACd,GAAGC,kDAAO;AAAA,EACV,GAAGC,8CAAK;AAAA,EACR,GAAGC,gDAAM;AAAA,EACT,GAAGC,oDAAQ;AAAA,EACX,GAAGC,0DAAW;AAAA,EACd,GAAGC,4CAAI;AAAA,EACP,GAAGC,4CAAI;AAAA,EACP,GAAGC,sDAAS;AAAA,EACZ,GAAGC,oDAAQ;AAAA,EACX,GAAGC,8DAAa;AAAA,EAChB,GAAGC,gEAAc;AAAA,EACjB,GAAGC,4CAAI;AAAA,EACP,GAAGC,sDAAS;AAAA,EACZ,GAAGC,0CAAG;AAAA,EACN,GAAGC,oDAAQ;AAAA,EACX,GAAGC,4DAAY;AAAA,EACf,GAAGC,4DAAY;AAAA,EACf,GAAGC,sDAAS;AAAA,EACZ,GAAGC,kDAAO;AAAA,EACV,GAAGC,gEAAc;AAAA,EACjB,GAAGC,kDAAO;AAAA,EACV,GAAGC,8CAAK;AAAA,EACR,GAAGC,4CAAI;AAAA,EACP,GAAGC,oDAAQ;AAAA,EACX,GAAGC,0CAAG;AAAA,EACN,GAAGC,4CAAI;AAAA,EACP,GAAGC,oDAAQ;AAAA,EACX,GAAGC,4CAAI;AAAA,EACP,GAAGC,8CAAK;AAAA,EACR,GAAGC,0DAAW;AAAA,EACd,GAAGC,sDAAS;AAAA,EACZ,GAAGC,8CAAK;AAAA,EACR,GAAGC,oDAAQ;AAAA,EACX,GAAGC,4CAAI;AAAA,EACP,GAAGC,4DAAY;AAAA,EACf,GAAGC,4CAAI;AAAA,EACP,GAAGC,4CAAI;AAAA,EACP,GAAGC,oDAAQ;AAAA,EACX,GAAGC,0DAAW;AAAA,EACd,GAAGC,0DAAW;AAAA,EACd,GAAGC,oDAAQ;AAAA,EACX,GAAGC,8CAAK;AAAA,EACR,GAAGC,gDAAM;AAAA,EACT,GAAGC,sDAAS;AAAA,EACZ,GAAGC,sDAAS;AAAA,EACZ,GAAGC,gDAAM;AAAA,EACT,GAAGC,sDAAS;AAAA,EACZ,GAAGC,gEAAc;AAAA,EACjB,GAAGC,kDAAO;AAAA,EACV,GAAGC,8CAAK;AAAA,EACR,GAAGC,gDAAM;AAAA,EACT,GAAGC,8CAAK;AAAA,EACR,GAAGC,8CAAK;AAAA,EACR,GAAGC,wDAAU;AAAA,EACb,GAAGC,4CAAI;AAAA,EACP,GAAGC,oDAAQ;AAAA,EACX,GAAGC,0CAAG;AAAA,EACN,GAAGC,sDAAS;AAAA,EACZ,GAAGC,wDAAU;AAAA,EACb,GAAGC,gDAAM;AAAA,EACT,GAAGC,kDAAO;AAAA,EACV,GAAGC,oDAAQ;AAAA,EACX,GAAGC,gDAAM;AAAA,EACT,GAAGC,sDAAS;AAAA,EACZ,GAAGC,8CAAK;AAAA,EACR,GAAGC,sDAAS;AAAA,EACZ,GAAGC,gDAAM;AAAA,EACT,GAAGC,wDAAU;AAAA,EACb,GAAGC,0DAAW;AAAA,EACd,GAAGC,kEAAe;AAAA,EAClB,GAAGC,gDAAM;AAAA,EACT,GAAGC,mEAAgB;AAAA,EACnB,GAAGC,gDAAM;AAAA,EACT,GAAGC,gDAAM;AAAA,EACT,GAAGC,wDAAU;AAAA,EACb,GAAGC,4CAAI;AAAA,EACP,GAAGC,0CAAG;AAAA,EACN,GAAGC,4CAAI;AAAA,EACP,GAAGC,oDAAQ;AAAA,EACX,GAAGC,8CAAK;AAAA,EACR,GAAGC,kDAAO;AAAA,EACV,GAAGC,kDAAO;AAAA,EACV,GAAGC,wDAAU;AAAA,EACb,GAAGC,gDAAM;AACb;AAEA,SAAS,UAAU,SAAS;AAC3BC,4CAAAA,aAAaC,yCAAM,QAAE,QAAQ,UAAU,CAAA,CAAE;AACzCD,4CAAAA,aAAa,OAAO,QAAQ,SAAS,CAAA,CAAE;AACvCA,4CAAAA,aAAaE,wCAAK,OAAE,QAAQ,SAAS,CAAA,CAAE;AACvCF,4CAAAA,aAAaG,yCAAM,QAAE,QAAQ,UAAU,CAAA,CAAE;AAC1C;AAGA,IAAIC,cAAG,SAAIA,cAAG,MAAC,YAAY;AAC1BA,gBAAAA,MAAA,MAAA,OAAA,sDAAY,oBAAoB;AAChC,MAAI,OAAOA,cAAG,MAAC,WAAY;AAC3B,MAAI,KAAK,SAAS;AACjB,SAAK,QAAQC,6CAAI;AAAA,EACjB;AACD,MAAI,KAAK,SAAS;AACjB,cAAU,KAAK,OAAO;AAAA,EACtB;AACF;;"}