{"version": 3, "file": "policy_detail.js", "sources": ["pages_sub/pages_country/policy_detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2NvdW50cnlccG9saWN5X2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <DetailSkeleton v-if=\"loading\" />\r\n\r\n  <view v-else-if=\"article\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">政策详情</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"main-content\">\r\n        <view class=\"article-header\">\r\n          <text class=\"article-title\">{{ article.title }}</text>\r\n          <view class=\"article-meta\">\r\n            <view class=\"meta-item\">\r\n              <text class=\"meta-label\">发布于 {{ article.createTime }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"content-card\">\r\n          <view class=\"content-body\">\r\n            <mp-html\r\n                :content=\"article.content\"\r\n                :domain=\"baseUrl\"\r\n                :tag-style=\"tagStyle\"\r\n                :container-style=\"containerStyle\"\r\n                :preview-img=\"true\"\r\n                lazy-load\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n\r\n  <view v-else class=\"error-state\">\r\n    <u-empty mode=\"list\" text=\"文章不存在或已被删除\"></u-empty>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {onLoad} from '@dcloudio/uni-app'\r\nimport {ref} from 'vue';\r\nimport mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';\r\nimport {getCountryPolicyArticle} from '@/api/content/countryPolicy.js';\r\nimport {IMAGE_BASE_URL} from '@/utils/config.js';\r\nimport {containerStyle, tagStyle} from '@/pages_sub/pages_article/api/common/mpHtmlStyles.js';\r\n\r\n// --- [新增] 自定义导航栏相关逻辑 (标准版本) ---\r\n// 1. 定义想要的额外间距（单位rpx）\r\nconst navBarPaddingBottomRpx = 10; // 政策页间距可以小一些，设为10rpx\r\n// 2. 将 rpx 转换为 px，用于 JS 计算\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\n\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    // 3. 在总高度计算中使用转换后的 px 值\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    // 4. 在回退方案中也使用转换后的 px 值\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n// --- 导航栏逻辑结束 ---\r\n\r\n\r\nconst baseUrl = IMAGE_BASE_URL;\r\n\r\nconst article = ref(null);\r\nconst loading = ref(true);\r\n\r\nonLoad(async (options) => {\r\n  // [新增] 调用函数获取导航栏尺寸\r\n  getNavBarInfo();\r\n\r\n  if (!options.id) {\r\n    uni.showToast({ title: '参数错误', icon: 'none' });\r\n    loading.value = false;\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const res = await getCountryPolicyArticle(options.id);\r\n    if (res.code === 200 && res.data) {\r\n      article.value = res.data;\r\n      // [移除] 不再需要手动设置原生导航栏标题\r\n      // uni.setNavigationBarTitle({ title: res.data.title || '政策详情' });\r\n    } else {\r\n      throw new Error(res.msg || '文章不存在或已被删除');\r\n    }\r\n  } catch (error) {\r\n    console.error('获取国别政策文章失败:', error);\r\n    article.value = null;\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- [新增] 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  background-color: #f8f9fa; /* 滚动区背景色 */\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx; /* 遵循新标准 */\r\n}\r\n\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n\r\n  /* [新增] 防止标题过长换行 */\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 60%; /* 限制标题最大宽度，避免与按钮重叠 */\r\n}\r\n/* --- 导航栏样式结束 --- */\r\n\r\n\r\n.main-content {\r\n  padding: 30rpx;\r\n}\r\n\r\n.article-header {\r\n  background: #ffffff;\r\n  padding: 40rpx;\r\n  border-radius: 24rpx;\r\n  margin-bottom: 30rpx;\r\n  .article-title {\r\n    font-size: 44rpx;\r\n    font-weight: 700;\r\n    color: #303133;\r\n    line-height: 1.5;\r\n  }\r\n  .article-meta {\r\n    margin-top: 30rpx;\r\n    font-size: 26rpx;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.content-card {\r\n  background: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 10rpx 40rpx 40rpx 40rpx;\r\n}\r\n\r\n.error-state {\r\n  padding-top: 200rpx;\r\n}\r\n\r\n:deep(.mp-html table) {\r\n  width: 100% !important;\r\n  border-collapse: collapse !important;\r\n  margin: 40rpx 0 !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n}\r\n\r\n:deep(.mp-html th) {\r\n  background: #f8f9fa !important;\r\n  color: #303133 !important;\r\n  padding: 24rpx !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n  font-weight: 600 !important;\r\n}\r\n\r\n:deep(.mp-html td) {\r\n  border: 2rpx solid #e9e9eb !important;\r\n  padding: 24rpx !important;\r\n  background: #ffffff !important;\r\n  color: #3D424D !important;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_country/policy_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "IMAGE_BASE_URL", "onLoad", "getCountryPolicyArticle"], "mappings": ";;;;;;;;;;;;;;;;AAiDA,MAAM,SAAS,MAAW;AAO1B,MAAM,yBAAyB;;;;AAE/B,UAAM,wBAAwBA,cAAG,MAAC,OAAO,sBAAsB;AAE/D,UAAM,kBAAkBC,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAE1B,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AACF,cAAM,iBAAiBD,oBAAI;AAC3B,wBAAgB,QAAQ,eAAe;AACvC,qBAAa,QAAQ,eAAe;AAEpC,qBAAa,QAAQ,eAAe,SAAS;AAAA,MAC9C,SAAQ,GAAG;AACV,cAAM,aAAaA,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AACtD,qBAAa,QAAQ;AAErB,qBAAa,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAAA,MACnE;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa,EAAE,OAAO,EAAG,CAAA;AAAA,IAC/B;AAIA,UAAM,UAAUE,aAAAA;AAEhB,UAAM,UAAUD,cAAAA,IAAI,IAAI;AACxB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAExBE,kBAAM,OAAC,OAAO,YAAY;AAExB;AAEA,UAAI,CAAC,QAAQ,IAAI;AACfH,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAED,UAAI;AACF,cAAM,MAAM,MAAMI,0BAAAA,wBAAwB,QAAQ,EAAE;AACpD,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,kBAAQ,QAAQ,IAAI;AAAA,QAG1B,OAAW;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,YAAY;AAAA,QACxC;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAc,MAAA,MAAA,SAAA,oDAAA,eAAe,KAAK;AAClC,gBAAQ,QAAQ;AAAA,MACpB,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnHD,GAAG,WAAW,eAAe;"}