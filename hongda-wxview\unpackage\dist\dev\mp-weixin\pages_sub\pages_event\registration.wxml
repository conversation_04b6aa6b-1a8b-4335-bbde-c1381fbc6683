<view class="registration-page data-v-5cc4b4a8"><up-navbar wx:if="{{b}}" class="data-v-5cc4b4a8" bindleftClick="{{a}}" u-i="5cc4b4a8-0" bind:__l="__l" u-p="{{b}}"/><view wx:if="{{c}}" class="loading-container data-v-5cc4b4a8"><up-loading-page wx:if="{{d}}" class="data-v-5cc4b4a8" u-i="5cc4b4a8-1" bind:__l="__l" u-p="{{d}}"></up-loading-page></view><view wx:elif="{{e}}" class="login-prompt-container data-v-5cc4b4a8"><up-empty wx:if="{{h}}" class="data-v-5cc4b4a8" u-s="{{['button']}}" u-i="5cc4b4a8-2" bind:__l="__l" u-p="{{h}}"><up-button class="data-v-5cc4b4a8" bindclick="{{f}}" u-i="5cc4b4a8-3,5cc4b4a8-2" bind:__l="__l" u-p="{{g}}" slot="button"></up-button></up-empty></view><view wx:elif="{{i}}" class="empty-container data-v-5cc4b4a8"><up-empty wx:if="{{j}}" class="data-v-5cc4b4a8" u-i="5cc4b4a8-4" bind:__l="__l" u-p="{{j}}"></up-empty></view><scroll-view wx:elif="{{k}}" scroll-y class="scroll-content data-v-5cc4b4a8"><view wx:if="{{l}}" class="event-title-section data-v-5cc4b4a8"><text class="event-title data-v-5cc4b4a8">{{m}}</text></view><view class="form-container data-v-5cc4b4a8"><up-form wx:if="{{p}}" class="r data-v-5cc4b4a8" u-s="{{['d']}}" u-r="formRef" u-i="5cc4b4a8-5" bind:__l="__l" u-p="{{p}}"><block wx:for="{{n}}" wx:for-item="item" wx:key="L"><up-form-item wx:if="{{item.a}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"><up-input wx:if="{{item.d}}" class="data-v-5cc4b4a8" u-i="{{item.b}}" bind:__l="__l" bindupdateModelValue="{{item.c}}" u-p="{{item.d}}"></up-input></up-form-item><up-form-item wx:elif="{{item.g}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"><up-textarea wx:if="{{item.j}}" class="data-v-5cc4b4a8" u-i="{{item.h}}" bind:__l="__l" bindupdateModelValue="{{item.i}}" u-p="{{item.j}}"></up-textarea></up-form-item><up-form-item wx:elif="{{item.m}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.q}}" bind:__l="__l" u-p="{{item.r}}"><up-input wx:if="{{item.p}}" class="data-v-5cc4b4a8" bindclick="{{item.n}}" u-i="{{item.o}}" bind:__l="__l" u-p="{{item.p}}"></up-input></up-form-item><up-form-item wx:elif="{{item.s}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.x}}" bind:__l="__l" u-p="{{item.y}}"><up-input wx:if="{{item.w}}" class="data-v-5cc4b4a8" u-i="{{item.t}}" bind:__l="__l" bindupdateModelValue="{{item.v}}" u-p="{{item.w}}"></up-input></up-form-item><up-form-item wx:elif="{{item.z}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.D}}" bind:__l="__l" u-p="{{item.E}}"><up-input wx:if="{{item.C}}" class="data-v-5cc4b4a8" u-i="{{item.A}}" bind:__l="__l" bindupdateModelValue="{{item.B}}" u-p="{{item.C}}"></up-input></up-form-item><up-form-item wx:elif="{{item.F}}" u-s="{{['d']}}" class="form-item data-v-5cc4b4a8" style="{{'margin-bottom:' + '92rpx'}}" u-i="{{item.J}}" bind:__l="__l" u-p="{{item.K}}"><up-input wx:if="{{item.I}}" class="data-v-5cc4b4a8" u-i="{{item.G}}" bind:__l="__l" bindupdateModelValue="{{item.H}}" u-p="{{item.I}}"></up-input></up-form-item></block></up-form></view><view class="bottom-spacer data-v-5cc4b4a8"></view></scroll-view><view wx:if="{{q}}" class="bottom-action-bar data-v-5cc4b4a8"><up-button wx:if="{{t}}" class="data-v-5cc4b4a8" u-s="{{['d']}}" bindclick="{{s}}" u-i="5cc4b4a8-18" bind:__l="__l" u-p="{{t}}">{{r}}</up-button></view><up-picker wx:if="{{y}}" class="r data-v-5cc4b4a8" u-r="pickerRef" bindconfirm="{{w}}" bindcancel="{{x}}" u-i="5cc4b4a8-19" bind:__l="__l" u-p="{{y}}"></up-picker><view wx:if="{{z}}" class="confirm-modal-overlay data-v-5cc4b4a8" bindtap="{{E}}"><view class="confirm-modal-content data-v-5cc4b4a8" catchtap="{{D}}"><view class="modal-header data-v-5cc4b4a8"><image class="warning-icon data-v-5cc4b4a8" src="{{A}}" mode="aspectFit"></image><text class="modal-title data-v-5cc4b4a8">操作提示</text></view><view class="modal-body data-v-5cc4b4a8"><text class="modal-message data-v-5cc4b4a8">确认提交报名信息？</text></view><view class="modal-footer data-v-5cc4b4a8"><view class="modal-btn cancel-btn data-v-5cc4b4a8" bindtap="{{B}}"> 暂不提交 </view><view class="modal-btn confirm-btn data-v-5cc4b4a8" bindtap="{{C}}"> 确认提交 </view></view></view></view></view>