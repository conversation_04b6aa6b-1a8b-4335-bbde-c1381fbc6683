{"version": 3, "file": "u-link.js", "sources": ["uni_modules/uview-plus/components/u-link/u-link.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxpbmsvdS1saW5rLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<text\r\n\t    class=\"u-link\"\r\n\t    @tap.stop=\"openLink\"\r\n\t    :style=\"[linkStyle, addStyle(customStyle)]\"\r\n\t>{{text}}</text>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle, addUnit, getPx, toast } from '../../libs/function/index';\r\n\t/**\r\n\t * link 超链接\r\n\t * @description 该组件为超链接组件，在不同平台有不同表现形式：在APP平台会通过plus环境打开内置浏览器，在小程序中把链接复制到粘贴板，同时提示信息，在H5中通过window.open打开链接。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/link.html\r\n\t * @property {String}\t\t\tcolor\t\t文字颜色 （默认 color['u-primary'] ）\r\n\t * @property {String ｜ Number}\tfontSize\t字体大小，单位px （默认 15 ）\r\n\t * @property {Boolean}\t\t\tunderLine\t是否显示下划线 （默认 false ）\r\n\t * @property {String}\t\t\thref\t\t跳转的链接，要带上http(s)\r\n\t * @property {String}\t\t\tmpTips\t\t各个小程序平台把链接复制到粘贴板后的提示语（默认“链接已复制，请在浏览器打开”）\r\n\t * @property {String}\t\t\tlineColor\t下划线颜色，默认同color参数颜色 \r\n\t * @property {String}\t\t\ttext\t\t超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色 \r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * \r\n\t * @example <u-link href=\"http://www.uviewui.com\">蜀道难，难于上青天</u-link>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-link\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tcomputed: {\r\n\t\t\tlinkStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tcolor: this.color,\r\n\t\t\t\t\tfontSize: addUnit(this.fontSize),\r\n\t\t\t\t\t// line-height设置为比字体大小多2px\r\n\t\t\t\t\tlineHeight: addUnit(getPx(this.fontSize) + 2),\r\n\t\t\t\t\ttextDecoration: this.underLine ? 'underline' : 'none'\r\n\t\t\t\t}\r\n\t\t\t\t// if (this.underLine) {\r\n\t\t\t\t// \tstyle.borderBottomColor = this.lineColor || this.color\r\n\t\t\t\t// \tstyle.borderBottomWidth = '1px'\r\n\t\t\t\t// }\r\n\t\t\t\treturn style\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: [\"click\"],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\topenLink() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.runtime.openURL(this.href)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.open(this.href)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.href,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.hideToast();\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\ttoast(this.mpTips);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$u-link-line-height:1 !default;\r\n\r\n\t.u-link {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tline-height: $u-link-line-height;\r\n\t\t/* #endif */\r\n\t\t@include flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tflex: 1;\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-link/u-link.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "getPx", "addStyle", "uni", "toast"], "mappings": ";;;;;;AA4BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,YAAY;AACX,YAAM,QAAQ;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,UAAUC,0CAAAA,QAAQ,KAAK,QAAQ;AAAA;AAAA,QAE/B,YAAYA,0CAAO,QAACC,0CAAK,MAAC,KAAK,QAAQ,IAAI,CAAC;AAAA,QAC5C,gBAAgB,KAAK,YAAY,cAAc;AAAA,MAChD;AAKA,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,WAAW;AAQVC,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACdA,wBAAG,MAAC,UAAS;AACb,eAAK,UAAU,MAAM;AACpBC,4DAAM,KAAK,MAAM;AAAA,WACjB;AAAA,QACF;AAAA,MACD,CAAC;AAED,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;ACtED,GAAG,gBAAgB,SAAS;"}