{"version": 3, "file": "noticeBar.js", "sources": ["uni_modules/uview-plus/components/u-notice-bar/noticeBar.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:17:13\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/noticeBar.js\r\n */\r\nexport default {\r\n    // noticeBar\r\n    noticeBar: {\r\n        text: [],\r\n        direction: 'row',\r\n        step: false,\r\n        icon: 'volume',\r\n        mode: '',\r\n        color: '#f9ae3d',\r\n        bgColor: '#fdf6ec',\r\n        speed: 80,\r\n        fontSize: 14,\r\n        duration: 2000,\r\n        disableTouch: true,\r\n        url: '',\r\n        linkType: 'navigateTo',\r\n\t\tjustifyContent: 'flex-start'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM,CAAE;AAAA,IACR,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,KAAK;AAAA,IACL,UAAU;AAAA,IAChB,gBAAgB;AAAA,EACb;AACL;;"}