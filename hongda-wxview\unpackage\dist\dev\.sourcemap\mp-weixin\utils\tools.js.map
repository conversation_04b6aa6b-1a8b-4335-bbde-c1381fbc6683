{"version": 3, "file": "tools.js", "sources": ["utils/tools.js"], "sourcesContent": ["/**\r\n * 通用工具函数\r\n */\r\nimport { BASE_URL } from './config.js';\r\n\r\n\r\n\r\n/**\r\n * 格式化活动状态\r\n * @param {number} status - 状态码\r\n * @returns {string} 状态文本\r\n */\r\nexport function formatEventStatus(status) {\r\n  const statusMap = {\r\n    0: '未开始',\r\n    1: '报名中', \r\n    2: '已结束',\r\n    3: '已取消',\r\n    4: '进行中'\r\n  };\r\n  return statusMap[status] || '未知';\r\n}\r\n\r\n/**\r\n * 获取状态样式类名\r\n * @param {number} status - 状态码\r\n * @returns {string} CSS类名\r\n */\r\nexport function getStatusClass(status) {\r\n  return status === 2 || status === 3 ? 'ended' : 'registering';\r\n}\r\n\r\n/**\r\n * 计算剩余名额\r\n * @param {number} maxParticipants - 最大参与人数\r\n * @param {number} registeredCount - 已报名人数\r\n * @returns {string|number} 剩余名额或\"不限\"\r\n */\r\nexport function calculateRemainingSpots(maxParticipants, registeredCount) {\r\n  if (!maxParticipants || maxParticipants <= 0) {\r\n    return '不限';\r\n  }\r\n  const remaining = maxParticipants - (registeredCount || 0);\r\n  return remaining > 0 ? remaining : 0;\r\n}\r\n\r\n/**\r\n * 防抖函数\r\n * @param {Function} func - 要防抖的函数\r\n * @param {number} wait - 等待时间（毫秒）\r\n * @returns {Function} 防抖后的函数\r\n */\r\nexport function debounce(func, wait) {\r\n  let timeout;\r\n  return function executedFunction(...args) {\r\n    const later = () => {\r\n      clearTimeout(timeout);\r\n      func(...args);\r\n    };\r\n    clearTimeout(timeout);\r\n    timeout = setTimeout(later, wait);\r\n  };\r\n}\r\n\r\n/**\r\n * 节流函数\r\n * @param {Function} func - 要节流的函数\r\n * @param {number} wait - 等待时间（毫秒）\r\n * @returns {Function} 节流后的函数\r\n */\r\nexport function throttle(func, wait) {\r\n  let timeout;\r\n  return function executedFunction(...args) {\r\n    if (!timeout) {\r\n      func(...args);\r\n      timeout = setTimeout(() => {\r\n        timeout = null;\r\n      }, wait);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * 格式化价格\r\n * @param {number} price - 价格数值\r\n * @param {string} currency - 货币符号\r\n * @returns {string} 格式化后的价格字符串\r\n */\r\nexport function formatPrice(price, currency = '¥') {\r\n  if (price === 0) return '免费';\r\n  if (!price || price < 0) return '价格待定';\r\n  return `${currency}${price.toFixed(2)}`;\r\n}\r\n\r\n/**\r\n * 验证手机号\r\n * @param {string} phone - 手机号\r\n * @returns {boolean} 是否有效\r\n */\r\nexport function validatePhone(phone) {\r\n  const phoneRegex = /^1[3-9]\\d{9}$/;\r\n  return phoneRegex.test(phone);\r\n}\r\n\r\n/**\r\n * 验证邮箱\r\n * @param {string} email - 邮箱地址\r\n * @returns {boolean} 是否有效\r\n */\r\nexport function validateEmail(email) {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\n/**\r\n * 深拷贝对象\r\n * @param {any} obj - 要拷贝的对象\r\n * @returns {any} 拷贝后的对象\r\n */\r\nexport function deepClone(obj) {\r\n  if (obj === null || typeof obj !== 'object') return obj;\r\n  if (obj instanceof Date) return new Date(obj.getTime());\r\n  if (obj instanceof Array) return obj.map(item => deepClone(item));\r\n  if (typeof obj === 'object') {\r\n    const clonedObj = {};\r\n    for (const key in obj) {\r\n      if (obj.hasOwnProperty(key)) {\r\n        clonedObj[key] = deepClone(obj[key]);\r\n      }\r\n    }\r\n    return clonedObj;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取文件扩展名\r\n * @param {string} filename - 文件名\r\n * @returns {string} 文件扩展名\r\n */\r\nexport function getFileExtension(filename) {\r\n  if (!filename) return '';\r\n  const lastDotIndex = filename.lastIndexOf('.');\r\n  return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1).toLowerCase() : '';\r\n}\r\n\r\n/**\r\n * 格式化文件大小\r\n * @param {number} bytes - 字节数\r\n * @returns {string} 格式化后的文件大小\r\n */\r\nexport function formatFileSize(bytes) {\r\n  if (bytes === 0) return '0 B';\r\n  const k = 1024;\r\n  const sizes = ['B', 'KB', 'MB', 'GB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i];\r\n} "], "names": [], "mappings": ";AAYO,SAAS,kBAAkB,QAAQ;AACxC,QAAM,YAAY;AAAA,IAChB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACE,SAAO,UAAU,MAAM,KAAK;AAC9B;AAOO,SAAS,eAAe,QAAQ;AACrC,SAAO,WAAW,KAAK,WAAW,IAAI,UAAU;AAClD;AAQO,SAAS,wBAAwB,iBAAiB,iBAAiB;AACxE,MAAI,CAAC,mBAAmB,mBAAmB,GAAG;AAC5C,WAAO;AAAA,EACR;AACD,QAAM,YAAY,mBAAmB,mBAAmB;AACxD,SAAO,YAAY,IAAI,YAAY;AACrC;AAQO,SAAS,SAAS,MAAM,MAAM;AACnC,MAAI;AACJ,SAAO,SAAS,oBAAoB,MAAM;AACxC,UAAM,QAAQ,MAAM;AAClB,mBAAa,OAAO;AACpB,WAAK,GAAG,IAAI;AAAA,IAClB;AACI,iBAAa,OAAO;AACpB,cAAU,WAAW,OAAO,IAAI;AAAA,EACpC;AACA;;;;;"}