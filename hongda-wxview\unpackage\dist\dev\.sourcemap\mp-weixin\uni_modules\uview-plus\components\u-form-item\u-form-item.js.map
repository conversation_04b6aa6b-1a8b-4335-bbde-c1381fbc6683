{"version": 3, "file": "u-form-item.js", "sources": ["uni_modules/uview-plus/components/u-form-item/u-form-item.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWZvcm0taXRlbS91LWZvcm0taXRlbS52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"u-form-item\" :class=\"{'u-form-item--error':(!!message && parentData.errorType === 'message')}\">\r\n\t\t<view\r\n\t\t\tclass=\"u-form-item__body\"\r\n\t\t\t@tap=\"clickHandler\"\r\n\t\t\t:style=\"[addStyle(customStyle), {\r\n                flexDirection: (labelPosition || parentData.labelPosition) === 'left' ? 'row' : 'column'\r\n\t\t\t}]\"\r\n\t\t>\r\n\t\t\t<!-- 微信小程序中，将一个参数设置空字符串，结果会变成字符串\"true\" -->\r\n\t\t\t<slot name=\"label\">\r\n\t\t\t\t<!-- {{required}} -->\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-form-item__body__left\"\r\n\t\t\t\t\tv-if=\"required || leftIcon || label\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\twidth: addUnit(labelWidth || parentData.labelWidth),\r\n\t\t\t\t\t\tmarginBottom: parentData.labelPosition === 'left' ? 0 : '5px',\r\n\t\t\t\t\t}\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<!-- 为了块对齐 -->\r\n\t\t\t\t\t<view class=\"u-form-item__body__left__content\">\r\n\t\t\t\t\t\t<!-- nvue不支持伪元素before -->\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tv-if=\"required\"\r\n\t\t\t\t\t\t\tclass=\"u-form-item__body__left__content__required\"\r\n\t\t\t\t\t\t>*</text>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"u-form-item__body__left__content__icon\"\r\n\t\t\t\t\t\t\tv-if=\"leftIcon\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\t\t:name=\"leftIcon\"\r\n\t\t\t\t\t\t\t\t:custom-style=\"leftIconStyle\"\r\n\t\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tclass=\"u-form-item__body__left__content__label\"\r\n\t\t\t\t\t\t\t:style=\"[parentData.labelStyle, {\r\n\t\t\t\t\t\t\t\tjustifyContent: parentData.labelAlign === 'left' ? 'flex-start' : parentData.labelAlign === 'center' ? 'center' : 'flex-end'\r\n\t\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t\t>{{ label }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</slot>\r\n\t\t\t<view class=\"u-form-item__body__right\">\r\n\t\t\t\t<view class=\"u-form-item__body__right__content\">\r\n\t\t\t\t\t<view class=\"u-form-item__body__right__content__slot\">\r\n\t\t\t\t\t\t<slot />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"item__body__right__content__icon\"\r\n\t\t\t\t\t\tv-if=\"$slots.right\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<slot name=\"right\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<slot name=\"error\">\r\n\t\t\t<text\r\n\t\t\t\tv-if=\"!!message && parentData.errorType === 'message'\"\r\n\t\t\t\tclass=\"u-form-item__body__right__message\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\tmarginLeft:  addUnit(parentData.labelPosition === 'top' ? 0 : (labelWidth || parentData.labelWidth))\r\n\t\t\t\t}\"\r\n\t\t\t>{{ message }}</text>\r\n\t\t</slot>\r\n\t\t<u-line\r\n\t\t\tv-if=\"borderBottom\"\r\n\t\t\t:color=\"message && parentData.errorType === 'border-bottom' ? color.error : propsLine.color\"\r\n\t\t\t:customStyle=\"`margin-top: ${message && parentData.errorType === 'message' ? '5px' : 0}`\"\r\n\t\t></u-line>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport defProps from '../../libs/config/props.js'\r\n\timport color from '../../libs/config/color';\r\n\timport { addStyle, addUnit, getProperty, setProperty, error } from '../../libs/function/index';\r\n\t/**\r\n\t * Form 表单\r\n\t * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/form.html\r\n\t * @property {String}\t\t\tlabel\t\t\tinput的label提示语\r\n\t * @property {String}\t\t\tprop\t\t\t绑定的值\r\n\t * @property {Array}\t\t\trules\t\t\t绑定的规则\r\n\t * @property {String | Boolean}\tborderBottom\t是否显示表单域的下划线边框\r\n\t * @property {String | Number}\tlabelWidth\t\tlabel的宽度，单位px\r\n\t * @property {String}\t\t\trightIcon\t\t右侧图标\r\n\t * @property {String}\t\t\tleftIcon\t\t左侧图标\r\n\t * @property {String | Object} leftIconStyle    左侧图标的样式\r\n\t * @property {Boolean}\t\t\trequired\t\t是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置 (默认 false )\r\n\t *\r\n\t * @example <u-form-item label=\"姓名\" prop=\"userInfo.name\" borderBottom ref=\"item1\"></u-form-item>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-form-item',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 错误提示语\r\n\t\t\t\tmessage: '',\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\t// 提示文本的位置\r\n\t\t\t\t\tlabelPosition: 'left',\r\n\t\t\t\t\t// 提示文本对齐方式\r\n\t\t\t\t\tlabelAlign: 'left',\r\n\t\t\t\t\t// 提示文本的样式\r\n\t\t\t\t\tlabelStyle: {},\r\n\t\t\t\t\t// 提示文本的宽度\r\n\t\t\t\t\tlabelWidth: 45,\r\n\t\t\t\t\t// 错误提示方式\r\n\t\t\t\t\terrorType: 'message'\r\n\t\t\t\t},\r\n\t\t\t\tcolor: color,\r\n\t\t\t\titemRules: []\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 组件创建完成时，将当前实例保存到u-form中\r\n\t\tcomputed: {\r\n\t\t\tpropsLine() {\r\n\t\t\t\treturn defProps.line\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\temits: [\"click\"],\r\n\t\twatch: {\r\n\t\t\t// 监听规则的变化\r\n\t\t\trules: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tthis.setRules(n);\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\tinit() {\r\n\t\t\t\t// 父组件的实例\r\n\t\t\t\tthis.updateParentData()\r\n\t\t\t\tif (!this.parent) {\r\n\t\t\t\t\terror('u-form-item需要结合u-form组件使用')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 手动设置校验的规则，如果规则中有函数的话，微信小程序中会过滤掉，所以只能手动调用设置规则\r\n\t\t\tsetRules(rules) {\r\n\t\t\t\t// 判断是否有规则\r\n\t\t\t\tif (rules.length === 0) {\r\n\t\t\t\t\tthis.itemRules = [];\r\n\t\t\t\t\treturn\r\n\t\t\t\t};\r\n\t\t\t\tthis.itemRules = rules;\r\n\t\t\t},\r\n\t\t\t// 获取父组件的参数\r\n\t\t\tupdateParentData() {\r\n\t\t\t\t// 此方法写在mixin中\r\n\t\t\t\tthis.getParentData('u-form');\r\n\t\t\t},\r\n\t\t\t// 移除u-form-item的校验结果\r\n\t\t\tclearValidate() {\r\n\t\t\t\tthis.message = null\r\n\t\t\t},\r\n\t\t\t// 清空当前的组件的校验结果，并重置为初始值\r\n\t\t\tresetField() {\r\n\t\t\t\t// 找到原始值\r\n\t\t\t\tconst value = getProperty(this.parent.originalModel, this.prop)\r\n\t\t\t\t// 将u-form的model的prop属性链还原原始值\r\n\t\t\t\tsetProperty(this.parent.model, this.prop, value)\r\n\t\t\t\t// 移除校验结果\r\n\t\t\t\tthis.message = null\r\n\t\t\t},\r\n\t\t\t// 点击组件\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-form-item {\r\n\t\t@include flex(column);\r\n\t\tfont-size: 14px;\r\n\t\tcolor: $u-main-color;\r\n\r\n\t\t&__body {\r\n\t\t\t@include flex;\r\n\t\t\tpadding: 10px 0;\r\n\r\n\t\t\t&__left {\r\n\t\t\t\t@include flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t&__content {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding-right: 10rpx;\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t&__icon {\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__required {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: -9px;\r\n\t\t\t\t\t\tcolor: $u-error;\r\n\t\t\t\t\t\tline-height: 20px;\r\n\t\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t\t\ttop: 3px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__label {\r\n\t\t\t\t\t\t@include flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__right {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t&__content {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t&__slot {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t@include flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__icon {\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\tcolor: $u-light-color;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__message {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tline-height: 12px;\r\n\t\t\t\t\tcolor: $u-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-form-item/u-form-item.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "color", "defProps", "addStyle", "addUnit", "error", "getProperty", "setProperty"], "mappings": ";;;;;;;;AAmGC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,sDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,QAEX,eAAe;AAAA;AAAA,QAEf,YAAY;AAAA;AAAA,QAEZ,YAAY,CAAE;AAAA;AAAA,QAEd,YAAY;AAAA;AAAA,QAEZ,WAAW;AAAA,MACX;AAAA,MACD,OAAOC,wCAAK;AAAA,MACZ,WAAW,CAAC;AAAA,IACb;AAAA,EACA;AAAA;AAAA,EAED,UAAU;AAAA,IACT,YAAY;AACX,aAAOC,wCAAQ,MAAC;AAAA,IACjB;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,WAAW;AAAA,MACX,QAAQ,GAAG;AACV,aAAK,SAAS,CAAC;AAAA,MACf;AAAA,IACD;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAC,0CAAO;AAAA,IACP,OAAO;AAEN,WAAK,iBAAiB;AACtB,UAAI,CAAC,KAAK,QAAQ;AACjBC,kDAAAA,MAAM,2BAA2B;AAAA,MAClC;AAAA,IACA;AAAA;AAAA,IAED,SAAS,OAAO;AAEf,UAAI,MAAM,WAAW,GAAG;AACvB,aAAK,YAAY;AACjB;AAAA;AAED,WAAK,YAAY;AAAA,IACjB;AAAA;AAAA,IAED,mBAAmB;AAElB,WAAK,cAAc,QAAQ;AAAA,IAC3B;AAAA;AAAA,IAED,gBAAgB;AACf,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,aAAa;AAEZ,YAAM,QAAQC,0CAAAA,YAAY,KAAK,OAAO,eAAe,KAAK,IAAI;AAE9DC,gDAAW,YAAC,KAAK,OAAO,OAAO,KAAK,MAAM,KAAK;AAE/C,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,eAAe;AACd,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtLD,GAAG,gBAAgB,SAAS;"}