{"version": 3, "file": "u-overlay.js", "sources": ["uni_modules/uview-plus/components/u-overlay/u-overlay.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LW92ZXJsYXkvdS1vdmVybGF5LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<u-transition\r\n\t    :show=\"show\"\r\n\t    custom-class=\"u-overlay\"\r\n\t    :duration=\"duration\"\r\n\t    :custom-style=\"overlayStyle\"\r\n\t    @click=\"clickHandler\"\r\n\t\************************=\"noop\"\r\n\t>\r\n\t\t<slot />\r\n\t</u-transition>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle, deepMerge } from '../../libs/function/index';\r\n\t/**\r\n\t * overlay 遮罩\r\n\t * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/overlay.html\r\n\t * @property {Boolean}\t\t\tshow\t\t是否显示遮罩（默认 false ）\r\n\t * @property {String | Number}\tzIndex\t\tzIndex 层级（默认 10070 ）\r\n\t * @property {String | Number}\tduration\t动画时长，单位毫秒（默认 300 ）\r\n\t * @property {String | Number}\topacity\t\t不透明度值，当做rgba的第四个参数 （默认 0.5 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @pages_event {Function} click 点击遮罩发送事件\r\n\t * @example <u-overlay :show=\"show\" @click=\"show = false\"></u-overlay>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-overlay\",\r\n\t\tmixins: [mpMixin, mixin,props],\r\n\t\tcomputed: {\r\n\t\t\toverlayStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tzIndex: this.zIndex,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t'background-color': `rgba(0, 0, 0, ${this.opacity})`\r\n\t\t\t\t}\r\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: [\"click\"],\r\n\t\tmethods: {\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n     $u-overlay-top:0 !default;\r\n     $u-overlay-left:0 !default;\r\n     $u-overlay-width:100% !default;\r\n     $u-overlay-height:100% !default;\r\n     $u-overlay-background-color:rgba(0, 0, 0, .7) !default;\r\n\t.u-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop:$u-overlay-top;\r\n\t\tleft:$u-overlay-left;\r\n\t\twidth: $u-overlay-width;\r\n\t\theight:$u-overlay-height;\r\n\t\tbackground-color:$u-overlay-background-color;\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-overlay/u-overlay.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "addStyle"], "mappings": ";;;;;;AA8BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAACC,qDAAK;AAAA,EAC7B,UAAU;AAAA,IACT,eAAe;AACd,YAAM,QAAQ;AAAA,QACb,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,oBAAoB,iBAAiB,KAAK,OAAO;AAAA,MAClD;AACA,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,eAAe;AACd,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;ACpDD,GAAG,gBAAgB,SAAS;"}