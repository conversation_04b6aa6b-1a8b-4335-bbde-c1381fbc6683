{"version": 3, "file": "u-tabs.js", "sources": ["uni_modules/uview-plus/components/u-tabs/u-tabs.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRhYnMvdS10YWJzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"u-tabs\" :class=\"[customClass]\">\r\n\t\t<view class=\"u-tabs__wrapper\">\r\n\t\t\t<slot name=\"left\" />\r\n\t\t\t<view class=\"u-tabs__wrapper__scroll-view-wrapper\">\r\n\t\t\t\t<scroll-view\r\n\t\t\t\t\t:scroll-x=\"scrollable\"\r\n\t\t\t\t\t:scroll-left=\"scrollLeft\"\r\n\t\t\t\t\tscroll-with-animation\r\n\t\t\t\t\tclass=\"u-tabs__wrapper__scroll-view\"\r\n\t\t\t\t\t:show-scrollbar=\"false\"\r\n\t\t\t\t\tref=\"u-tabs__wrapper__scroll-view\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav\"\r\n\t\t\t\t\t\tref=\"u-tabs__wrapper__nav\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in list\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t@tap=\"clickHandler(item, index)\"\r\n\t\t\t\t\t\t\t@longpress=\"longPressHandler(item,index)\"\r\n\t\t\t\t\t\t\t:ref=\"`u-tabs__wrapper__nav__item-${index}`\"\r\n\t\t\t\t\t\t\t:style=\"[addStyle(itemStyle), {flex: scrollable ? '' : 1}]\"\r\n\t\t\t\t\t\t\t:class=\"[`u-tabs__wrapper__nav__item-${index}`,\r\n\t\t\t\t\t\t\t\titem.disabled && 'u-tabs__wrapper__nav__item--disabled',\r\n\t\t\t\t\t\t\t\tinnerCurrent == index ? 'u-tabs__wrapper__nav__item-active' : '']\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<slot v-if=\"$slots.icon\" name=\"icon\" :item=\"item\" :keyName=\"keyName\" :index=\"index\" />\r\n\t\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"u-tabs__wrapper__nav__item__prefix-icon\" v-if=\"item.icon\">\r\n\t\t\t\t\t\t\t\t\t<up-icon\r\n\t\t\t\t\t\t\t\t\t\t:name=\"item.icon\"\r\n\t\t\t\t\t\t\t\t\t\t:customStyle=\"addStyle(iconStyle)\"\r\n\t\t\t\t\t\t\t\t\t></up-icon>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t<slot v-if=\"$slots.content\" name=\"content\" :item=\"item\" :keyName=\"keyName\" :index=\"index\" />\r\n\t\t\t\t\t\t\t<slot v-else-if=\"!$slots.content && ($slots.default || $slots.$default)\"\r\n\t\t\t\t\t\t\t\t:item=\"item\" :keyName=\"keyName\" :index=\"index\" />\r\n\t\t\t\t\t\t\t<text v-else\r\n\t\t\t\t\t\t\t\t:class=\"[item.disabled && 'u-tabs__wrapper__nav__item__text--disabled']\"\r\n\t\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__item__text\"\r\n\t\t\t\t\t\t\t\t:style=\"[textStyle(index)]\"\r\n\t\t\t\t\t\t\t>{{ item[keyName] }}</text>\r\n\t\t\t\t\t\t\t<u-badge\r\n\t\t\t\t\t\t\t\t:show=\"!!(item.badge && (item.badge.show || item.badge.isDot || item.badge.value))\"\r\n\t\t\t\t\t\t\t\t:isDot=\"item.badge && item.badge.isDot || propsBadge.isDot\"\r\n\t\t\t\t\t\t\t\t:value=\"item.badge && item.badge.value || propsBadge.value\"\r\n\t\t\t\t\t\t\t\t:max=\"item.badge && item.badge.max || propsBadge.max\"\r\n\t\t\t\t\t\t\t\t:type=\"item.badge && item.badge.type || propsBadge.type\"\r\n\t\t\t\t\t\t\t\t:showZero=\"item.badge && item.badge.showZero || propsBadge.showZero\"\r\n\t\t\t\t\t\t\t\t:bgColor=\"item.badge && item.badge.bgColor || propsBadge.bgColor\"\r\n\t\t\t\t\t\t\t\t:color=\"item.badge && item.badge.color || propsBadge.color\"\r\n\t\t\t\t\t\t\t\t:shape=\"item.badge && item.badge.shape || propsBadge.shape\"\r\n\t\t\t\t\t\t\t\t:numberType=\"item.badge && item.badge.numberType || propsBadge.numberType\"\r\n\t\t\t\t\t\t\t\t:inverted=\"item.badge && item.badge.inverted || propsBadge.inverted\"\r\n\t\t\t\t\t\t\t\tcustomStyle=\"margin-left: 4px;\"\r\n\t\t\t\t\t\t\t></u-badge>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\t\twidth: addUnit(lineWidth),\r\n\t\t\t\t\t\t\t\theight: addUnit(lineHeight),\r\n\t\t\t\t\t\t\t\tbackground: lineColor,\r\n\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\r\n\t\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"u-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\tref=\"u-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\t\twidth: addUnit(lineWidth),\r\n\t\t\t\t\t\t\t\ttransform: `translate(${lineOffsetLeft}px)`,\r\n\t\t\t\t\t\t\t\ttransitionDuration: `${firstTime ? 0 : duration}ms`,\r\n\t\t\t\t\t\t\t\theight: addUnit(lineHeight),\r\n\t\t\t\t\t\t\t\tbackground: lineColor,\r\n\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\r\n\t\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<slot name=\"right\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport defProps from '../../libs/config/props.js'\r\n\timport { addUnit, addStyle, deepMerge, getPx, sleep, getWindowInfo } from '../../libs/function/index';\r\n\t/**\r\n\t * Tabs 标签\r\n\t * @description tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/tabs.html\r\n\t * @property {String | Number}\tduration\t\t\t滑块移动一次所需的时间，单位秒（默认 200 ）\r\n\t * @property {String | Number}\tswierWidth\t\t\tswiper的宽度（默认 '750rpx' ）\r\n\t * @property {String}\tkeyName\t 从`list`元素对象中读取的键名（默认 'name' ）\r\n\t * @pages_event {Function(index)} change 标签改变时触发 index: 点击了第几个tab，索引从0开始\r\n\t * @pages_event {Function(index)} click 点击标签时触发 index: 点击了第几个tab，索引从0开始\r\n\t * @pages_event {Function(index)} longPress 长按标签时触发 index: 点击了第几个tab，索引从0开始\r\n\t * @example <u-tabs :list=\"list\" :is-scroll=\"false\" :current=\"current\" @change=\"change\" @longPress=\"longPress\"></u-tabs>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-tabs',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfirstTime: true,\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tscrollViewWidth: 0,\r\n\t\t\t\tlineOffsetLeft: 0,\r\n\t\t\t\ttabsRect: {\r\n\t\t\t\t\tleft: 0\r\n\t\t\t\t},\r\n\t\t\t\tinnerCurrent: 0,\r\n\t\t\t\tmoving: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcurrent: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler (newValue, oldValue) {\r\n\t\t\t\t\t// 内外部值不相等时，才尝试移动滑块\r\n\t\t\t\t\tif (newValue !== this.innerCurrent) {\r\n\t\t\t\t\t\tif (typeof newValue == 'string') {\r\n\t\t\t\t\t\t\tthis.innerCurrent = parseInt(newValue)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.innerCurrent = newValue\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.resize()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// list变化时，重新渲染list各项信息\r\n\t\t\tlist() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.resize()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttextStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tconst style = {}\r\n\t\t\t\t\t// 取当期是否激活的样式\r\n\t\t\t\t\tconst customeStyle = (index == this.innerCurrent)\r\n\t\t\t\t\t\t? addStyle(this.activeStyle) \r\n\t\t\t\t\t\t: addStyle(this.inactiveStyle)\r\n\t\t\t\t\t// 如果当前菜单被禁用，则加上对应颜色，需要在此做处理，是因为nvue下，无法在style样式中通过!import覆盖标签的内联样式\r\n\t\t\t\t\tif (this.list[index].disabled) {\r\n\t\t\t\t\t\tstyle.color = '#c8c9cc'\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn deepMerge(customeStyle, style)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpropsBadge() {\r\n\t\t\t\treturn defProps.badge\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tthis.init()\r\n            this.windowResizeCallback = (res) => {\r\n                this.init()\r\n            }\r\n            uni.onWindowResize(this.windowResizeCallback)\r\n\t\t},\r\n        beforeUnmount() {\r\n            uni.offWindowResize(this.windowResizeCallback)\r\n        },\r\n\t\temits: ['click', 'longPress', 'change', 'update:current'],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\tsetLineLeft() {\r\n\t\t\t\tconst tabItem = this.list[this.innerCurrent];\r\n\t\t\t\tif (!tabItem) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 获取滑块该移动的位置\r\n\t\t\t\tlet lineOffsetLeft = this.list\r\n\t\t\t\t\t.slice(0, this.innerCurrent)\r\n\t\t\t\t\t.reduce((total, curr) => total + curr.rect.width, 0);\r\n                // 获取下划线的数值px表示法\r\n\t\t\t\tconst lineWidth = getPx(this.lineWidth);\r\n\t\t\t\tthis.lineOffsetLeft = lineOffsetLeft + (tabItem.rect.width - lineWidth) / 2\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 第一次移动滑块，无需过渡时间\r\n\t\t\t\tthis.animation(this.lineOffsetLeft, this.firstTime ? 0 : parseInt(this.duration))\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// 如果是第一次执行此方法，让滑块在初始化时，瞬间滑动到第一个tab item的中间\r\n\t\t\t\t// 这里需要一个定时器，因为在非nvue下，是直接通过style绑定过渡时间，需要等其过渡完成后，再设置为false(非第一次移动滑块)\r\n\t\t\t\tif (this.firstTime) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.firstTime = false\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// nvue下设置滑块的位置\r\n\t\t\tanimation(x, duration = 0) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst ref = this.$refs['u-tabs__wrapper__nav__line']\r\n\t\t\t\tanimation.transition(ref, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `translateX(${x}px)`\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 点击某一个标签\r\n\t\t\tclickHandler(item, index) {\r\n\t\t\t\t// 因为标签可能为disabled状态，所以click是一定会发出的，但是change事件是需要可用的状态才发出\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t...item,\r\n\t\t\t\t\tindex\r\n\t\t\t\t}, index)\r\n\t\t\t\t// 如果disabled状态，返回\r\n\t\t\t\tif (item.disabled) return\r\n\t\t\t\t// 如果点击当前不触发change\r\n\t\t\t\tif (this.innerCurrent == index) return\r\n\t\t\t\tthis.innerCurrent = index\r\n\t\t\t\tthis.resize()\r\n\t\t\t\tthis.$emit('update:current', index)\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t...item,\r\n\t\t\t\t\tindex\r\n\t\t\t\t}, index)\r\n\t\t\t},\r\n\t\t\t// 长按事件\r\n\t\t\tlongPressHandler(item, index) {\r\n\t\t\t\tthis.$emit('longPress', {\r\n\t\t\t\t\t...item,\r\n\t\t\t\t\tindex\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinit() {\r\n\t\t\t\tsleep().then(() => {\r\n\t\t\t\t\tthis.resize()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetScrollLeft() {\r\n\t\t\t\t// 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\r\n\t\t\t\tif (this.innerCurrent < 0) {\r\n                    this.innerCurrent = 0;\r\n                }\r\n\t\t\t\tconst tabRect = this.list[this.innerCurrent]\r\n\t\t\t\t// 累加得到当前item到左边的距离\r\n\t\t\t\tconst offsetLeft = this.list\r\n\t\t\t\t\t.slice(0, this.innerCurrent)\r\n\t\t\t\t\t.reduce((total, curr) => {\r\n\t\t\t\t\t\treturn total + curr.rect.width\r\n\t\t\t\t\t}, 0)\r\n\t\t\t\t// 此处为屏幕宽度\r\n\t\t\t\tconst windowWidth = getWindowInfo().windowWidth\r\n\t\t\t\t// 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\r\n\t\t\t\tlet scrollLeft = offsetLeft - (this.tabsRect.width - tabRect.rect.width) / 2 - (windowWidth - this.tabsRect\r\n\t\t\t\t\t.right) / 2 + this.tabsRect.left / 2\r\n\t\t\t\t// 这里做一个限制，限制scrollLeft的最大值为整个scroll-view宽度减去tabs组件的宽度\r\n\t\t\t\tscrollLeft = Math.min(scrollLeft, this.scrollViewWidth - this.tabsRect.width)\r\n\t\t\t\tthis.scrollLeft = Math.max(0, scrollLeft)\r\n\t\t\t},\r\n\t\t\t// 获取所有标签的尺寸\r\n\t\t\tresize() {\r\n\t\t\t\t// 如果不存在list，则不处理\r\n\t\t\t\tif(this.list.length === 0) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tPromise.all([this.getTabsRect(), this.getAllItemRect()]).then(([tabsRect, itemRect = []]) => {\r\n\t\t\t\t\t// 兼容在swiper组件中使用\r\n\t\t\t\t\tif (tabsRect.left > tabsRect.width) {\r\n\t\t\t\t\t\ttabsRect.right = tabsRect.right - Math.floor(tabsRect.left / tabsRect.width) * tabsRect.width\r\n\t\t\t\t\t\ttabsRect.left = tabsRect.left % tabsRect.width\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// console.log(tabsRect)\r\n\t\t\t\t\tthis.tabsRect = tabsRect\r\n\t\t\t\t\tthis.scrollViewWidth = 0\r\n\t\t\t\t\titemRect.map((item, index) => {\r\n\t\t\t\t\t\t// 计算scroll-view的宽度，这里\r\n\t\t\t\t\t\tthis.scrollViewWidth += item.width\r\n\t\t\t\t\t\t// 另外计算每一个item的中心点X轴坐标\r\n\t\t\t\t\t\tthis.list[index].rect = item\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 获取了tabs的尺寸之后，设置滑块的位置\r\n\t\t\t\t\tthis.setLineLeft()\r\n\t\t\t\t\tthis.setScrollLeft()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取导航菜单的尺寸\r\n\t\t\tgetTabsRect() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tthis.queryRect('u-tabs__wrapper__scroll-view').then(size => resolve(size))\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取所有标签的尺寸\r\n\t\t\tgetAllItemRect() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tconst promiseAllArr = this.list.map((item, index) => this.queryRect(\r\n\t\t\t\t\t\t`u-tabs__wrapper__nav__item-${index}`, true))\r\n\t\t\t\t\tPromise.all(promiseAllArr).then(sizes => resolve(sizes))\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取各个标签的尺寸\r\n\t\t\tqueryRect(el, item) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t// $uGetRect为uView自带的节点查询简化方法，详见文档介绍：https://ijry.github.io/uview-plus/js/getRect.html\r\n\t\t\t\t// 组件内部一般用this.$uGetRect，对外的为uni.$u.getRect，二者功能一致，名称不同\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tthis.$uGetRect(`.${el}`).then(size => {\r\n\t\t\t\t\t\tresolve(size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// nvue下，使用dom模块查询元素高度\r\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tdom.getComponentRect(item ? this.$refs[el][0] : this.$refs[el], res => {\r\n\t\t\t\t\t\tresolve(res.size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-tabs {\r\n\r\n\t\t&__wrapper {\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&__scroll-view-wrapper {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\toverflow: auto hidden;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\r\n\t\t\t&__scroll-view {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t&__nav {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t&__item {\r\n\t\t\t\t\tpadding: 0 11px;\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t/* #endif */\r\n\r\n\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__text {\r\n\t\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\t\tcolor: $u-content-color;\r\n                        white-space: nowrap !important;\r\n\r\n\t\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t\tcolor: $u-disabled-color !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__line {\r\n\t\t\t\t\theight: 3px;\r\n\t\t\t\t\tbackground: $u-primary;\r\n\t\t\t\t\twidth: 30px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 2px;\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t\ttransition-property: transform;\r\n\t\t\t\t\ttransition-duration: 300ms;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-tabs/u-tabs.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle", "deepMerge", "defProps", "uni", "addUnit", "getPx", "sleep", "getWindowInfo"], "mappings": ";;;;;;;AAuHC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,UAAU;AAAA,QACT,MAAM;AAAA,MACN;AAAA,MACD,cAAc;AAAA,MACd,QAAQ;AAAA,IACT;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,SAAS;AAAA,MACR,WAAW;AAAA,MACX,QAAS,UAAU,UAAU;AAE5B,YAAI,aAAa,KAAK,cAAc;AACnC,cAAI,OAAO,YAAY,UAAU;AAChC,iBAAK,eAAe,SAAS,QAAQ;AAAA,iBAC/B;AACN,iBAAK,eAAe;AAAA,UACrB;AACA,eAAK,UAAU,MAAM;AACpB,iBAAK,OAAO;AAAA,WACZ;AAAA,QACF;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAED,OAAO;AACN,WAAK,UAAU,MAAM;AACpB,aAAK,OAAO;AAAA,OACZ;AAAA,IACF;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,aAAO,WAAS;AACf,cAAM,QAAQ,CAAC;AAEf,cAAM,eAAgB,SAAS,KAAK,eACjCC,0CAAQ,SAAC,KAAK,WAAW,IACzBA,0CAAQ,SAAC,KAAK,aAAa;AAE9B,YAAI,KAAK,KAAK,KAAK,EAAE,UAAU;AAC9B,gBAAM,QAAQ;AAAA,QACf;AACA,eAAOC,0CAAS,UAAC,cAAc,KAAK;AAAA,MACrC;AAAA,IACA;AAAA,IACD,aAAa;AACZ,aAAOC,wCAAQ,MAAC;AAAA,IACjB;AAAA,EACA;AAAA,EACD,MAAM,UAAU;AACf,SAAK,KAAK;AACD,SAAK,uBAAuB,CAAC,QAAQ;AACjC,WAAK,KAAK;AAAA,IACd;AACAC,wBAAI,eAAe,KAAK,oBAAoB;AAAA,EACrD;AAAA,EACK,gBAAgB;AACZA,wBAAI,gBAAgB,KAAK,oBAAoB;AAAA,EAChD;AAAA,EACP,OAAO,CAAC,SAAS,aAAa,UAAU,gBAAgB;AAAA,EACxD,SAAS;AAAA,IACR,UAAAH,0CAAQ;AAAA,IACR,SAAAI,0CAAO;AAAA,IACP,cAAc;AACb,YAAM,UAAU,KAAK,KAAK,KAAK,YAAY;AAC3C,UAAI,CAAC,SAAS;AACb;AAAA,MACD;AAEA,UAAI,iBAAiB,KAAK,KACxB,MAAM,GAAG,KAAK,YAAY,EAC1B,OAAO,CAAC,OAAO,SAAS,QAAQ,KAAK,KAAK,OAAO,CAAC;AAEpD,YAAM,YAAYC,0CAAAA,MAAM,KAAK,SAAS;AACtC,WAAK,iBAAiB,kBAAkB,QAAQ,KAAK,QAAQ,aAAa;AAQ1E,UAAI,KAAK,WAAW;AACnB,mBAAW,MAAM;AAChB,eAAK,YAAY;AAAA,QACjB,GAAE,EAAE;AAAA,MACN;AAAA,IACA;AAAA;AAAA,IAED,UAAU,GAAG,WAAW,GAAG;AAAA,IAU1B;AAAA;AAAA,IAED,aAAa,MAAM,OAAO;AAEzB,WAAK,MAAM,SAAS;AAAA,QACnB,GAAG;AAAA,QACH;AAAA,MACA,GAAE,KAAK;AAER,UAAI,KAAK;AAAU;AAEnB,UAAI,KAAK,gBAAgB;AAAO;AAChC,WAAK,eAAe;AACpB,WAAK,OAAO;AACZ,WAAK,MAAM,kBAAkB,KAAK;AAClC,WAAK,MAAM,UAAU;AAAA,QACpB,GAAG;AAAA,QACH;AAAA,MACA,GAAE,KAAK;AAAA,IACR;AAAA;AAAA,IAED,iBAAiB,MAAM,OAAO;AAC7B,WAAK,MAAM,aAAa;AAAA,QACvB,GAAG;AAAA,QACH;AAAA,OACA;AAAA,IACD;AAAA,IACD,OAAO;AACNC,gDAAK,MAAA,EAAG,KAAK,MAAM;AAClB,aAAK,OAAO;AAAA,OACZ;AAAA,IACD;AAAA,IACD,gBAAgB;AAEf,UAAI,KAAK,eAAe,GAAG;AACX,aAAK,eAAe;AAAA,MACxB;AACZ,YAAM,UAAU,KAAK,KAAK,KAAK,YAAY;AAE3C,YAAM,aAAa,KAAK,KACtB,MAAM,GAAG,KAAK,YAAY,EAC1B,OAAO,CAAC,OAAO,SAAS;AACxB,eAAO,QAAQ,KAAK,KAAK;AAAA,MACzB,GAAE,CAAC;AAEL,YAAM,cAAcC,0CAAa,cAAA,EAAG;AAEpC,UAAI,aAAa,cAAc,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,KAAK,cAAc,KAAK,SACjG,SAAS,IAAI,KAAK,SAAS,OAAO;AAEpC,mBAAa,KAAK,IAAI,YAAY,KAAK,kBAAkB,KAAK,SAAS,KAAK;AAC5E,WAAK,aAAa,KAAK,IAAI,GAAG,UAAU;AAAA,IACxC;AAAA;AAAA,IAED,SAAS;AAER,UAAG,KAAK,KAAK,WAAW,GAAG;AAC1B;AAAA,MACD;AACA,cAAQ,IAAI,CAAC,KAAK,YAAW,GAAI,KAAK,eAAgB,CAAA,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,WAAW,CAAE,CAAA,MAAM;AAE5F,YAAI,SAAS,OAAO,SAAS,OAAO;AACnC,mBAAS,QAAQ,SAAS,QAAQ,KAAK,MAAM,SAAS,OAAO,SAAS,KAAK,IAAI,SAAS;AACxF,mBAAS,OAAO,SAAS,OAAO,SAAS;AAAA,QAC1C;AAEA,aAAK,WAAW;AAChB,aAAK,kBAAkB;AACvB,iBAAS,IAAI,CAAC,MAAM,UAAU;AAE7B,eAAK,mBAAmB,KAAK;AAE7B,eAAK,KAAK,KAAK,EAAE,OAAO;AAAA,SACxB;AAED,aAAK,YAAY;AACjB,aAAK,cAAc;AAAA,OACnB;AAAA,IACD;AAAA;AAAA,IAED,cAAc;AACb,aAAO,IAAI,QAAQ,aAAW;AAC7B,aAAK,UAAU,8BAA8B,EAAE,KAAK,UAAQ,QAAQ,IAAI,CAAC;AAAA,OACzE;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB;AAChB,aAAO,IAAI,QAAQ,aAAW;AAC7B,cAAM,gBAAgB,KAAK,KAAK,IAAI,CAAC,MAAM,UAAU,KAAK;AAAA,UACzD,8BAA8B,KAAK;AAAA,UAAI;AAAA,QAAI,CAAC;AAC7C,gBAAQ,IAAI,aAAa,EAAE,KAAK,WAAS,QAAQ,KAAK,CAAC;AAAA,OACvD;AAAA,IACD;AAAA;AAAA,IAED,UAAU,IAAI,MAAM;AAInB,aAAO,IAAI,QAAQ,aAAW;AAC7B,aAAK,UAAU,IAAI,EAAE,EAAE,EAAE,KAAK,UAAQ;AACrC,kBAAQ,IAAI;AAAA,SACZ;AAAA,OACD;AAAA,IAYD;AAAA,EACD;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvVD,GAAG,gBAAgB,SAAS;"}