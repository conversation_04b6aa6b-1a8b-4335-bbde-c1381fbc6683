{"version": 3, "file": "event.js", "sources": ["api/data/event.js"], "sourcesContent": ["/**\r\n * 活动相关API接口\r\n */\r\nimport http from '@/utils/request.js';\r\nimport { API_PATHS } from '@/utils/config.js';\r\n\r\n/**\r\n * 获取活动列表\r\n * @param {Object} params 查询参数\r\n * @param {number} params.pageNum 页码\r\n * @param {number} params.pageSize 每页数量\r\n * @param {string} params.title 活动标题（搜索）\r\n * @param {string} params.location 活动地点\r\n * @param {number} params.status 活动状态\r\n * @param {string} params.startTime 开始时间\r\n * @param {string} params.endTime 结束时间\r\n * @param {string} params.orderBy 排序字段\r\n * @param {string} params.isAsc 是否升序\r\n * @param {number} params.isHot 是否热门 (1: 热门, 0: 非热门)\r\n * @returns {Promise} API响应\r\n */\r\nexport const getEventListApi = (params) => {\r\n  return http.get(API_PATHS.EVENT_LIST, params);\r\n};\r\n\r\n/**\r\n * 获取活动详情\r\n * @param {number} id 活动ID\r\n * @returns {Promise} API响应\r\n */\r\nexport const getEventDetailApi = (id) => {\r\n  return http.get(`${API_PATHS.EVENT_DETAIL}/${id}`);\r\n};\r\n\r\n/**\r\n * 获取热门活动列表（首页用）\r\n * @param {number} limit 限制数量，默认5条\r\n * @returns {Promise} API响应\r\n */\r\nexport const getHotEventListApi = (limit = 5) => {\r\n  return http.get(API_PATHS.EVENT_LIST, { \r\n    pageNum: 1, \r\n    pageSize: limit, \r\n    isHot: 1,\r\n    status: 1 // 只获取报名中的热门活动\r\n  });\r\n};\r\n\r\n/**\r\n * 获取活动地区列表（用于筛选下拉框）\r\n * @returns {Promise} API响应\r\n */\r\nexport const getEventLocationsApi = () => {\r\n  return http.get(API_PATHS.EVENT_LOCATIONS);\r\n};\r\n\r\n/**\r\n * 获取即将开始的活动列表\r\n * @param {number} limit 限制数量，默认10条\r\n * @returns {Promise} API响应\r\n */\r\nexport const getUpcomingEventsApi = (limit = 10) => {\r\n  const now = new Date();\r\n  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\r\n  \r\n  return http.get(API_PATHS.EVENT_LIST, {\r\n    pageNum: 1,\r\n    pageSize: limit,\r\n    status: 1, // 报名中\r\n    startTime: now.toISOString(),\r\n    endTime: nextWeek.toISOString(),\r\n    orderBy: 'startTime',\r\n    isAsc: 'asc'\r\n  });\r\n};\r\n\r\n/**\r\n * 按地区获取活动列表\r\n * @param {string} location 地区名称\r\n * @param {Object} options 其他选项\r\n * @returns {Promise} API响应\r\n */\r\nexport const getEventsByLocationApi = (location, options = {}) => {\r\n  const params = {\r\n    pageNum: 1,\r\n    pageSize: 20,\r\n    location,\r\n    status: 1, // 默认只获取报名中的活动\r\n    ...options\r\n  };\r\n  \r\n  return http.get(API_PATHS.EVENT_LIST, params);\r\n};\r\n\r\n/**\r\n * 搜索活动\r\n * @param {Object} params 查询参数, e.g., { pageNum, pageSize, title, ... }\r\n * @returns {Promise} API响应\r\n */\r\nexport const searchEventsApi = (params) => {\r\n  // 直接将页面传递过来的参数对象透传给API请求\r\n  return http.get(API_PATHS.EVENT_LIST, params);\r\n};\r\n\r\n/**\r\n * 获取日历视图活动列表\r\n * 专门为日历视图优化：只返回状态0/1的活动，按开始时间从近到远排序\r\n * @param {Object} params 查询参数\r\n * @param {number} params.pageNum 页码\r\n * @param {number} params.pageSize 每页数量\r\n * @param {string} params.title 活动标题（搜索）\r\n * @param {string} params.location 活动地点\r\n * @param {string} params.timeRangeStart 时间范围开始\r\n * @param {string} params.timeRangeEnd 时间范围结束\r\n * @returns {Promise} API响应\r\n */\r\nexport const getCalendarEventsApi = (params) => {\r\n  return http.get('/events/calendar', params);\r\n};\r\n\r\n/**\r\n * 导出活动列表\r\n * @param {Object} params 查询参数\r\n * @returns {Promise} API响应\r\n */\r\nexport const exportEventListApi = (params) => {\r\n  return http.post(API_PATHS.EVENT_EXPORT, params, {\r\n    responseType: 'blob'\r\n  });\r\n}; "], "names": ["http", "API_PATHS"], "mappings": ";;;AAqBY,MAAC,kBAAkB,CAAC,WAAW;AACzC,SAAOA,cAAI,KAAC,IAAIC,aAAS,UAAC,YAAY,MAAM;AAC9C;AAOY,MAAC,oBAAoB,CAAC,OAAO;AACvC,SAAOD,cAAI,KAAC,IAAI,GAAGC,aAAAA,UAAU,YAAY,IAAI,EAAE,EAAE;AACnD;AAOY,MAAC,qBAAqB,CAAC,QAAQ,MAAM;AAC/C,SAAOD,mBAAK,IAAIC,aAAS,UAAC,YAAY;AAAA,IACpC,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA;AAAA,EACZ,CAAG;AACH;AAqDY,MAAC,kBAAkB,CAAC,WAAW;AAEzC,SAAOD,cAAI,KAAC,IAAIC,aAAS,UAAC,YAAY,MAAM;AAC9C;AAcY,MAAC,uBAAuB,CAAC,WAAW;AAC9C,SAAOD,mBAAK,IAAI,oBAAoB,MAAM;AAC5C;;;;;;"}