"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
const _sfc_main = {
  __name: "PopupAdComponent",
  props: {
    show: {
      type: <PERSON>olean,
      default: false
    },
    adData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const visible = common_vendor.ref(props.show);
    common_vendor.watch(() => props.show, (newVal) => {
      visible.value = newVal;
    });
    const handleAdClick = () => {
      if (props.adData && props.adData.linkUrl) {
        common_vendor.index.navigateTo({
          url: props.adData.linkUrl
        });
        closePopup();
      }
    };
    const closePopup = () => {
      visible.value = false;
      emit("close");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: visible.value
      }, visible.value ? {
        b: __props.adData.imageUrl,
        c: common_vendor.o(handleAdClick),
        d: common_vendor.p({
          type: "closeempty",
          size: "24",
          color: "#fff"
        }),
        e: common_vendor.o(closePopup),
        f: common_vendor.o(() => {
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ce514586"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/PopupAdComponent.js.map
