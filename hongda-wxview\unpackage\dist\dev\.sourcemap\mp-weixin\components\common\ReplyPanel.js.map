{"version": 3, "file": "ReplyPanel.js", "sources": ["components/common/ReplyPanel.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvY29tbW9uL1JlcGx5UGFuZWwudnVl"], "sourcesContent": ["<template>\r\n  <u-popup\r\n      :show=\"show\"\r\n      @close=\"handleClose\"\r\n      mode=\"bottom\"\r\n      round=\"20\"\r\n      :safe-area-inset-bottom=\"true\"\r\n  >\r\n    <view class=\"reply-popup\">\r\n      <view class=\"popup-header\">\r\n        <text class=\"popup-title\" v-if=\"replyTarget\">回复 @{{ replyTarget.nickname }}</text>\r\n        <text class=\"popup-close\" @click=\"handleClose\">×</text>\r\n      </view>\r\n      <view class=\"popup-body\">\r\n        <textarea\r\n            class=\"reply-input\"\r\n            v-model=\"content\"\r\n            placeholder=\"写下你的回复...\"\r\n            :auto-height=\"true\"\r\n            maxlength=\"300\"\r\n            :focus=\"show\"\r\n        ></textarea>\r\n        <view class=\"popup-footer\">\r\n          <text class=\"reply-counter\">{{ content.length }}/300</text>\r\n          <button\r\n              class=\"reply-submit\"\r\n              :class=\"{'reply-submit-active': canSubmit}\"\r\n              :disabled=\"!canSubmit || isSubmitting\"\r\n              :loading=\"isSubmitting\"\r\n              @click=\"handleSubmit\"\r\n          >\r\n            {{ isSubmitting ? '发送中' : '发送' }}\r\n          </button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </u-popup>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, watch } from 'vue';\r\n\r\nconst props = defineProps({\r\n  // 控制弹窗显示\r\n  show: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  // 被回复的目标评论对象\r\n  replyTarget: {\r\n    type: Object,\r\n    default: null\r\n  },\r\n  // 外部控制的提交状态\r\n  isSubmitting: {\r\n    type: Boolean,\r\n    default: false\r\n  }\r\n});\r\n\r\nconst emit = defineEmits(['update:show', 'close', 'submit']);\r\n\r\nconst content = ref('');\r\n\r\n// 监听 show 属性，当弹窗关闭时清空内容\r\nwatch(() => props.show, (newVal) => {\r\n  if (!newVal) {\r\n    content.value = '';\r\n  }\r\n});\r\n\r\n// 计算是否可以提交（内容不为空）\r\nconst canSubmit = computed(() => content.value.trim().length > 0);\r\n\r\n// 关闭弹窗\r\nconst handleClose = () => {\r\n  emit('close');\r\n};\r\n\r\n// 提交回复\r\nconst handleSubmit = () => {\r\n  if (!canSubmit.value || props.isSubmitting) return;\r\n  emit('submit', content.value);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.reply-popup {\r\n  background: #ffffff;\r\n  .popup-header {\r\n    position: relative;\r\n    padding: 30rpx;\r\n    border-bottom: 2rpx solid #f1f5f9;\r\n    text-align: center;\r\n    .popup-title {\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n      color: #606266;\r\n    }\r\n    .popup-close {\r\n      position: absolute;\r\n      right: 30rpx;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 40rpx;\r\n      color: #909399;\r\n    }\r\n  }\r\n  .popup-body {\r\n    padding: 30rpx;\r\n    .reply-input {\r\n      width: 100%;\r\n      min-height: 200rpx;\r\n      background: #f8f9fa;\r\n      border: 2rpx solid #e5e7eb;\r\n      border-radius: 16rpx;\r\n      padding: 24rpx;\r\n      font-size: 28rpx;\r\n      line-height: 1.6;\r\n      color: #303133;\r\n      box-sizing: border-box;\r\n    }\r\n    .popup-footer {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-top: 20rpx;\r\n      .reply-counter {\r\n        font-size: 24rpx;\r\n        color: #909399;\r\n      }\r\n      .reply-submit {\r\n        padding: 0 40rpx;\r\n        height: 60rpx;\r\n        line-height: 60rpx;\r\n        border-radius: 30rpx;\r\n        background: #dcdfe6;\r\n        color: #ffffff;\r\n        font-size: 28rpx;\r\n        margin: 0;\r\n        &.reply-submit-active {\r\n          background: #3c9cff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/common/ReplyPanel.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "computed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,UAAM,QAAQ;AAkBd,UAAM,OAAO;AAEb,UAAM,UAAUA,cAAAA,IAAI,EAAE;AAGtBC,kBAAK,MAAC,MAAM,MAAM,MAAM,CAAC,WAAW;AAClC,UAAI,CAAC,QAAQ;AACX,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;AAGD,UAAM,YAAYC,cAAAA,SAAS,MAAM,QAAQ,MAAM,OAAO,SAAS,CAAC;AAGhE,UAAM,cAAc,MAAM;AACxB,WAAK,OAAO;AAAA,IACd;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,UAAU,SAAS,MAAM;AAAc;AAC5C,WAAK,UAAU,QAAQ,KAAK;AAAA,IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFA,GAAG,gBAAgB,SAAS;"}