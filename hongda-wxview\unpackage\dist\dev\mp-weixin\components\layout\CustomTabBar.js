"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "CustomTabBar",
  props: {
    current: {
      type: Number,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const color = common_vendor.ref("#8A8A8A");
    const selectedColor = common_vendor.ref("#023F98");
    const list = common_vendor.ref([
      {
        pagePath: "/pages/index/index",
        iconPath: "",
        selectedIconPath: "",
        text: "推荐"
      },
      {
        pagePath: "/pages/article/index",
        iconPath: "",
        selectedIconPath: "",
        text: "资讯"
      },
      {
        pagePath: "/pages/event/index",
        iconPath: "",
        selectedIconPath: "",
        text: "热门活动",
        central: true
      },
      {
        pagePath: "/pages/country/index",
        iconPath: "",
        selectedIconPath: "",
        text: "国别资讯"
      },
      {
        pagePath: "/pages/profile/index",
        iconPath: "",
        selectedIconPath: "",
        text: "我的"
      }
    ]);
    const eventHotIconUrl = common_vendor.ref("");
    common_vendor.onMounted(() => {
      try {
        const assets = common_vendor.index.getStorageSync("staticAssets");
        if (assets && assets["event_hot"])
          eventHotIconUrl.value = assets["event_hot"];
        const mapping = {
          0: { icon: "recommend", active: "recommend_active" },
          1: { icon: "article", active: "article_active" },
          3: { icon: "country", active: "country_active" },
          4: { icon: "profile", active: "profile_active" }
        };
        list.value = list.value.map((item, idx) => {
          if (item.central)
            return item;
          const m = mapping[idx];
          if (!m)
            return item;
          return {
            ...item,
            iconPath: (assets == null ? void 0 : assets[m.icon]) || "",
            selectedIconPath: (assets == null ? void 0 : assets[m.active]) || ""
          };
        });
      } catch (e) {
      }
    });
    const switchTab = (item, index) => {
      if (props.current === index) {
        return;
      }
      common_vendor.index.switchTab({
        url: item.pagePath
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.central
          }, item.central ? {} : {
            b: __props.current === index ? item.selectedIconPath : item.iconPath,
            c: common_vendor.t(item.text),
            d: __props.current === index ? selectedColor.value : color.value
          }, {
            e: item.pagePath,
            f: common_vendor.o(($event) => switchTab(item, index), item.pagePath)
          });
        }),
        b: eventHotIconUrl.value,
        c: common_vendor.o(($event) => switchTab(list.value[2], 2))
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f44adf2b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/layout/CustomTabBar.js.map
