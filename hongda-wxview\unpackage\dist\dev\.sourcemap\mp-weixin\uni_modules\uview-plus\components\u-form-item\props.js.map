{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-form-item/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // input的label提示语\r\n        label: {\r\n            type: String,\r\n            default: () => defProps.formItem.label\r\n        },\r\n        // 绑定的值\r\n        prop: {\r\n            type: String,\r\n            default: () => defProps.formItem.prop\r\n        },\r\n        // 绑定的规则\r\n        rules: {\r\n            type: Array,\r\n            default: () => defProps.formItem.rules\r\n        },\r\n        // 是否显示表单域的下划线边框\r\n        borderBottom: {\r\n            type: [String, Boolean],\r\n            default: () => defProps.formItem.borderBottom\r\n        },\r\n        // label的位置，left-左边，top-上边\r\n        labelPosition: {\r\n            type: String,\r\n            default: () => defProps.formItem.labelPosition\r\n        },\r\n        // label的宽度，单位px\r\n        labelWidth: {\r\n            type: [String, Number],\r\n            default: () => defProps.formItem.labelWidth\r\n        },\r\n        // 右侧图标\r\n        rightIcon: {\r\n            type: String,\r\n            default: () => defProps.formItem.rightIcon\r\n        },\r\n        // 左侧图标\r\n        leftIcon: {\r\n            type: String,\r\n            default: () => defProps.formItem.leftIcon\r\n        },\r\n        // 是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置\r\n        required: {\r\n            type: Boolean,\r\n            default: () => defProps.formItem.required\r\n        },\r\n        leftIconStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.formItem.leftIconStyle,\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,OAAO;AAAA,MACtB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,IACD,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}