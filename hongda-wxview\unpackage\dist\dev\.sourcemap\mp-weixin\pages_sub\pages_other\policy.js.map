{"version": 3, "file": "policy.js", "sources": ["pages_sub/pages_other/policy.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXHBvbGljeS52dWU"], "sourcesContent": ["<template>\n  <view class=\"policy-page\">\n    <up-navbar\n      :title=\"pageTitle\"\n      :autoBack=\"true\"\n      :safeAreaInsetTop=\"true\"\n      :fixed=\"true\"\n      :placeholder=\"true\"\n      bgColor=\"transparent\"\n      :zIndex=\"99\"\n      leftIconColor=\"#333333\"\n      :titleStyle=\"{\n        fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',\n        fontWeight: 'normal',\n        fontSize: '32rpx',\n        color: '#000000',\n        lineHeight: '44rpx'\n      }\"\n    />\n\n    <view class=\"content-wrapper\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <u-loading-icon mode=\"spinner\" color=\"#023F98\" :size=\"60\"></u-loading-icon>\n        <text class=\"loading-text\">正在加载协议内容...</text>\n      </view>\n\n      <!-- 错误状态 -->\n      <view v-else-if=\"error\" class=\"error-container\">\n        <view class=\"error-icon\">⚠️</view>\n        <text class=\"error-text\">{{ errorMessage }}</text>\n        <button class=\"retry-btn\" @click=\"loadPolicy\">重试</button>\n      </view>\n\n      <!-- 协议内容-->\n      <view v-else-if=\"policyData\" class=\"policy-content-plain\">\n        <mp-html\n          :content=\"policyData.contentHtml\"\n          :lazy-load=\"true\"\n          :selectable=\"true\"\n          :show-img-menu=\"false\"\n          :container-style=\"htmlStyle\"\n        />\n      </view>\n\n      <!-- 无数据状态 -->\n      <view v-else class=\"empty-container\">\n        <text class=\"empty-text\">暂无协议内容</text>\n      </view>\n    </view>\n  </view>\n  \n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { getLatestPolicyApi } from '@/pages_sub/pages_other/api/data/policy.js'\n\n// 定义页面名称\ndefineOptions({\n  name: 'PolicyPage'\n})\n\n// 响应式数据\nconst loading = ref(true)\nconst error = ref(false)\nconst errorMessage = ref('')\nconst policyData = ref(null)\nconst policyType = ref('')\nconst pageTitle = ref('协议详情')\n\nconst htmlStyle = 'font-size: 28rpx; line-height: 1.8; color: #111111; padding: 24rpx; background-color: #FFFFFF; word-break: break-word;'\n\n// 页面加载时获取协议类型参数\nonLoad((options) => {\n  console.log('协议页面加载，参数:', options)\n  \n  if (options.type) {\n    policyType.value = options.type\n    \n    // 设置页面标题\n    if (options.type === 'user_agreement') {\n      pageTitle.value = '用户协议'\n    } else if (options.type === 'privacy_policy') {\n      pageTitle.value = '隐私政策'\n    }\n    // 仍保留自定义导航栏，无需设置系统导航栏标题\n    \n    // 加载协议内容\n    loadPolicy()\n  } else {\n    error.value = true\n    errorMessage.value = '缺少协议类型参数'\n    loading.value = false\n  }\n})\n\n// 加载协议内容\nconst loadPolicy = async () => {\n  console.log('开始加载协议内容，类型:', policyType.value)\n  \n  loading.value = true\n  error.value = false\n  errorMessage.value = ''\n  \n  try {\n    const response = await getLatestPolicyApi(policyType.value)\n    console.log('协议内容加载响应:', response)\n    \n    if (response && response.code === 200 && response.data) {\n      policyData.value = response.data\n      \n      // 如果后端返回了标题，更新自定义导航栏标题\n      if (response.data.title) {\n        pageTitle.value = response.data.title\n      }\n      \n      console.log('协议内容加载成功')\n    } else {\n      throw new Error(response.msg || '获取协议内容失败')\n    }\n  } catch (err) {\n    console.error('协议内容加载失败:', err)\n    error.value = true\n    errorMessage.value = err.message || '网络请求失败，请检查网络连接'\n    \n    // 显示错误提示\n    uni.showToast({\n      title: errorMessage.value,\n      icon: 'none',\n      duration: 3000\n    })\n  } finally {\n    loading.value = false\n  }\n}\n\n// 格式化时间显示\nconst formatTime = (timeStr) => {\n  if (!timeStr) return '未知'\n  \n  try {\n    const date = new Date(timeStr)\n    const year = date.getFullYear()\n    const month = String(date.getMonth() + 1).padStart(2, '0')\n    const day = String(date.getDate()).padStart(2, '0')\n    return `${year}年${month}月${day}日`\n  } catch (e) {\n    console.error('时间格式化失败:', e)\n    return timeStr\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.policy-page {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #ffffff;\n}\n\n.content-wrapper {\n  flex: 1;\n  padding: 0; \n}\n\n// 加载状态\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 0;\n  \n  .loading-text {\n    margin-top: 32rpx;\n    font-size: 28rpx;\n    color: #666666;\n  }\n}\n\n// 错误状态\n.error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 0;\n  \n  .error-icon {\n    font-size: 80rpx;\n    margin-bottom: 32rpx;\n  }\n  \n  .error-text {\n    font-size: 28rpx;\n    color: #666666;\n    text-align: center;\n    margin-bottom: 48rpx;\n    line-height: 1.5;\n  }\n  \n  .retry-btn {\n    background-color: #023F98;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    padding: 24rpx 48rpx;\n    font-size: 28rpx;\n    \n    &::after {\n      border: none;\n    }\n  }\n}\n\n// 空数据状态\n.empty-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 120rpx 0;\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #999999;\n  }\n}\n\n// 纯文本内容：去除装饰，仅调整基础排版\n.policy-content-plain {\n  :deep(.mp-html) {\n    padding: 24rpx;\n    color: #111111;\n    background: #FFFFFF;\n    h1, h2, h3, h4, h5, h6 {\n      color: #000000 !important;\n      font-weight: 700 !important;\n      margin: 24rpx 0 16rpx 0 !important;\n      line-height: 1.4 !important;\n    }\n    p {\n      margin: 20rpx 0 !important;\n      line-height: 1.8 !important;\n      text-align: left !important;\n    }\n    ul, ol {\n      margin: 20rpx 0 !important;\n      padding-left: 40rpx !important;\n    }\n    li {\n      margin: 10rpx 0 !important;\n      line-height: 1.7 !important;\n    }\n    strong, b {\n      color: #000000 !important;\n      font-weight: 700 !important;\n    }\n    code {\n      background-color: #f6f6f6 !important;\n      padding: 4rpx 8rpx !important;\n      border-radius: 4rpx !important;\n      font-size: 24rpx !important;\n    }\n  }\n}\n\n// 响应式设计\n@media screen and (max-width: 750rpx) {\n  .content-wrapper {\n    padding: 0;\n  }\n  \n  .policy-content-plain {\n    :deep(.mp-html) { padding: 24rpx; }\n  }\n}\n</style>\n", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/policy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getLatestPolicyApi"], "mappings": ";;;;;;;;;;;;;;;AAwEA,MAAM,YAAY;;;;;;AAPlB,UAAM,UAAUA,cAAG,IAAC,IAAI;AACxB,UAAM,QAAQA,cAAG,IAAC,KAAK;AACvB,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,aAAaA,cAAG,IAAC,IAAI;AAC3B,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,YAAYA,cAAG,IAAC,MAAM;AAK5BC,kBAAM,OAAC,CAAC,YAAY;AAClBC,oBAAAA,6DAAY,cAAc,OAAO;AAEjC,UAAI,QAAQ,MAAM;AAChB,mBAAW,QAAQ,QAAQ;AAG3B,YAAI,QAAQ,SAAS,kBAAkB;AACrC,oBAAU,QAAQ;AAAA,QACxB,WAAe,QAAQ,SAAS,kBAAkB;AAC5C,oBAAU,QAAQ;AAAA,QACnB;AAID,mBAAY;AAAA,MAChB,OAAS;AACL,cAAM,QAAQ;AACd,qBAAa,QAAQ;AACrB,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;AAGD,UAAM,aAAa,YAAY;AAC7BA,oBAAA,MAAA,MAAA,OAAA,2CAAY,gBAAgB,WAAW,KAAK;AAE5C,cAAQ,QAAQ;AAChB,YAAM,QAAQ;AACd,mBAAa,QAAQ;AAErB,UAAI;AACF,cAAM,WAAW,MAAMC,yDAAmB,WAAW,KAAK;AAC1DD,sBAAAA,MAAY,MAAA,OAAA,2CAAA,aAAa,QAAQ;AAEjC,YAAI,YAAY,SAAS,SAAS,OAAO,SAAS,MAAM;AACtD,qBAAW,QAAQ,SAAS;AAG5B,cAAI,SAAS,KAAK,OAAO;AACvB,sBAAU,QAAQ,SAAS,KAAK;AAAA,UACjC;AAEDA,wBAAAA,8DAAY,UAAU;AAAA,QAC5B,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,2CAAc,aAAa,GAAG;AAC9B,cAAM,QAAQ;AACd,qBAAa,QAAQ,IAAI,WAAW;AAGpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,aAAa;AAAA,UACpB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvIA,GAAG,WAAW,eAAe;"}