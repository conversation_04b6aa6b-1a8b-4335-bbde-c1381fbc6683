{"version": 3, "file": "u-form.js", "sources": ["uni_modules/uview-plus/components/u-form/u-form.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWZvcm0vdS1mb3JtLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"u-form\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from \"./props.js\";\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport Schema from \"../../libs/util/async-validator\";\r\n\timport { toast, getProperty, setProperty, deepClone, error } from '../../libs/function/index';\r\n\timport test from '../../libs/function/test';\r\n\t// 去除警告信息\r\n\tSchema.warning = function() {};\r\n\t/**\r\n\t * Form 表单\r\n\t * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/form.html\r\n\t * @property {Object}\t\t\t\t\t\tmodel\t\t\t当前form的需要验证字段的集合\r\n\t * @property {Object | Function | Array}\trules\t\t\t验证规则\r\n\t * @property {String}\t\t\t\t\t\terrorType\t\t错误的提示方式，见上方说明 ( 默认 message )\r\n\t * @property {Boolean}\t\t\t\t\t\tborderBottom\t是否显示表单域的下划线边框   ( 默认 true ）\r\n\t * @property {String}\t\t\t\t\t\tlabelPosition\t表单域提示文字的位置，left-左侧，top-上方 ( 默认 'left' ）\r\n\t * @property {String | Number}\t\t\t\tlabelWidth\t\t提示文字的宽度，单位px  ( 默认 45 ）\r\n\t * @property {String}\t\t\t\t\t\tlabelAlign\t\tlable字体的对齐方式   ( 默认 ‘left' ）\r\n\t * @property {Object}\t\t\t\t\t\tlabelStyle\t\tlable的样式，对象形式\r\n\t * @example <up-form labelPosition=\"left\" :model=\"model1\" :rules=\"rules\" ref=\"form1\"></up-form>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-form\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuForm: this,\r\n\t\t\t};\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tformRules: {},\r\n\t\t\t\t// 规则校验器\r\n\t\t\t\tvalidator: {},\r\n\t\t\t\t// 原始的model快照，用于resetFields方法重置表单时使用\r\n\t\t\t\toriginalModel: null,\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听规则的变化\r\n\t\t\trules: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tthis.setRules(n);\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\t// 监听属性的变化，通知子组件u-form-item重新获取信息\r\n\t\t\tpropsChange(n) {\r\n\t\t\t\tif (this.children?.length) {\r\n\t\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\t\t// 判断子组件(u-form-item)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\r\n\t\t\t\t\t\ttypeof child.updateParentData == \"function\" &&\r\n\t\t\t\t\t\t\tchild.updateParentData();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 监听model的初始值作为重置表单的快照\r\n\t\t\tmodel: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tif (!this.originalModel) {\r\n\t\t\t\t\t\tthis.originalModel = deepClone(n);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tpropsChange() {\r\n\t\t\t\treturn [\r\n\t\t\t\t\tthis.errorType,\r\n\t\t\t\t\tthis.borderBottom,\r\n\t\t\t\t\tthis.labelPosition,\r\n\t\t\t\t\tthis.labelWidth,\r\n\t\t\t\t\tthis.labelAlign,\r\n\t\t\t\t\tthis.labelStyle,\r\n\t\t\t\t];\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 存储当前form下的所有u-form-item的实例\r\n\t\t\t// 不能定义在data中，否则微信小程序会造成循环引用而报错\r\n\t\t\tthis.children = [];\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 手动设置校验的规则，如果规则中有函数的话，微信小程序中会过滤掉，所以只能手动调用设置规则\r\n\t\t\tsetRules(rules) {\r\n\t\t\t\t// 判断是否有规则\r\n\t\t\t\tif (Object.keys(rules).length === 0) return;\r\n\t\t\t\tif (process.env.NODE_ENV === 'development' && Object.keys(this.model).length === 0) {\r\n\t\t\t\t\terror('设置rules，model必须设置！如果已经设置，请刷新页面。');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t};\r\n\t\t\t\tthis.formRules = rules;\r\n\t\t\t\t// 重新将规则赋予Validator\r\n\t\t\t\tthis.validator = new Schema(rules);\r\n\t\t\t},\r\n\t\t\t// 清空所有u-form-item组件的内容，本质上是调用了u-form-item组件中的resetField()方法\r\n\t\t\tresetFields() {\r\n\t\t\t\tthis.resetModel();\r\n\t\t\t},\r\n\t\t\t// 重置model为初始值的快照\r\n\t\t\tresetModel(obj) {\r\n\t\t\t\t// 历遍所有u-form-item，根据其prop属性，还原model的原始快照\r\n\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\tconst prop = child?.prop;\r\n\t\t\t\t\tconst value = getProperty(this.originalModel, prop);\r\n\t\t\t\t\tsetProperty(this.model, prop, value);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 清空校验结果\r\n\t\t\tclearValidate(props) {\r\n\t\t\t\tprops = [].concat(props);\r\n\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\t// 如果u-form-item的prop在props数组中，则清除对应的校验结果信息\r\n\t\t\t\t\tif (props[0] === undefined || props.includes(child.prop)) {\r\n\t\t\t\t\t\tchild.message = null;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 对部分表单字段进行校验\r\n\t\t\tasync validateField(value, callback, event = null,options) {\r\n\t\t\t\t// $nextTick是必须的，否则model的变更，可能会延后于此方法的执行\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t// 校验错误信息，返回给回调方法，用于存放所有form-item的错误信息\r\n\t\t\t\t\tconst errorsRes = [];\r\n\t\t\t\t\t// 如果为字符串，转为数组\r\n\t\t\t\t\tvalue = [].concat(value);\r\n\t\t\t\t\t// 历遍children所有子form-item\r\n\t\t\t\t\tlet promises = this.children.map(child => {\r\n\t\t\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\t\t\t// 用于存放form-item的错误信息\r\n\t\t\t\t\t\t\tconst childErrors = [];\r\n\t\t\t\t\t\t\tif (value.includes(child.prop)) {\r\n\t\t\t\t\t\t\t\t// 获取对应的属性，通过类似'a.b.c'的形式\r\n\t\t\t\t\t\t\t\tconst propertyVal = getProperty(\r\n\t\t\t\t\t\t\t\t\tthis.model,\r\n\t\t\t\t\t\t\t\t\tchild.prop\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t// 属性链数组\r\n\t\t\t\t\t\t\t\tconst propertyChain = child.prop.split(\".\");\r\n\t\t\t\t\t\t\t\tconst propertyName =\r\n\t\t\t\t\t\t\t\t\tpropertyChain[propertyChain.length - 1];\r\n\r\n\t\t\t\t\t\t\t\tlet rule = []\r\n\t\t\t\t\t\t\t\tif (child.itemRules && child.itemRules.length > 0) {\r\n\t\t\t\t\t\t\t\t\trule = child.itemRules\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\trule = this.formRules[child.prop];\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// 如果不存在对应的规则，直接返回，否则校验器会报错\r\n\t\t\t\t\t\t\t\tif (!rule) {\r\n\t\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// rule规则可为数组形式，也可为对象形式，此处拼接成为数组\r\n\t\t\t\t\t\t\t\tconst rules = [].concat(rule);\r\n\r\n\t\t\t\t\t\t\t\t// 对rules数组进行校验\r\n\t\t\t\t\t\t\t\tif (!rules.length) {\r\n\t\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tfor (let i = 0; i < rules.length; i++) {\r\n\t\t\t\t\t\t\t\t\tconst ruleItem = rules[i];\r\n\t\t\t\t\t\t\t\t\t// 将u-form-item的触发器转为数组形式\r\n\t\t\t\t\t\t\t\t\tconst trigger = [].concat(ruleItem?.trigger);\r\n\t\t\t\t\t\t\t\t\t// 如果是有传入触发事件，但是此form-item却没有配置此触发器的话，不执行校验操作\r\n\t\t\t\t\t\t\t\t\tif (event && !trigger.includes(event)) {\r\n\t\t\t\t\t\t\t\t\t\tresolve()\r\n\t\t\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// 实例化校验对象，传入构造规则\r\n\t\t\t\t\t\t\t\t\tconst validator = new Schema({\r\n\t\t\t\t\t\t\t\t\t\t[propertyName]: ruleItem,\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tvalidator.validate({\r\n\t\t\t\t\t\t\t\t\t\t[propertyName]: propertyVal,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t(errors, fields) => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (test.array(errors)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\terrors.forEach(element => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\telement.prop = child.prop;\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\terrorsRes.push(...errors);\r\n\t\t\t\t\t\t\t\t\t\t\t\tchildErrors.push(...errors);\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t//没有配置，或者配置了showErrorMsg为true时候，才修改子组件message，默认没有配置\r\n\t\t\t\t\t\t\t\t\t\t\tif(!options||options?.showErrorMsg==true){\r\n\t\t\t\t\t\t\t\t\t\t\t\tchild.message =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tchildErrors[0]?.message ? childErrors[0].message : null;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (i == (rules.length - 1)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tresolve(errorsRes)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tresolve({})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 使用Promise.all来等待所有Promise完成  \r\n\t\t\t\t\tPromise.all(promises)\r\n\t\t\t\t\t\t.then(results => {\r\n\t\t\t\t\t\t\t// 执行回调函数\r\n\t\t\t\t\t\t\ttypeof callback === \"function\" && callback(errorsRes);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\t\tconsole.error('An error occurred:', error);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 校验全部数据\r\n\t\t\t * @param {Object} options\r\n\t\t\t * @param {Boolean} options.showErrorMsg -是否显示校验信息，\r\n\t\t\t */\r\n\t\t\tvalidate(options) {\r\n\t\t\t\t// 开发环境才提示，生产环境不会提示\r\n\t\t\t\tif (process.env.NODE_ENV === 'development' && Object.keys(this.formRules).length === 0) {\r\n\t\t\t\t\terror('未设置rules，请看文档说明！如果已经设置，请刷新页面。');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\t// $nextTick是必须的，否则model的变更，可能会延后于validate方法\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t// 获取所有form-item的prop，交给validateField方法进行校验\r\n\t\t\t\t\t\tconst formItemProps = this.children.map(\r\n\t\t\t\t\t\t\t(item) => item.prop\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\t// console.log(formItemProps)\r\n\t\t\t\t\t\tthis.validateField(formItemProps, (errors) => {\r\n\t\t\t\t\t\t\tif(errors.length) {\r\n\t\t\t\t\t\t\t\t// 如果错误提示方式为toast，则进行提示\r\n\t\t\t\t\t\t\t\tthis.errorType === 'toast' && toast(errors[0].message)\r\n\t\t\t\t\t\t\t\treject(errors)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tresolve(true)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},null,options);\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-form/u-form.vue'\nwx.createComponent(Component)"], "names": ["<PERSON><PERSON><PERSON>", "mpMixin", "mixin", "props", "deepClone", "error", "getProperty", "setProperty", "test", "uni", "toast"], "mappings": ";;;;;;;;AAcCA,+CAAA,OAAO,UAAU,WAAW;AAAC;AAe7B,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACC,yCAAAA,SAASC,uCAAA,OAAOC,kDAAK;AAAA,EAC9B,UAAU;AACF,WAAA;AAAA,MACN,OAAO;AAAA,IAAA;AAAA,EAET;AAAA,EACA,OAAO;AACC,WAAA;AAAA,MACN,WAAW,CAAC;AAAA;AAAA,MAEZ,WAAW,CAAC;AAAA;AAAA,MAEZ,eAAe;AAAA,IAAA;AAAA,EAEjB;AAAA,EACA,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,WAAW;AAAA,MACX,QAAQ,GAAG;AACV,aAAK,SAAS,CAAC;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAEA,YAAY,GAAG;;AACV,WAAA,UAAK,aAAL,mBAAe,QAAQ;AACrB,aAAA,SAAS,IAAI,CAAC,UAAU;AAE5B,iBAAO,MAAM,oBAAoB,cAChC,MAAM,iBAAiB;AAAA,QAAA,CACxB;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAEA,OAAO;AAAA,MACN,WAAW;AAAA,MACX,QAAQ,GAAG;AACN,YAAA,CAAC,KAAK,eAAe;AACnB,eAAA,gBAAgBC,oDAAU,CAAC;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,UAAU;AAAA,IACT,cAAc;AACN,aAAA;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MAAA;AAAA,IAEP;AAAA,EACD;AAAA,EACA,UAAU;AAGT,SAAK,WAAW;EACjB;AAAA,EACA,SAAS;AAAA;AAAA,IAER,SAAS,OAAO;AAEf,UAAI,OAAO,KAAK,KAAK,EAAE,WAAW;AAAG;AACrC,UAA8C,OAAO,KAAK,KAAK,KAAK,EAAE,WAAW,GAAG;AACnFC,kDAAA,MAAM,iCAAiC;AACvC;AAAA,MACD;AACA,WAAK,YAAY;AAEZ,WAAA,YAAY,IAAIL,+CAAA,OAAO,KAAK;AAAA,IAClC;AAAA;AAAA,IAEA,cAAc;AACb,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA,IAEA,WAAW,KAAK;AAEV,WAAA,SAAS,IAAI,CAAC,UAAU;AAC5B,cAAM,OAAO,+BAAO;AACpB,cAAM,QAAQM,0CAAA,YAAY,KAAK,eAAe,IAAI;AACtCC,kDAAAA,YAAA,KAAK,OAAO,MAAM,KAAK;AAAA,MAAA,CACnC;AAAA,IACF;AAAA;AAAA,IAEA,cAAcJ,QAAO;AACpBA,eAAQ,CAAG,EAAA,OAAOA,MAAK;AAClB,WAAA,SAAS,IAAI,CAAC,UAAU;AAExBA,YAAAA,OAAM,CAAC,MAAM,UAAaA,OAAM,SAAS,MAAM,IAAI,GAAG;AACzD,gBAAM,UAAU;AAAA,QACjB;AAAA,MAAA,CACA;AAAA,IACF;AAAA;AAAA,IAEA,MAAM,cAAc,OAAO,UAAU,QAAQ,MAAK,SAAS;AAE1D,WAAK,UAAU,MAAM;AAEpB,cAAM,YAAY,CAAA;AAEV,gBAAA,CAAG,EAAA,OAAO,KAAK;AAEvB,YAAI,WAAW,KAAK,SAAS,IAAI,CAAS,UAAA;AACzC,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,kBAAM,cAAc,CAAA;AACpB,gBAAI,MAAM,SAAS,MAAM,IAAI,GAAG;AAE/B,oBAAM,cAAcG,0CAAA;AAAA,gBACnB,KAAK;AAAA,gBACL,MAAM;AAAA,cAAA;AAGP,oBAAM,gBAAgB,MAAM,KAAK,MAAM,GAAG;AAC1C,oBAAM,eACL,cAAc,cAAc,SAAS,CAAC;AAEvC,kBAAI,OAAO,CAAA;AACX,kBAAI,MAAM,aAAa,MAAM,UAAU,SAAS,GAAG;AAClD,uBAAO,MAAM;AAAA,cAAA,OACP;AACC,uBAAA,KAAK,UAAU,MAAM,IAAI;AAAA,cACjC;AAEA,kBAAI,CAAC,MAAM;AACF;AACR;AAAA,cACD;AAEA,oBAAM,QAAQ,CAAA,EAAG,OAAO,IAAI;AAGxB,kBAAA,CAAC,MAAM,QAAQ;AACV;cACT;AACA,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAChC,sBAAA,WAAW,MAAM,CAAC;AAExB,sBAAM,UAAU,CAAG,EAAA,OAAO,qCAAU,OAAO;AAE3C,oBAAI,SAAS,CAAC,QAAQ,SAAS,KAAK,GAAG;AAC9B;AACR;AAAA,gBACD;AAEM,sBAAA,YAAY,IAAIN,sDAAO;AAAA,kBAC5B,CAAC,YAAY,GAAG;AAAA,gBAAA,CAChB;AACS,0BAAA;AAAA,kBAAS;AAAA,oBAClB,CAAC,YAAY,GAAG;AAAA,kBACjB;AAAA,kBACC,CAAC,QAAQ,WAAW;;AACf,wBAAAQ,yCAAA,KAAK,MAAM,MAAM,GAAG;AACvB,6BAAO,QAAQ,CAAW,YAAA;AACzB,gCAAQ,OAAO,MAAM;AAAA,sBAAA,CACrB;AACS,gCAAA,KAAK,GAAG,MAAM;AACZ,kCAAA,KAAK,GAAG,MAAM;AAAA,oBAC3B;AAEA,wBAAG,CAAC,YAAS,mCAAS,iBAAc,MAAK;AAClC,4BAAA,YACL,iBAAY,CAAC,MAAb,mBAAgB,WAAU,YAAY,CAAC,EAAE,UAAU;AAAA,oBACrD;AACI,wBAAA,KAAM,MAAM,SAAS,GAAI;AAC5B,8BAAQ,SAAS;AAAA,oBAClB;AAAA,kBACD;AAAA,gBAAA;AAAA,cAEF;AAAA,YAAA,OACM;AACN,sBAAQ,CAAE,CAAA;AAAA,YACX;AAAA,UAAA,CACA;AAAA,QAAA,CACD;AAGD,gBAAQ,IAAI,QAAQ,EAClB,KAAK,CAAW,YAAA;AAET,iBAAA,aAAa,cAAc,SAAS,SAAS;AAAA,QAAA,CACpD,EACA,MAAM,CAAAH,WAAS;AACfI,wBAAA,MAAc,MAAA,SAAA,8DAAA,sBAAsBJ,MAAK;AAAA,QAAA,CACzC;AAAA,MAAA,CACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,SAAS,SAAS;AAEjB,UAA8C,OAAO,KAAK,KAAK,SAAS,EAAE,WAAW,GAAG;AACvFA,kDAAA,MAAM,+BAA+B;AACrC;AAAA,MACD;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,aAAK,UAAU,MAAM;AAEd,gBAAA,gBAAgB,KAAK,SAAS;AAAA,YACnC,CAAC,SAAS,KAAK;AAAA,UAAA;AAGX,eAAA,cAAc,eAAe,CAAC,WAAW;AAC7C,gBAAG,OAAO,QAAQ;AAEjB,mBAAK,cAAc,WAAWK,0CAAAA,MAAM,OAAO,CAAC,EAAE,OAAO;AACrD,qBAAO,MAAM;AAAA,YAAA,OACP;AACN,sBAAQ,IAAI;AAAA,YACb;AAAA,UAAA,GACC,MAAK,OAAO;AAAA,QAAA,CACd;AAAA,MAAA,CACD;AAAA,IACF;AAAA,EACD;AACD;;;;;AC5PD,GAAG,gBAAgB,SAAS;"}