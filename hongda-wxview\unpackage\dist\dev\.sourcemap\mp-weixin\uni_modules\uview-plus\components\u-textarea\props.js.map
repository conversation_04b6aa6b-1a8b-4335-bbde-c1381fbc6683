{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-textarea/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\n\r\nexport const props = defineMixin({\r\n\tprops: {\r\n\t\t// 输入框的内容\r\n\t\tvalue: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.value\r\n\t\t},\r\n\t\t// 输入框的内容\r\n\t\tmodelValue: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.value\r\n\t\t},\r\n\t\t// 输入框为空时占位符\r\n\t\tplaceholder: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.placeholder\r\n\t\t},\r\n\t\t// 指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/\r\n\t\tplaceholderClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.placeholderClass\r\n\t\t},\r\n\t\t// 指定placeholder的样式\r\n\t\tplaceholderStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => defProps.input.placeholderStyle\r\n\t\t},\r\n\t\t// 输入框高度\r\n\t\theight: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.height\r\n\t\t},\r\n\t\t// 设置键盘右下角按钮的文字，仅微信小程序，App-vue和H5有效\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.textarea.confirmType\r\n\t\t},\r\n\t\t// 是否禁用\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.disabled\r\n\t\t},\r\n\t\t// 是否显示统计字数\r\n\t\tcount: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.count\r\n\t\t},\r\n\t\t// 是否自动获取焦点，nvue不支持，H5取决于浏览器的实现\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.focus\r\n\t\t},\r\n\t\t// 是否自动增加高度\r\n\t\tautoHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.autoHeight\r\n\t\t},\r\n\t\t// 如果textarea是在一个position:fixed的区域，需要显示指定属性fixed为true\r\n\t\tfixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.fixed\r\n\t\t},\r\n\t\t// 指定光标与键盘的距离\r\n\t\tcursorSpacing: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: () => defProps.textarea.cursorSpacing\r\n\t\t},\r\n\t\t// 指定focus时的光标位置\r\n\t\tcursor: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.cursor\r\n\t\t},\r\n\t\t// 是否显示键盘上方带有”完成“按钮那一栏，\r\n\t\tshowConfirmBar: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.showConfirmBar\r\n\t\t},\r\n\t\t// 光标起始位置，自动聚焦时有效，需与selection-end搭配使用\r\n\t\tselectionStart: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: () => defProps.textarea.selectionStart\r\n\t\t},\r\n\t\t// 光标结束位置，自动聚焦时有效，需与selection-start搭配使用\r\n\t\tselectionEnd: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: () => defProps.textarea.selectionEnd\r\n\t\t},\r\n\t\t// 键盘弹起时，是否自动上推页面\r\n\t\tadjustPosition: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.adjustPosition\r\n\t\t},\r\n\t\t// 是否去掉 iOS 下的默认内边距，只微信小程序有效\r\n\t\tdisableDefaultPadding: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.disableDefaultPadding\r\n\t\t},\r\n\t\t// focus时，点击页面的时候不收起键盘，只微信小程序有效\r\n\t\tholdKeyboard: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.textarea.holdKeyboard\r\n\t\t},\r\n\t\t// 最大输入长度，设置为 -1 的时候不限制最大长度\r\n\t\tmaxlength: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.textarea.maxlength\r\n\t\t},\r\n\t\t// 边框类型，surround-四周边框，bottom-底部边框\r\n\t\tborder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.textarea.border\r\n\t\t},\r\n\t\t// 用于处理或者过滤输入框内容的方法\r\n\t\tformatter: {\r\n\t\t\ttype: [Function, null],\r\n\t\t\tdefault: () => defProps.textarea.formatter\r\n\t\t},\r\n\t\t// 是否忽略组件内对文本合成系统事件的处理\r\n\t\tignoreCompositionEvent: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t}\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,UAAU,IAAI;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AACF,CAAC;;"}