<view class="event-list-page data-v-8e954d49"><view class="header-wrapper data-v-8e954d49"><image class="header-bg data-v-8e954d49" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-8e954d49"><view class="navbar-left data-v-8e954d49" bindtap="{{c}}"><up-icon wx:if="{{b}}" class="data-v-8e954d49" u-i="8e954d49-0" bind:__l="__l" u-p="{{b}}"></up-icon></view><view class="navbar-title data-v-8e954d49"><text class="title-text data-v-8e954d49">热门活动列表</text></view><view class="navbar-right data-v-8e954d49"></view></view><view class="top-controls data-v-8e954d49"><up-subsection wx:if="{{e}}" class="data-v-8e954d49" bindchange="{{d}}" u-i="8e954d49-1" bind:__l="__l" u-p="{{e}}"></up-subsection><view class="search-wrapper data-v-8e954d49"><custom-search-box wx:if="{{i}}" class="data-v-8e954d49" bindsearch="{{f}}" bindinput="{{g}}" u-i="8e954d49-2" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></custom-search-box></view></view></view><view wx:if="{{j}}" class="filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{n}}"><text class="filter-text data-v-8e954d49">{{k}}</text><up-icon wx:if="{{m}}" class="{{['data-v-8e954d49', l && 'rotate-180']}}" u-i="8e954d49-3" bind:__l="__l" u-p="{{m}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{r}}"><text class="filter-text data-v-8e954d49">{{o}}</text><up-icon wx:if="{{q}}" class="{{['data-v-8e954d49', p && 'rotate-180']}}" u-i="8e954d49-4" bind:__l="__l" u-p="{{q}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{w}}"><text class="filter-text data-v-8e954d49">{{s}}</text><up-icon wx:if="{{v}}" class="{{['data-v-8e954d49', t && 'rotate-180']}}" u-i="8e954d49-5" bind:__l="__l" u-p="{{v}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{A}}"><text class="filter-text data-v-8e954d49">{{x}}</text><up-icon wx:if="{{z}}" class="{{['data-v-8e954d49', y && 'rotate-180']}}" u-i="8e954d49-6" bind:__l="__l" u-p="{{z}}"></up-icon></view></view><view wx:if="{{B}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">排序</text><view class="option-grid data-v-8e954d49"><view wx:for="{{C}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{D}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{E}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{F}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">所属地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{G}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{H}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{I}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{J}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{K}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{L}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{M}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{N}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">全部状态</text><view class="option-grid data-v-8e954d49"><view wx:for="{{O}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{P}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{Q}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{R}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{Z}}" refresher-enabled refresher-triggered="{{aa}}" bindrefresherrefresh="{{ab}}"><view wx:if="{{S}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{T}}" class="data-v-8e954d49" u-i="8e954d49-7" bind:__l="__l" u-p="{{T}}"></up-empty><view wx:if="{{U}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{W}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{V}}" u-i="8e954d49-8" bind:__l="__l" u-p="{{W}}"> 重新加载 </up-button></view></view><event-card wx:for="{{X}}" wx:for-item="event" wx:key="a" class="data-v-8e954d49" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-8e954d49"><up-loadmore wx:if="{{Y}}" class="data-v-8e954d49" u-i="8e954d49-10" bind:__l="__l" u-p="{{Y}}"/></view></scroll-view><view wx:if="{{ac}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{ag}}"><text class="filter-text data-v-8e954d49">{{ad}}</text><up-icon wx:if="{{af}}" class="{{['data-v-8e954d49', ae && 'rotate-180']}}" u-i="8e954d49-11" bind:__l="__l" u-p="{{af}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{ak}}"><text class="filter-text data-v-8e954d49">{{ah}}</text><up-icon wx:if="{{aj}}" class="{{['data-v-8e954d49', ai && 'rotate-180']}}" u-i="8e954d49-12" bind:__l="__l" u-p="{{aj}}"></up-icon></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view></view><view wx:if="{{al}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">所属地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{am}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{an}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{ao}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{ap}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{aq}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{ar}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{as}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{at}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{aD}}" refresher-enabled refresher-triggered="{{aE}}" bindrefresherrefresh="{{aF}}"><view wx:if="{{av}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{aw}}" class="data-v-8e954d49" u-i="8e954d49-13" bind:__l="__l" u-p="{{aw}}"></up-empty><view wx:if="{{ax}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{az}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{ay}}" u-i="8e954d49-14" bind:__l="__l" u-p="{{az}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-8e954d49" bindclickItem="{{aA}}" bindviewMore="{{aB}}" u-i="8e954d49-15" bind:__l="__l" u-p="{{aC||''}}"/><view class="calendar-bottom-safe data-v-8e954d49"/></scroll-view><custom-tab-bar wx:if="{{aG}}" class="data-v-8e954d49" u-i="8e954d49-16" bind:__l="__l" u-p="{{aG}}"/></view>