{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-swiper-indicator/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 轮播的长度\r\n        length: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiperIndicator.length\r\n        },\r\n        // 当前处于活动状态的轮播的索引\r\n        current: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiperIndicator.current\r\n        },\r\n        // 指示器非激活颜色\r\n        indicatorActiveColor: {\r\n            type: String,\r\n            default: () => defProps.swiperIndicator.indicatorActiveColor\r\n        },\r\n        // 指示器的激活颜色\r\n        indicatorInactiveColor: {\r\n            type: String,\r\n            default: () => defProps.swiperIndicator.indicatorInactiveColor\r\n        },\r\n\t\t// 指示器模式，line-线型，dot-点型\r\n\t\tindicatorMode: {\r\n\t\t    type: String,\r\n\t\t    default: () => defProps.swiperIndicator.indicatorMode\r\n\t\t}\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,gBAAgB;AAAA,IAC3C;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,gBAAgB;AAAA,IAC3C;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,gBAAgB;AAAA,IAC3C;AAAA;AAAA,IAED,wBAAwB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,gBAAgB;AAAA,IAC3C;AAAA;AAAA,IAEP,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,gBAAgB;AAAA,IAC3C;AAAA,EACE;AACL,CAAC;;"}