{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-avatar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nimport test from '../../libs/function/test';\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 头像图片路径(不能为相对路径)\r\n        src: {\r\n            type: String,\r\n            default: () => defProps.avatar.src\r\n        },\r\n        // 头像形状，circle-圆形，square-方形\r\n        shape: {\r\n            type: String,\r\n            default: () => defProps.avatar.shape\r\n        },\r\n        // 头像尺寸\r\n        size: {\r\n            type: [String, Number],\r\n            default: () => defProps.avatar.size\r\n        },\r\n        // 裁剪模式\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.avatar.mode\r\n        },\r\n        // 显示的文字\r\n        text: {\r\n            type: String,\r\n            default: () => defProps.avatar.text\r\n        },\r\n        // 背景色\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.avatar.bgColor\r\n        },\r\n        // 文字颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.avatar.color\r\n        },\r\n        // 文字大小\r\n        fontSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.avatar.fontSize\r\n        },\r\n        // 显示的图标\r\n        icon: {\r\n            type: String,\r\n            default: () => defProps.avatar.icon\r\n        },\r\n        // 显示小程序头像，只对百度，微信，QQ小程序有效\r\n        mpAvatar: {\r\n            type: Boolean,\r\n            default: () => defProps.avatar.mpAvatar\r\n        },\r\n        // 是否使用随机背景色\r\n        randomBgColor: {\r\n            type: Boolean,\r\n            default: () => defProps.avatar.randomBgColor\r\n        },\r\n        // 加载失败的默认头像(组件有内置默认图片)\r\n        defaultUrl: {\r\n            type: String,\r\n            default: () => defProps.avatar.defaultUrl\r\n        },\r\n        // 如果配置了randomBgColor为true，且配置了此值，则从默认的背景色数组中取出对应索引的颜色值，取值0-19之间\r\n        colorIndex: {\r\n            type: [String, Number],\r\n            // 校验参数规则，索引在0-19之间\r\n            validator(n) {\r\n                return test.range(n, [0, 19]) || n === ''\r\n            },\r\n            default: () => defProps.avatar.colorIndex\r\n        },\r\n        // 组件标识符\r\n        name: {\r\n            type: String,\r\n            default: () => defProps.avatar.name\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps", "test"], "mappings": ";;;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA;AAAA,MAErB,UAAU,GAAG;AACT,eAAOC,yCAAI,KAAC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,MAAM;AAAA,MAC1C;AAAA,MACD,SAAS,MAAMD,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}