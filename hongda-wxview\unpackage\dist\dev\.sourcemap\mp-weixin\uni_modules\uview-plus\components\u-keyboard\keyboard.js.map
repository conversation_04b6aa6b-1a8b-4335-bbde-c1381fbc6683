{"version": 3, "file": "keyboard.js", "sources": ["uni_modules/uview-plus/components/u-keyboard/keyboard.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:07:49\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/keyboard.js\r\n */\r\nexport default {\r\n    // 键盘组件\r\n    keyboard: {\r\n        mode: 'number',\r\n        dotDisabled: false,\r\n        tooltip: true,\r\n        showTips: true,\r\n        tips: '',\r\n        showCancel: true,\r\n        showConfirm: true,\r\n        random: false,\r\n        safeAreaInsetBottom: true,\r\n        closeOnClickOverlay: true,\r\n        show: false,\r\n        overlay: true,\r\n        zIndex: 10075,\r\n        cancelText: '取消',\r\n        confirmText: '确定',\r\n        autoChange: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,EACf;AACL;;"}