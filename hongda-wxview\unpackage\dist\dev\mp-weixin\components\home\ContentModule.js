"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_countryPolicy = require("../../api/content/countryPolicy.js");
if (!Array) {
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  (_easycom_u_loading_icon2 + _easycom_u_icon2 + _easycom_u_empty2)();
}
const _easycom_u_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_u_loading_icon + _easycom_u_icon + _easycom_u_empty)();
}
const _sfc_main = {
  __name: "ContentModule",
  props: {
    countryId: {
      type: [Number, String],
      required: true
    },
    policyType: {
      type: String,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const allArticles = common_vendor.ref([]);
    const categoryList = common_vendor.ref([]);
    const activeCategoryIndex = common_vendor.ref(0);
    const loading = common_vendor.ref(true);
    const filteredArticleList = common_vendor.computed(() => {
      if (loading.value || categoryList.value.length === 0 || !categoryList.value[activeCategoryIndex.value]) {
        return [];
      }
      const currentCategoryName = categoryList.value[activeCategoryIndex.value].name;
      if (currentCategoryName === "全部") {
        return allArticles.value;
      }
      return allArticles.value.filter((item) => item.categoryName === currentCategoryName);
    });
    const fetchData = async () => {
      loading.value = true;
      if (!props.countryId || !props.policyType) {
        allArticles.value = [];
        categoryList.value = [];
        loading.value = false;
        return;
      }
      try {
        const res = await api_content_countryPolicy.listCountryPolicyArticle({
          countryId: props.countryId,
          policyType: props.policyType,
          pageNum: 1,
          pageSize: 100
        });
        allArticles.value = res.rows || [];
        const categories = [...new Set(allArticles.value.map((item) => item.categoryName).filter(Boolean))];
        categoryList.value = [{ name: "全部" }, ...categories.map((name) => ({ name }))];
        activeCategoryIndex.value = 0;
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", `获取${props.policyType}政策文章失败:`, error);
        allArticles.value = [];
        categoryList.value = [];
      } finally {
        loading.value = false;
      }
    };
    const onCategoryChange = (index) => {
      activeCategoryIndex.value = index;
    };
    const goToArticleDetail = (articleId) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_country/policy_detail?id=${articleId}`
      });
    };
    common_vendor.watch(() => props.policyType, (newVal, oldVal) => {
      if (newVal && newVal !== oldVal) {
        fetchData();
      }
    });
    common_vendor.onMounted(() => {
      fetchData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(categoryList.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category.name),
            b: index,
            c: activeCategoryIndex.value === index ? 1 : "",
            d: common_vendor.o(($event) => onCategoryChange(index), index)
          };
        }),
        b: loading.value
      }, loading.value ? {
        c: common_vendor.p({
          mode: "circle",
          text: "加载中...",
          size: "24"
        })
      } : filteredArticleList.value.length > 0 ? {
        e: common_vendor.f(filteredArticleList.value, (article, k0, i0) => {
          return {
            a: common_vendor.t(article.title),
            b: "6f0ef454-1-" + i0,
            c: article.articleId,
            d: common_vendor.o(($event) => goToArticleDetail(article.articleId), article.articleId)
          };
        }),
        f: common_vendor.p({
          name: "arrow-right",
          color: "#BFBFBF",
          size: "16"
        })
      } : {
        g: common_vendor.p({
          mode: "list",
          text: "暂无相关内容"
        })
      }, {
        d: filteredArticleList.value.length > 0
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6f0ef454"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
