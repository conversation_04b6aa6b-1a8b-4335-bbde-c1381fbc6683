{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-toolbar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否展示工具条\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.toolbar.show\r\n        },\r\n        // 取消按钮的文字\r\n        cancelText: {\r\n            type: String,\r\n            default: () => defProps.toolbar.cancelText\r\n        },\r\n        // 确认按钮的文字\r\n        confirmText: {\r\n            type: String,\r\n            default: () => defProps.toolbar.confirmText\r\n        },\r\n        // 取消按钮的颜色\r\n        cancelColor: {\r\n            type: String,\r\n            default: () => defProps.toolbar.cancelColor\r\n        },\r\n        // 确认按钮的颜色\r\n        confirmColor: {\r\n            type: String,\r\n            default: () => defProps.toolbar.confirmColor\r\n        },\r\n        // 标题文字\r\n        title: {\r\n            type: String,\r\n            default: () => defProps.toolbar.title\r\n        },\r\n        // 开启右侧插槽\r\n        rightSlot: {\r\n            type: Boolean,\r\n            default: false\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,EACJ;AACL,CAAC;;"}