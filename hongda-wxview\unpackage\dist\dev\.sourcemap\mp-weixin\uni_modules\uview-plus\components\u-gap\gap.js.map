{"version": 3, "file": "gap.js", "sources": ["uni_modules/uview-plus/components/u-gap/gap.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:05:25\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gap.js\r\n */\r\nexport default {\r\n    // gap组件\r\n    gap: {\r\n        bgColor: 'transparent',\r\n        height: 20,\r\n        marginTop: 0,\r\n        marginBottom: 0,\r\n        customStyle: {}\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,MAAA;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa,CAAE;AAAA,EAClB;AACL;;"}