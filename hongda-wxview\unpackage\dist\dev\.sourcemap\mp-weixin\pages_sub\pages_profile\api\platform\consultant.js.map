{"version": 3, "file": "consultant.js", "sources": ["pages_sub/pages_profile/api/platform/consultant.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * @description 获取当前展示的顾问信息\r\n * @returns {Promise}\r\n */\r\nexport function getDisplayConsultantApi() {\r\n    return http.get('/consultant/display');\r\n}\r\n\r\n\r\n"], "names": ["http"], "mappings": ";;AAMO,SAAS,0BAA0B;AACtC,SAAOA,cAAI,KAAC,IAAI,qBAAqB;AACzC;;"}