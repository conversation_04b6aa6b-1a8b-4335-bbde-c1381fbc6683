{"version": 3, "file": "detail.js", "sources": ["pages_sub/pages_event/detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2V2ZW50XGRldGFpbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"event-detail-page\">\n    <!-- Top Navigation Bar -->\n    <up-navbar\n      title=\"活动详情\"\n      :fixed=\"true\"\n      :safeAreaInsetTop=\"true\"\n      bgColor=\"#ffffff\"\n      leftIcon=\"arrow-left\"\n      leftIconColor=\"#333333\"\n      titleStyle=\"color: #333333; font-weight: bold;\"\n      @leftClick=\"handleNavBack\"\n    >\n    </up-navbar>\n\n    <!-- 第一层防护：全局加载状态 -->\n    <view class=\"loading-container\" v-if=\"isLoading\">\n      <up-loading-page loadingText=\"正在加载活动详情...\" loadingMode=\"spinner\"></up-loading-page>\n    </view>\n\n    <!-- 第二层防护：页面主要内容，只有在数据完全准备好时才渲染 -->\n    <scroll-view scroll-y class=\"scroll-content\" :style=\"{ paddingTop: totalTopPadding }\" v-if=\"!isLoading && eventDetail\">\n      <!-- Top Event Image -->\n      <view class=\"top-image-container\">\n        <up-image :src=\"getFullImageUrl(eventDetail.coverImageUrl)\" mode=\"aspectFill\" height=\"420rpx\" width=\"100%\"></up-image>\n      </view>\n\n      <!-- Middle Information Card -->\n      <EventInfoCard :eventDetail=\"eventDetail\" />\n\n      <!-- 活动详情富文本区域 -->\n      <EventDetailContent :eventDetail=\"eventDetail\" />\n\n      <!-- 底部留白，为固定操作栏腾出空间 -->\n      <view class=\"bottom-spacer\"></view>\n    </scroll-view>\n\n    <!-- 底部操作栏：只有在数据加载完成且存在时才显示 -->\n    <EventActionBar\n      v-if=\"!isLoading && eventDetail\"\n      :eventDetail=\"eventDetail\"\n      :isLoading=\"isLoading\"\n      :registrationStatus=\"registrationStatus\"\n      :isButtonDisabled=\"isButtonDisabled\"\n      :buttonText=\"buttonText\"\n      @share=\"handleShare\"\n      @register=\"handleRegistration\"\n    />\n  </view>\n</template>\n\n<script setup>\nimport {computed, onMounted, ref, watch} from 'vue';\nimport {onLoad, onShareAppMessage, onShow, onUnload} from '@dcloudio/uni-app';\nimport {getEventDetailApi} from '@/api/data/event.js';\nimport {checkRegistrationStatusApi} from '@/pages_sub/pages_event/api/data/registration.js';\nimport {getFullImageUrl} from '@/utils/image.js';\nimport EventInfoCard from '@event/EventInfoCard.vue'\nimport EventDetailContent from '@event/EventDetailContent.vue'\nimport EventActionBar from '@event/EventActionBar.vue'\n\nconst eventId = ref(null);\nconst eventDetail = ref(null);\n//报名状态从 eventDetail 中完全分离出来\n//registrationStatus可选值：'loading', 'unregistered', 'registered', 'not_logged_in', 'error'\nconst registrationStatus = ref('loading');\nconst isLoading = ref(true);\nconst loginCheckTrigger = ref(0); // 新增：强制触发登录状态重新计算的标记\n\n//动态获取状态栏高度\nconst statusBarHeight = ref(0);\nconst navBarHeight = ref(44); \n\n// 计算总的顶部padding\nconst totalTopPadding = computed(() => {\n  return `${statusBarHeight.value + navBarHeight.value}px`;\n});\n\nonMounted(() => {\n  // 获取系统信息\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight || 0;\n});\n\n// 从本地存储获取用户token\nconst getUserToken = () => {\n  try {\n    return uni.getStorageSync('token') || null;\n  } catch (e) {\n    return null;\n  }\n};\n\n// 检查用户是否已登录\nconst isLoggedIn = computed(() => {\n  // 通过 loginCheckTrigger 强制重新计算\n  loginCheckTrigger.value; // eslint-disable-line no-unused-expressions\n  return !!getUserToken();\n});\n\n// 🔄 状态分离重构：基于独立的 registrationStatus 计算按钮文字\nconst buttonText = computed(() => {\n  if (!eventDetail.value) return '加载中...';\n  \n  // 根据独立的 registrationStatus 状态决定按钮文字\n  switch (registrationStatus.value) {\n    case 'loading':\n      return '检查状态中...';\n    case 'registered':\n      return '已报名';\n    case 'not_logged_in':\n      return '立即报名';\n    case 'unregistered':\n      // 根据活动状态判断\n      if (eventDetail.value.status === 2 || eventDetail.value.status === 3) return '活动已结束';\n      if (eventDetail.value.status === 4) return '活动进行中';\n      return '立即报名';\n    case 'error':\n      return '立即报名'; // 出错时默认显示报名按钮\n    default:\n      return '立即报名';\n  }\n});\n\n// 状态分离重构：基于独立状态计算按钮是否禁用\nconst isButtonDisabled = computed(() => {\n  if (!eventDetail.value) return true;\n  \n  // 如果状态还在加载中，禁用按钮\n  if (registrationStatus.value === 'loading') return true;\n  \n  // 根据活动状态判断是否禁用\n  return eventDetail.value.status === 2 || eventDetail.value.status === 3;\n});\n\n// 子组件内已处理：时间、剩余名额和富文本\n\n// 智能导航回退处理函数\nconst handleNavBack = () => {\n  console.log('=== 开始智能导航回退处理 ===');\n  \n  // 第一步：尝试正常回退\n  uni.navigateBack({\n    success: () => {\n      console.log('正常回退成功');\n    },\n    fail: (err) => {\n      console.warn('正常回退失败:', err);\n      \n      // 第二步：尝试跳转到活动列表页面\n      uni.navigateTo({\n        url: '/pages/event/index',\n        success: () => {\n          console.log('跳转到活动列表页面成功');\n        },\n        fail: (err2) => {\n          console.warn('跳转到活动列表页面失败:', err2);\n          \n          // 第三步：最后的兜底方案，跳转到首页\n          uni.switchTab({\n            url: '/pages/index/index',\n            success: () => {\n              console.log('跳转到首页成功');\n            },\n            fail: (err3) => {\n              console.error('所有导航方案都失败了:', err3);\n              uni.showToast({\n                title: '导航失败，请重新打开小程序',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\n\n// 🔄 状态分离重构：新的独立报名状态检查函数\nconst fetchRegistrationStatus = async () => {\n  console.log('=== 开始独立的报名状态检查 ===');\n  \n  try {\n    // 1. 检查登录状态\n    const token = getUserToken();\n    if (!token) {\n      console.log('用户未登录，设置状态为 not_logged_in');\n      registrationStatus.value = 'not_logged_in';\n      return;\n    }\n\n    // 2. 检查必要的前置条件\n    if (!eventId.value) {\n      console.warn('缺少 eventId，无法检查报名状态');\n      registrationStatus.value = 'error';\n      return;\n    }\n\n    console.log('前置条件满足，开始调用 API 检查报名状态...');\n    \n    // 3. 先检查本地存储的报名状态（快速反馈）\n    try {\n      const localRegistrationStatus = uni.getStorageSync('registrationStatus') || {};\n      const localStatus = localRegistrationStatus[eventId.value];\n      if (localStatus && localStatus.isRegistered) {\n        console.log('从本地存储发现已报名状态，立即更新 UI');\n        registrationStatus.value = 'registered';\n      }\n    } catch (error) {\n      console.warn('读取本地报名状态失败:', error);\n    }\n\n    // 4. 调用 API 获取最新状态\n    const statusRes = await checkRegistrationStatusApi(eventId.value);\n    \n    if (statusRes.code === 200) {\n      // 修复：根据后端 AjaxResult 的实际结构访问数据\n      // AjaxResult 返回的数据直接在根级别：{code: 200, msg: \"操作成功\", isRegistered: true/false}\n      const isRegistered = statusRes.isRegistered || false;\n      console.log('获取报名状态成功:', isRegistered);\n      \n      const newStatus = isRegistered ? 'registered' : 'unregistered';\n      \n      // 5. 更新状态（只更新 registrationStatus，不涉及 eventDetail）\n      if (registrationStatus.value !== newStatus) {\n        console.log('报名状态发生变化:', registrationStatus.value, '->', newStatus);\n        registrationStatus.value = newStatus;\n        \n        // 显示状态更新提示\n        if (newStatus === 'registered' && registrationStatus.value !== 'registered') {\n          uni.$u.toast('报名状态已更新');\n        }\n      } else {\n        console.log('报名状态无变化，保持现状');\n      }\n      \n    } else {\n      console.warn('检查报名状态 API 返回错误:', statusRes);\n      registrationStatus.value = 'error';\n    }\n\n  } catch (error) {\n    console.error('报名状态检查失败:', error);\n    registrationStatus.value = 'error';\n  }\n  \n  console.log('=== 报名状态检查完成，最终状态:', registrationStatus.value, ' ===');\n};\n\n// 页面加载\nonLoad(async (options) => {\n  eventId.value = options.id;\n  if (!eventId.value) {\n    uni.$u.toast('活动不存在');\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n    return;\n  }\n\n  // 【数据实时更新方案】监听全局数据变化事件\n  uni.$on('dataChanged', async () => {\n    console.log('📩 收到数据变化事件，重新获取活动详情...');\n    \n    if (eventId.value) {\n      try {\n        console.log('开始重新获取活动详情数据...');\n        \n        // 重新获取活动详情（包含最新的报名人数）\n        const detailRes = await getEventDetailApi(eventId.value);\n        \n        if (detailRes.code === 200) {\n          // 更新活动详情数据\n          const rawData = detailRes.data;\n          rawData.details = rawData.details || '';\n          rawData.summary = rawData.summary || '';\n          rawData.title = rawData.title || '';\n          rawData.location = rawData.location || '';\n          rawData.coverImageUrl = rawData.coverImageUrl || '';\n          \n          eventDetail.value = rawData;\n          console.log('活动详情数据已更新，最新报名人数:', rawData.registeredCount);\n          \n          // 同时刷新报名状态\n          await fetchRegistrationStatus();\n          \n          uni.showToast({\n            title: '数据已更新',\n            icon: 'success',\n            duration: 1500\n          });\n        }\n      } catch (error) {\n        console.error('重新获取活动详情失败:', error);\n      }\n    } else {\n      console.warn('事件监听：缺少 eventId，跳过数据刷新');\n    }\n  });\n\n  try {\n    console.log('开始加载活动详情，eventId:', eventId.value);\n    \n    // 状态分离重构：第一步 - 严格串行加载活动详情\n    const detailRes = await getEventDetailApi(eventId.value);\n    \n    if (detailRes.code === 200) {\n      console.log('活动详情API调用成功，开始处理数据...');\n      \n      // --- 数据清洗和安全处理 ---\n      const rawData = detailRes.data;\n      \n      // 清洗 details 字段：确保它是一个安全的字符串\n      if (!rawData.details || typeof rawData.details !== 'string') {\n        rawData.details = '';\n      } else {\n        rawData.details = rawData.details.trim();\n      }\n      \n      // 清洗 summary 字段\n      if (!rawData.summary || typeof rawData.summary !== 'string') {\n        rawData.summary = '';\n      } else {\n        rawData.summary = rawData.summary.trim();\n      }\n      \n      // 确保其他关键字段的安全性\n      rawData.title = rawData.title || '';\n      rawData.location = rawData.location || '';\n      rawData.coverImageUrl = rawData.coverImageUrl || '';\n      \n      // 关键：确保eventDetail完全赋值后才继续\n      eventDetail.value = rawData;\n      console.log('活动详情数据已完全加载并赋值:', {\n        id: eventDetail.value.id,\n        title: eventDetail.value.title,\n        status: eventDetail.value.status\n      });\n      \n    } else {\n      throw new Error(detailRes.msg || '获取活动详情失败');\n    }\n\n    // 状态分离重构：第二步 - 串行调用独立的报名状态检查\n    console.log('活动详情加载完成，现在开始检查报名状态...');\n    await fetchRegistrationStatus();\n    console.log('报名状态检查完成');\n\n  } catch (error) {\n    console.error('获取活动详情失败', error);\n    uni.$u.toast(error.message || '加载失败，请稍后重试');\n    // 即使活动详情加载失败，也要设置报名状态为错误\n    registrationStatus.value = 'error';\n  } finally {\n    // 确保无论成功还是失败，都要停止loading状态\n    console.log('页面加载流程完成，停止loading状态');\n    isLoading.value = false;\n  }\n});\n\n// 状态分离重构：简化 onShow 逻辑\nonShow(async () => {\n  console.log('=== 活动详情页面 onShow 触发 ===');\n  \n  // 使用调试工具检查登录状态\n  // 开发期调试：已移除 loginDebugUtils\n  \n  // 强制触发 isLoggedIn 计算属性重新计算\n  loginCheckTrigger.value++;\n  \n  const currentToken = getUserToken();\n  console.log('当前登录状态:', !!currentToken);\n  console.log('当前token:', currentToken);\n  console.log('当前活动ID:', eventId.value);\n  console.log('活动详情是否已加载:', !!eventDetail.value);\n  console.log('页面是否还在加载中:', isLoading.value);\n  console.log('当前报名状态:', registrationStatus.value);\n  \n  // 状态分离重构：检查是否有状态刷新标记（来自报名页面的返回）\n  try {\n    const refreshFlag = uni.getStorageSync('needRefreshRegistrationStatus');\n    if (refreshFlag && refreshFlag.eventId === eventId.value) {\n      console.log('发现状态刷新标记，立即刷新报名状态:', refreshFlag);\n      // 清除标记，避免重复刷新\n      uni.removeStorageSync('needRefreshRegistrationStatus');\n      // 强制刷新状态\n      if (eventId.value) {\n        await fetchRegistrationStatus();\n        return; // 直接返回，避免重复检查\n      }\n    }\n  } catch (e) {\n    console.warn('检查状态刷新标记失败:', e);\n  }\n\n  // 🔄 状态分离重构：只要有 eventId 就可以检查报名状态，不再依赖 eventDetail 加载状态\n  if (!isLoading.value && eventId.value) {\n    console.log('条件满足，开始重新检查报名状态...');\n    await fetchRegistrationStatus();\n  } else {\n    console.log('跳过报名状态检查，原因:', {\n      isLoading: isLoading.value,\n      hasEventId: !!eventId.value\n    });\n    \n    // 如果页面还在加载中，等待加载完成后再检查\n    if (isLoading.value && eventId.value) {\n      console.log('页面正在加载中，等待加载完成后再检查报名状态...');\n      const unwatch = watch(isLoading, async (newVal) => {\n        if (!newVal) {\n          console.log('页面加载完成，现在开始检查报名状态...');\n          await fetchRegistrationStatus();\n          unwatch();\n        }\n      });\n    }\n  }\n});\n\n// 🗑️ 移除旧的 checkRegistrationStatusSafely 函数，被 fetchRegistrationStatus 替代\n\n// 页面卸载时移除事件监听\nonUnload(() => {\n  uni.$off('dataChanged');\n});\n\n// 分享功能\nonShareAppMessage(() => {\n  return {\n    title: eventDetail.value?.title || '精彩活动推荐',\n    path: `/pages/event/detail?id=${eventId.value}`,\n    imageUrl: getFullImageUrl(eventDetail.value?.coverImageUrl)\n  };\n});\n\n// 状态分离重构：更新报名处理逻辑\nconst handleRegistration = () => {\n  console.log('=== 点击报名按钮 ===');\n  console.log('按钮当前显示文字:', buttonText.value);\n  console.log('按钮是否禁用:', isButtonDisabled.value);\n  console.log('当前报名状态:', registrationStatus.value);\n  \n  // 使用调试工具检查当前状态\n  // 开发期调试：已移除 loginDebugUtils\n  \n  console.log('当前登录状态:', isLoggedIn.value);\n  console.log('当前token:', getUserToken());\n  console.log('活动详情:', {\n    id: eventDetail.value?.id,\n    title: eventDetail.value?.title,\n    status: eventDetail.value?.status\n  });\n  \n  // 强制触发登录状态重新检查\n  loginCheckTrigger.value++;\n  \n  // 检查登录状态\n  if (!isLoggedIn.value || registrationStatus.value === 'not_logged_in') {\n    console.log('用户未登录，跳转到登录页');\n    // 开发期调试：已移除 loginDebugUtils.logNavigation\n    uni.navigateTo({ url: '/pages_sub/pages_other/login' });\n    return;\n  }\n\n  console.log('用户已登录');\n  \n  if (registrationStatus.value === 'registered') {\n    // 已报名，只显示提示信息，不跳转\n    console.log('用户已报名此活动');\n    uni.$u.toast('您已报名此活动');\n  } else if (registrationStatus.value === 'unregistered' || registrationStatus.value === 'error') {\n    // 未报名或状态错误，跳转到报名页面\n    console.log('用户未报名，跳转到报名页面');\n    console.log('跳转参数:', {\n      id: eventId.value,\n      title: eventDetail.value?.title\n    });\n    // 开发期调试：已移除 loginDebugUtils.logNavigation\n    uni.navigateTo({\n      url: `/pages_sub/pages_event/registration?id=${eventId.value}&title=${encodeURIComponent(eventDetail.value?.title || '')}`\n    });\n  } else {\n    // 状态还在加载中\n    console.log('报名状态还在加载中，请稍后再试');\n    uni.$u.toast('正在检查报名状态，请稍后再试');\n  }\n};\n\n// 分享按钮点击\nconst handleShare = () => {\n  // 小程序分享功能\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    href: `/pages/event/detail?id=${eventId.value}`,\n    title: eventDetail.value?.title || '精彩活动推荐',\n    summary: eventDetail.value?.summary || '',\n    imageUrl: getFullImageUrl(eventDetail.value?.coverImageUrl),\n    success: function (res) {\n      uni.$u.toast('分享成功');\n    },\n    fail: function (err) {\n      console.error('分享失败:', err);\n    }\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.event-detail-page {\n  background: #FFFFFF;\n  width: 750rpx;\n  height: 1624rpx;\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\n}\n\n.scroll-content {\n  flex: 1;\n  padding-bottom: 120rpx;\n  box-sizing: border-box;\n}\n\n.top-image-container {\n  width: 100%;\n  height: 420rpx; /* 顶部大图高度 */\n}\n\n.info-card {\n  background: #FFFFFF;\n  margin: 0 30rpx; \n  margin-top: -60rpx; \n  border-radius: 16rpx 16rpx 16rpx 16rpx;\n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);\n  padding: 30rpx; \n  position: relative; \n  z-index: 1; \n}\n\n.info-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.share-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 8rpx; /* 控制图标和文字的间距 */\n}\n\n.share-text {\n  width: 56rpx;\n  height: 44rpx;\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #023F98; /* 更新为蓝色 */\n  line-height: 44rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n}\n\n.share-button-content {\n  display: flex;\n  flex-direction: row; \n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.card-header {\n  display: flex;\n  align-items: flex-start; /* 关键：改为顶部对齐 */\n  margin-bottom: 20rpx;\n}\n\n.status-tag-detail {\n   width: 90rpx;\n   height: 40rpx;\n   display: flex;\n   justify-content: center;\n   align-items: center;\n   position: relative;\n   overflow: hidden;\n   font-size: 22rpx;\n   border-radius: 12rpx;\n   margin-right: 16rpx;\n   margin-top: 15rpx;\n   flex-shrink: 0;\n   &.ended {\n     background-color: #909399;\n   }\n   &.ended .status-bg-image {\n     display: none;\n   }\n}\n\n/* 步骤2: 让新加的 wrapper 容器可伸缩 */\n.event-title-section {\n  flex: 1; /* 关键：让这个容器占据剩余空间 */\n  min-width: 0; /* 关键：一个flex布局的技巧，允许子元素在空间不足时正确换行 */\n  margin-left: 16rpx;\n}\n\n/* 步骤3: 让内部的文字可以换行 */\n.event-title {\n  /* 关键：设置文本换行规则 */\n  white-space: normal;\n  word-break: break-word;\n  \n  /* 保留字体样式 */\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n  font-weight: normal;\n  font-size: 32rpx;\n  color: #23232A;\n  line-height: 1.5; /* 推荐：让多行文本有舒适的行间距 */\n}\n\n.info-row {\n  display: flex;\n  align-items: center;\n  // margin-bottom: 16rpx; /* 行间距 */\n  margin-top: 24rpx;\n}\n\n.info-text {\n  /* 确保您已在项目中全局引入了该字体 */\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\n  font-size: 26rpx; /* 更新字号 */\n  color: #606266;   /* 这是基础文字颜色，蓝色会覆盖它 */\n  margin-left: 16rpx; /* 图标与文字间距 */\n}\n\n.detail-section {\n  background-color: #ffffff;\n  margin: 30rpx 0; /* 与上方卡片和左右的间距 */\n  // border-radius: 20rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\n  padding: 30rpx;\n}\n\n.section-title {\n  font-size: 34rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.placeholder-content {\n  /* 占位符容器样式 */\n  background-color: #f8f8f8;\n  border-radius: 10rpx;\n  padding: 20rpx;\n  text-align: center;\n  margin-top: 20rpx;\n}\n\n.placeholder-text {\n  /* 占位符文字样式 */\n  color: #909399;\n  font-size: 28rpx;\n  line-height: 1.5;\n}\n\n.loading-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #ffffff;\n  z-index: 999;\n}\n\n.rich-text-content {\n  /* 富文本容器样式 */\n  line-height: 1.6;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.rich-text {\n  /* rich-text 组件样式 */\n  width: 100%;\n  line-height: 1.6;\n}\n\n/* rich-text 内部元素样式优化 */\n:deep(.rich-text) {\n  p {\n    margin: 16rpx 0;\n    line-height: 1.6;\n  }\n  \n  img {\n    max-width: 100%;\n    height: auto;\n    border-radius: 8rpx;\n    margin: 16rpx 0;\n  }\n  \n  h1, h2, h3, h4, h5, h6 {\n    margin: 24rpx 0 16rpx 0;\n    font-weight: bold;\n  }\n  \n  ul, ol {\n    margin: 16rpx 0;\n    padding-left: 40rpx;\n  }\n  \n  li {\n    margin: 8rpx 0;\n  }\n  \n  blockquote {\n    background-color: #f8f8f8;\n    border-left: 8rpx solid #409eff;\n    padding: 16rpx;\n    margin: 16rpx 0;\n    border-radius: 8rpx;\n  }\n}\n\n.summary-content {\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-radius: 10rpx;\n}\n\n.summary-text {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n.bottom-action-bar {\n  position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    width: 100%; /* 750rpx 即为全屏宽度 */\n    box-sizing: border-box;\n    z-index: 100;\n  \n    /* 关键: 应用您提供的所有样式 */\n    height: calc(156rpx + env(safe-area-inset-bottom)); /* 容器总高度 = 设计高度 + 安全区域 */\n    background-color: #FFFFFF ;\n    border-top: 2rpx solid #EEEEEE; /* 设计稿中的 border，通常是顶部边框 */\n    border-radius: 0;\n    box-shadow: none; /* 移除旧阴影 */\n    display: flex;\n    justify-content: space-between;\n    align-items: center; /* 垂直居中按钮 */\n    /* 关键: 设置左右内边距，并为底部安全区域留出空间 */\n    padding: 0 30rpx; \n    padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 新增：背景图样式 */\n.status-bg-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1; /* 将图片置于底层 */\n}\n\n/* 新增：文字样式 */\n.status-text {\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n    font-weight: normal;\n    font-size: 22rpx;\n    color: #023F98; \n    position: relative;\n    z-index: 2;\n}\n\n/* up-button 样式调整 */\n:deep(.up-button--square) {\n  border-radius: 10rpx !important; /* 确保按钮圆角 */\n}\n:deep(.up-button--primary) {\n  border-radius: 44rpx !important; /* 立即报名按钮的圆角 */\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_event/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "checkRegistrationStatusApi", "onLoad", "getEventDetailApi", "onShow", "watch", "onUnload", "onShareAppMessage", "getFullImageUrl"], "mappings": ";;;;;;;;;;;;;;;;;AAyDA,MAAA,gBAAA,MAAA;AACA,MAAA,qBAAA,MAAA;AACA,MAAA,iBAAA,MAAA;;;;AAEA,UAAA,UAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,IAAA;AAGA,UAAA,qBAAAA,cAAAA,IAAA,SAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,oBAAAA,cAAAA,IAAA,CAAA;AAGA,UAAA,kBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,kBAAAC,cAAA,SAAA,MAAA;AACA,aAAA,GAAA,gBAAA,QAAA,aAAA,KAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,UAAA,MAAA;AAEA,YAAA,aAAAC,oBAAA;AACA,sBAAA,QAAA,WAAA,mBAAA;AAAA,IACA,CAAA;AAGA,UAAA,eAAA,MAAA;AACA,UAAA;AACA,eAAAA,oBAAA,eAAA,OAAA,KAAA;AAAA,MACA,SAAA,GAAA;AACA,eAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,aAAAF,cAAA,SAAA,MAAA;AAEA,wBAAA;AACA,aAAA,CAAA,CAAA;IACA,CAAA;AAGA,UAAA,aAAAA,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,YAAA;AAAA,eAAA;AAGA,cAAA,mBAAA,OAAA;AAAA,QACA,KAAA;AACA,iBAAA;AAAA,QACA,KAAA;AACA,iBAAA;AAAA,QACA,KAAA;AACA,iBAAA;AAAA,QACA,KAAA;AAEA,cAAA,YAAA,MAAA,WAAA,KAAA,YAAA,MAAA,WAAA;AAAA,mBAAA;AACA,cAAA,YAAA,MAAA,WAAA;AAAA,mBAAA;AACA,iBAAA;AAAA,QACA,KAAA;AACA,iBAAA;AAAA,QACA;AACA,iBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,mBAAAA,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,YAAA;AAAA,eAAA;AAGA,UAAA,mBAAA,UAAA;AAAA,eAAA;AAGA,aAAA,YAAA,MAAA,WAAA,KAAA,YAAA,MAAA,WAAA;AAAA,IACA,CAAA;AAKA,UAAA,gBAAA,MAAA;AACAE,oBAAAA,MAAA,MAAA,OAAA,2CAAA,oBAAA;AAGAA,oBAAAA,MAAA,aAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,2CAAA,QAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAA,MAAA,MAAA,QAAA,2CAAA,WAAA,GAAA;AAGAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,MAAA;AACAA,4BAAAA,MAAA,MAAA,OAAA,2CAAA,aAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,SAAA;AACAA,4BAAA,MAAA,MAAA,QAAA,2CAAA,gBAAA,IAAA;AAGAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,KAAA;AAAA,gBACA,SAAA,MAAA;AACAA,gCAAAA,MAAA,MAAA,OAAA,2CAAA,SAAA;AAAA,gBACA;AAAA,gBACA,MAAA,CAAA,SAAA;AACAA,gCAAA,MAAA,MAAA,SAAA,2CAAA,eAAA,IAAA;AACAA,gCAAAA,MAAA,UAAA;AAAA,oBACA,OAAA;AAAA,oBACA,MAAA;AAAA,kBACA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,0BAAA,YAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,2CAAA,qBAAA;AAEA,UAAA;AAEA,cAAA,QAAA;AACA,YAAA,CAAA,OAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,2CAAA,2BAAA;AACA,6BAAA,QAAA;AACA;AAAA,QACA;AAGA,YAAA,CAAA,QAAA,OAAA;AACAA,wBAAAA,MAAA,MAAA,QAAA,2CAAA,qBAAA;AACA,6BAAA,QAAA;AACA;AAAA,QACA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,2BAAA;AAGA,YAAA;AACA,gBAAA,0BAAAA,cAAA,MAAA,eAAA,oBAAA,KAAA,CAAA;AACA,gBAAA,cAAA,wBAAA,QAAA,KAAA;AACA,cAAA,eAAA,YAAA,cAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,2CAAA,sBAAA;AACA,+BAAA,QAAA;AAAA,UACA;AAAA,QACA,SAAA,OAAA;AACAA,wBAAA,MAAA,MAAA,QAAA,2CAAA,eAAA,KAAA;AAAA,QACA;AAGA,cAAA,YAAA,MAAAC,4CAAAA,2BAAA,QAAA,KAAA;AAEA,YAAA,UAAA,SAAA,KAAA;AAGA,gBAAA,eAAA,UAAA,gBAAA;AACAD,wBAAA,MAAA,MAAA,OAAA,2CAAA,aAAA,YAAA;AAEA,gBAAA,YAAA,eAAA,eAAA;AAGA,cAAA,mBAAA,UAAA,WAAA;AACAA,gCAAA,MAAA,OAAA,2CAAA,aAAA,mBAAA,OAAA,MAAA,SAAA;AACA,+BAAA,QAAA;AAGA,gBAAA,cAAA,gBAAA,mBAAA,UAAA,cAAA;AACAA,4BAAAA,MAAA,GAAA,MAAA,SAAA;AAAA,YACA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,2CAAA,cAAA;AAAA,UACA;AAAA,QAEA,OAAA;AACAA,wBAAA,MAAA,MAAA,QAAA,2CAAA,oBAAA,SAAA;AACA,6BAAA,QAAA;AAAA,QACA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,2CAAA,aAAA,KAAA;AACA,2BAAA,QAAA;AAAA,MACA;AAEAA,0BAAA,MAAA,OAAA,2CAAA,sBAAA,mBAAA,OAAA,MAAA;AAAA,IACA;AAGAE,kBAAA,OAAA,OAAA,YAAA;AACA,cAAA,QAAA,QAAA;AACA,UAAA,CAAA,QAAA,OAAA;AACAF,sBAAAA,MAAA,GAAA,MAAA,OAAA;AACA,mBAAA,MAAA;AACAA,wBAAA,MAAA,aAAA;AAAA,QACA,GAAA,IAAA;AACA;AAAA,MACA;AAGAA,0BAAA,IAAA,eAAA,YAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,yBAAA;AAEA,YAAA,QAAA,OAAA;AACA,cAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,2CAAA,iBAAA;AAGA,kBAAA,YAAA,MAAAG,eAAAA,kBAAA,QAAA,KAAA;AAEA,gBAAA,UAAA,SAAA,KAAA;AAEA,oBAAA,UAAA,UAAA;AACA,sBAAA,UAAA,QAAA,WAAA;AACA,sBAAA,UAAA,QAAA,WAAA;AACA,sBAAA,QAAA,QAAA,SAAA;AACA,sBAAA,WAAA,QAAA,YAAA;AACA,sBAAA,gBAAA,QAAA,iBAAA;AAEA,0BAAA,QAAA;AACAH,4BAAA,MAAA,MAAA,OAAA,2CAAA,qBAAA,QAAA,eAAA;AAGA,oBAAA,wBAAA;AAEAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,gBACA,UAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,SAAA,OAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,2CAAA,eAAA,KAAA;AAAA,UACA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,MAAA,QAAA,2CAAA,wBAAA;AAAA,QACA;AAAA,MACA,CAAA;AAEA,UAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,2CAAA,qBAAA,QAAA,KAAA;AAGA,cAAA,YAAA,MAAAG,eAAAA,kBAAA,QAAA,KAAA;AAEA,YAAA,UAAA,SAAA,KAAA;AACAH,wBAAAA,MAAA,MAAA,OAAA,2CAAA,uBAAA;AAGA,gBAAA,UAAA,UAAA;AAGA,cAAA,CAAA,QAAA,WAAA,OAAA,QAAA,YAAA,UAAA;AACA,oBAAA,UAAA;AAAA,UACA,OAAA;AACA,oBAAA,UAAA,QAAA,QAAA,KAAA;AAAA,UACA;AAGA,cAAA,CAAA,QAAA,WAAA,OAAA,QAAA,YAAA,UAAA;AACA,oBAAA,UAAA;AAAA,UACA,OAAA;AACA,oBAAA,UAAA,QAAA,QAAA,KAAA;AAAA,UACA;AAGA,kBAAA,QAAA,QAAA,SAAA;AACA,kBAAA,WAAA,QAAA,YAAA;AACA,kBAAA,gBAAA,QAAA,iBAAA;AAGA,sBAAA,QAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,2CAAA,mBAAA;AAAA,YACA,IAAA,YAAA,MAAA;AAAA,YACA,OAAA,YAAA,MAAA;AAAA,YACA,QAAA,YAAA,MAAA;AAAA,UACA,CAAA;AAAA,QAEA,OAAA;AACA,gBAAA,IAAA,MAAA,UAAA,OAAA,UAAA;AAAA,QACA;AAGAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,wBAAA;AACA,cAAA,wBAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,UAAA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,2CAAA,YAAA,KAAA;AACAA,sBAAA,MAAA,GAAA,MAAA,MAAA,WAAA,YAAA;AAEA,2BAAA,QAAA;AAAA,MACA,UAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,sBAAA;AACA,kBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGAI,kBAAAA,OAAA,YAAA;AACAJ,oBAAAA,MAAA,MAAA,OAAA,2CAAA,0BAAA;AAMA,wBAAA;AAEA,YAAA,eAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,CAAA,CAAA,YAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,YAAA,YAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,QAAA,KAAA;AACAA,0BAAA,MAAA,OAAA,2CAAA,cAAA,CAAA,CAAA,YAAA,KAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,cAAA,UAAA,KAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,mBAAA,KAAA;AAGA,UAAA;AACA,cAAA,cAAAA,cAAAA,MAAA,eAAA,+BAAA;AACA,YAAA,eAAA,YAAA,YAAA,QAAA,OAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,2CAAA,sBAAA,WAAA;AAEAA,8BAAA,kBAAA,+BAAA;AAEA,cAAA,QAAA,OAAA;AACA,kBAAA,wBAAA;AACA;AAAA,UACA;AAAA,QACA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,QAAA,2CAAA,eAAA,CAAA;AAAA,MACA;AAGA,UAAA,CAAA,UAAA,SAAA,QAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,oBAAA;AACA,cAAA,wBAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,gBAAA;AAAA,UACA,WAAA,UAAA;AAAA,UACA,YAAA,CAAA,CAAA,QAAA;AAAA,QACA,CAAA;AAGA,YAAA,UAAA,SAAA,QAAA,OAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,2CAAA,2BAAA;AACA,gBAAA,UAAAK,cAAAA,MAAA,WAAA,OAAA,WAAA;AACA,gBAAA,CAAA,QAAA;AACAL,4BAAAA,MAAA,MAAA,OAAA,2CAAA,sBAAA;AACA,oBAAA,wBAAA;AACA;YACA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AAKAM,kBAAAA,SAAA,MAAA;AACAN,0BAAA,KAAA,aAAA;AAAA,IACA,CAAA;AAGAO,kBAAAA,kBAAA,MAAA;;AACA,aAAA;AAAA,QACA,SAAA,iBAAA,UAAA,mBAAA,UAAA;AAAA,QACA,MAAA,0BAAA,QAAA,KAAA;AAAA,QACA,UAAAC,YAAA,iBAAA,iBAAA,UAAA,mBAAA,aAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,qBAAA,MAAA;;AACAR,oBAAAA,MAAA,MAAA,OAAA,2CAAA,gBAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,aAAA,WAAA,KAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,iBAAA,KAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,mBAAA,KAAA;AAKAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,WAAA,WAAA,KAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,2CAAA,YAAA,aAAA,CAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,2CAAA,SAAA;AAAA,QACA,KAAA,iBAAA,UAAA,mBAAA;AAAA,QACA,QAAA,iBAAA,UAAA,mBAAA;AAAA,QACA,SAAA,iBAAA,UAAA,mBAAA;AAAA,MACA,CAAA;AAGA,wBAAA;AAGA,UAAA,CAAA,WAAA,SAAA,mBAAA,UAAA,iBAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,cAAA;AAEAA,sBAAAA,MAAA,WAAA,EAAA,KAAA,+BAAA,CAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,2CAAA,OAAA;AAEA,UAAA,mBAAA,UAAA,cAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,UAAA;AACAA,sBAAAA,MAAA,GAAA,MAAA,SAAA;AAAA,MACA,WAAA,mBAAA,UAAA,kBAAA,mBAAA,UAAA,SAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,eAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,SAAA;AAAA,UACA,IAAA,QAAA;AAAA,UACA,QAAA,iBAAA,UAAA,mBAAA;AAAA,QACA,CAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,0CAAA,QAAA,KAAA,UAAA,qBAAA,iBAAA,UAAA,mBAAA,UAAA,EAAA,CAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,2CAAA,iBAAA;AACAA,sBAAAA,MAAA,GAAA,MAAA,gBAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,cAAA,MAAA;;AAEAA,oBAAAA,MAAA,MAAA;AAAA,QACA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,QACA,MAAA,0BAAA,QAAA,KAAA;AAAA,QACA,SAAA,iBAAA,UAAA,mBAAA,UAAA;AAAA,QACA,WAAA,iBAAA,UAAA,mBAAA,YAAA;AAAA,QACA,UAAAQ,YAAA,iBAAA,iBAAA,UAAA,mBAAA,aAAA;AAAA,QACA,SAAA,SAAA,KAAA;AACAR,wBAAAA,MAAA,GAAA,MAAA,MAAA;AAAA,QACA;AAAA,QACA,MAAA,SAAA,KAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,2CAAA,SAAA,GAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzfA,GAAG,WAAW,eAAe;"}