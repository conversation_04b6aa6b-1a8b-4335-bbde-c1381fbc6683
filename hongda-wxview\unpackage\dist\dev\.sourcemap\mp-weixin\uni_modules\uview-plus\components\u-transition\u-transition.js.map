{"version": 3, "file": "u-transition.js", "sources": ["uni_modules/uview-plus/components/u-transition/u-transition.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXRyYW5zaXRpb24vdS10cmFuc2l0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t\tv-if=\"inited\"\r\n\t\tclass=\"u-transition\"\r\n\t\tref=\"u-transition\"\r\n\t\t@tap=\"clickHandler\"\r\n\t\t:class=\"classes\"\r\n\t\t:style=\"[mergeStyle]\"\r\n\t\t@touchmove=\"noop\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { props } from './props';\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addStyle } from '../../libs/function/index';\r\n// 组件的methods方法，由于内容较长，写在外部文件中通过mixin引入\r\nimport transitionMixin from \"./transitionMixin.js\";\r\n/**\r\n * transition  动画组件\r\n * @description\r\n * @tutorial\r\n * @property {String}\t\t\tshow\t\t\t是否展示组件 （默认 false ）\r\n * @property {String}\t\t\tmode\t\t\t使用的动画模式 （默认 'fade' ）\r\n * @property {String | Number}\tduration\t\t动画的执行时间，单位ms （默认 '300' ）\r\n * @property {String}\t\t\ttimingFunction\t使用的动画过渡函数 （默认 'ease-out' ）\r\n * @property {Object}\t\t\tcustomStyle\t\t自定义样式\r\n * @pages_event {Function} before-enter\t进入前触发\r\n * @pages_event {Function} enter\t\t\t进入中触发\r\n * @pages_event {Function} after-enter\t进入后触发\r\n * @pages_event {Function} before-leave\t离开前触发\r\n * @pages_event {Function} leave\t\t\t离开中触发\r\n * @pages_event {Function} after-leave\t离开后触发\r\n * @example\r\n */\r\nexport default {\r\n\tname: 'u-transition',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tinited: false, // 是否显示/隐藏组件\r\n\t\t\tviewStyle: {}, // 组件内部的样式\r\n\t\t\tstatus: '', // 记录组件动画的状态\r\n\t\t\ttransitionEnded: false, // 组件是否结束的标记\r\n\t\t\tdisplay: false, // 组件是否展示\r\n\t\t\tclasses: '', // 应用的类名\r\n\t\t}\r\n\t},\r\n\temits: ['click', 'beforeEnter', 'enter', 'afterEnter', 'beforeLeave', 'leave', 'afterLeave'],\r\n\tcomputed: {\r\n\t    mergeStyle() {\r\n\t        const { viewStyle, customStyle } = this\r\n\t        return {\r\n\t            // #ifndef APP-NVUE\r\n\t            transitionDuration: `${this.duration}ms`,\r\n\t            // display: `${this.display ? '' : 'none'}`,\r\n\t\t\t\ttransitionTimingFunction: this.timingFunction,\r\n\t            // #endif\r\n\t\t\t\t// 避免自定义样式影响到动画属性，所以写在viewStyle前面\r\n\t            ...addStyle(customStyle),\r\n\t            ...viewStyle\r\n\t        }\r\n\t    }\r\n\t},\r\n\t// 将mixin挂在到组件中，实际上为一个vue格式对象。\r\n\tmixins: [mpMixin, mixin, transitionMixin, props],\r\n\twatch: {\r\n\t\tshow: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\t// vue和nvue分别执行不同的方法\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tnewVal ? this.nvueEnter() : this.nvueLeave()\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tnewVal ? this.vueEnter() : this.vueLeave()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 表示同时监听初始化时的props的show的意思\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* #ifndef APP-NVUE */\r\n// vue版本动画相关的样式抽离在外部文件\r\n// @use './vue.ani-style.scss' as *;\r\n/**\r\n * vue版本动画内置的动画模式有如下：\r\n * fade：淡入\r\n * zoom：缩放\r\n * fade-zoom：缩放淡入\r\n * fade-up：上滑淡入\r\n * fade-down：下滑淡入\r\n * fade-left：左滑淡入\r\n * fade-right：右滑淡入\r\n * slide-up：上滑进入\r\n * slide-down：下滑进入\r\n * slide-left：左滑进入\r\n * slide-right：右滑进入\r\n */\r\n\r\n $u-zoom-scale: scale(0.95);\r\n\r\n.u-fade-enter-active,\r\n.u-fade-leave-active {\r\n\ttransition-property: opacity;\r\n}\r\n\r\n.u-fade-enter,\r\n.u-fade-leave-to {\r\n\topacity: 0\r\n}\r\n\r\n.u-fade-zoom-enter,\r\n.u-fade-zoom-leave-to {\r\n\ttransform: $u-zoom-scale;\r\n\topacity: 0;\r\n}\r\n\r\n.u-fade-zoom-enter-active,\r\n.u-fade-zoom-leave-active {\r\n\ttransition-property: transform, opacity;\r\n}\r\n\r\n.u-fade-down-enter-active,\r\n.u-fade-down-leave-active,\r\n.u-fade-left-enter-active,\r\n.u-fade-left-leave-active,\r\n.u-fade-right-enter-active,\r\n.u-fade-right-leave-active,\r\n.u-fade-up-enter-active,\r\n.u-fade-up-leave-active {\r\n\ttransition-property: opacity, transform;\r\n}\r\n\r\n.u-fade-up-enter,\r\n.u-fade-up-leave-to {\r\n\ttransform: translate3d(0, 100%, 0);\r\n\topacity: 0\r\n}\r\n\r\n.u-fade-down-enter,\r\n.u-fade-down-leave-to {\r\n\ttransform: translate3d(0, -100%, 0);\r\n\topacity: 0\r\n}\r\n\r\n.u-fade-left-enter,\r\n.u-fade-left-leave-to {\r\n\ttransform: translate3d(-100%, 0, 0);\r\n\topacity: 0\r\n}\r\n\r\n.u-fade-right-enter,\r\n.u-fade-right-leave-to {\r\n\ttransform: translate3d(100%, 0, 0);\r\n\topacity: 0\r\n}\r\n\r\n.u-slide-down-enter-active,\r\n.u-slide-down-leave-active,\r\n.u-slide-left-enter-active,\r\n.u-slide-left-leave-active,\r\n.u-slide-right-enter-active,\r\n.u-slide-right-leave-active,\r\n.u-slide-up-enter-active,\r\n.u-slide-up-leave-active {\r\n\ttransition-property: transform;\r\n}\r\n\r\n.u-slide-up-enter,\r\n.u-slide-up-leave-to {\r\n\ttransform: translate3d(0, 100%, 0)\r\n}\r\n\r\n.u-slide-down-enter,\r\n.u-slide-down-leave-to {\r\n\ttransform: translate3d(0, -100%, 0)\r\n}\r\n\r\n.u-slide-left-enter,\r\n.u-slide-left-leave-to {\r\n\ttransform: translate3d(-100%, 0, 0)\r\n}\r\n\r\n.u-slide-right-enter,\r\n.u-slide-right-leave-to {\r\n\ttransform: translate3d(100%, 0, 0)\r\n}\r\n\r\n.u-zoom-enter-active,\r\n.u-zoom-leave-active {\r\n\ttransition-property: transform\r\n}\r\n\r\n.u-zoom-enter,\r\n.u-zoom-leave-to {\r\n\ttransform: $u-zoom-scale\r\n}\r\n/* #endif */\r\n\r\n.u-transition {}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-transition/u-transition.vue'\nwx.createComponent(Component)"], "names": ["addStyle", "mpMixin", "mixin", "transitionMixin", "props"], "mappings": ";;;;;;;AAsCA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA;AAAA,MACR,WAAW,CAAE;AAAA;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,iBAAiB;AAAA;AAAA,MACjB,SAAS;AAAA;AAAA,MACT,SAAS;AAAA;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO,CAAC,SAAS,eAAe,SAAS,cAAc,eAAe,SAAS,YAAY;AAAA,EAC3F,UAAU;AAAA,IACN,aAAa;AACT,YAAM,EAAE,WAAW,YAAY,IAAI;AACnC,aAAO;AAAA,QAEH,oBAAoB,GAAG,KAAK,QAAQ;AAAA;AAAA,QAE7C,0BAA0B,KAAK;AAAA;AAAA,QAGtB,GAAGA,0CAAAA,SAAS,WAAW;AAAA,QACvB,GAAG;AAAA,MACP;AAAA,IACJ;AAAA,EACH;AAAA;AAAA,EAED,QAAQ,CAACC,yCAAO,SAAEC,8CAAOC,6DAAAA,iBAAiBC,mDAAAA,KAAK;AAAA,EAC/C,OAAO;AAAA,IACN,MAAM;AAAA,MACL,QAAQ,QAAQ;AAMf,iBAAS,KAAK,aAAa,KAAK,SAAS;AAAA,MAEzC;AAAA;AAAA,MAED,WAAW;AAAA,IACZ;AAAA,EACD;AACD;;;;;;;;;;;;AClFA,GAAG,gBAAgB,SAAS;"}