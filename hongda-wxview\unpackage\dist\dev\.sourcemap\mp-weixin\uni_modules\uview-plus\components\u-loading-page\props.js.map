{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-loading-page/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\n\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 提示内容\r\n        loadingText: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingPage.loadingText\r\n        },\r\n        // 文字上方用于替换loading动画的图片\r\n        image: {\r\n            type: String,\r\n            default: () => defProps.loadingPage.image\r\n        },\r\n        // 加载动画的模式，circle-圆形，spinner-花朵形，semicircle-半圆形\r\n        loadingMode: {\r\n            type: String,\r\n            default: () => defProps.loadingPage.loadingMode\r\n        },\r\n        // 是否加载中\r\n        loading: {\r\n            type: Boolean,\r\n            default: () => defProps.loadingPage.loading\r\n        },\r\n        // 背景色\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.loadingPage.bgColor\r\n        },\r\n        // 文字颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.loadingPage.color\r\n        },\r\n        // 文字大小\r\n        fontSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingPage.fontSize\r\n        },\r\n\t\t// 图标大小\r\n\t\ticonSize: {\r\n\t\t    type: [String, Number],\r\n\t\t    default: () => defProps.loadingPage.fontSize\r\n\t\t},\r\n        // 加载中图标的颜色，只能rgb或者十六进制颜色值\r\n        loadingColor: {\r\n            type: String,\r\n            default: () => defProps.loadingPage.loadingColor\r\n        },\r\n        // 层级\r\n        zIndex: {\r\n            type: [Number],\r\n            default: () => defProps.loadingPage.zIndex\r\n        },\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAEP,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAEK,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,MAAM;AAAA,MACb,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA,EACJ;AACL,CAAC;;"}