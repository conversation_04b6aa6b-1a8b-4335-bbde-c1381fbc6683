"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_countryPolicy = require("../../api/content/countryPolicy.js");
const utils_config = require("../../utils/config.js");
const pages_sub_pages_article_api_common_mpHtmlStyles = require("../pages_article/api/common/mpHtmlStyles.js");
if (!Array) {
  const _component_DetailSkeleton = common_vendor.resolveComponent("DetailSkeleton");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  (_component_DetailSkeleton + _easycom_uni_icons2 + _easycom_u_empty2)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_u_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_uni_icons + mpHtml + _easycom_u_empty)();
}
const mpHtml = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
const navBarPaddingBottomRpx = 10;
const _sfc_main = {
  __name: "policy_detail",
  setup(__props) {
    const navBarPaddingBottomPx = common_vendor.index.upx2px(navBarPaddingBottomRpx);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const getNavBarInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        statusBarHeight.value = menuButtonInfo.top;
        navBarHeight.value = menuButtonInfo.height;
        headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
      } catch (e) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        navBarHeight.value = 44;
        headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    const baseUrl = utils_config.IMAGE_BASE_URL;
    const article = common_vendor.ref(null);
    const loading = common_vendor.ref(true);
    common_vendor.onLoad(async (options) => {
      getNavBarInfo();
      if (!options.id) {
        common_vendor.index.showToast({ title: "参数错误", icon: "none" });
        loading.value = false;
        return;
      }
      try {
        const res = await api_content_countryPolicy.getCountryPolicyArticle(options.id);
        if (res.code === 200 && res.data) {
          article.value = res.data;
        } else {
          throw new Error(res.msg || "文章不存在或已被删除");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_country/policy_detail.vue:112", "获取国别政策文章失败:", error);
        article.value = null;
      } finally {
        loading.value = false;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loading.value
      }, loading.value ? {} : article.value ? {
        c: statusBarHeight.value + "px",
        d: common_vendor.p({
          type: "left",
          color: "#000000",
          size: "22"
        }),
        e: common_vendor.o(goBack),
        f: navBarHeight.value + "px",
        g: common_vendor.unref(navBarPaddingBottomPx) + "px",
        h: headerHeight.value + "px",
        i: common_vendor.t(article.value.title),
        j: common_vendor.t(article.value.createTime),
        k: common_vendor.p({
          content: article.value.content,
          domain: common_vendor.unref(baseUrl),
          ["tag-style"]: common_vendor.unref(pages_sub_pages_article_api_common_mpHtmlStyles.tagStyle),
          ["container-style"]: common_vendor.unref(pages_sub_pages_article_api_common_mpHtmlStyles.containerStyle),
          ["preview-img"]: true,
          ["lazy-load"]: true
        }),
        l: headerHeight.value + "px"
      } : {
        m: common_vendor.p({
          mode: "list",
          text: "文章不存在或已被删除"
        })
      }, {
        b: article.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-434d6b0a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_country/policy_detail.js.map
