{"version": 3, "file": "registration.js", "sources": ["pages_sub/pages_event/registration.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2V2ZW50XHJlZ2lzdHJhdGlvbi52dWU"], "sourcesContent": ["<template>\n  <view class=\"registration-page\">\n    <!-- 自定义导航栏 -->\n    <up-navbar\n      :title=\"eventInfo?.title || '活动报名'\"\n      :fixed=\"true\"\n      :safeAreaInsetTop=\"true\"\n      bgColor=\"transparent\"\n      leftIcon=\"arrow-left\"\n      leftIconColor=\"#333333\"\n      titleStyle=\"color: #333333; font-weight: bold;\"\n      @leftClick=\"handleNavBack\"\n    />\n\n    <!-- 全局加载状态 -->\n    <view class=\"loading-container\" v-if=\"isLoading\">\n      <up-loading-page loadingText=\"正在加载报名表单...\" loadingMode=\"spinner\"></up-loading-page>\n    </view>\n\n    <!-- 未登录状态提示 -->\n    <view class=\"login-prompt-container\" v-else-if=\"!isLoggedIn\">\n      <up-empty \n        mode=\"permission\" \n        text=\"请先登录后进行报名\"\n        textColor=\"#909399\"\n        textSize=\"28\"\n      >\n        <template #button>\n          <up-button \n            type=\"primary\" \n            text=\"立即登录\"\n            @click=\"goToLogin\"\n            customStyle=\"background-color: #f56c6c; border-color: #f56c6c; width: 200rpx; height: 70rpx; font-size: 28rpx;\"\n          ></up-button>\n        </template>\n      </up-empty>\n    </view>\n\n    <!-- 空状态：无表单配置或加载失败 -->\n    <view class=\"empty-container\" v-else-if=\"isLoggedIn && (!formConfig || formConfig.length === 0)\">\n      <up-empty \n        mode=\"data\" \n        text=\"该活动无需报名表单\"\n        textColor=\"#909399\"\n        textSize=\"28\"\n      ></up-empty>\n    </view>\n\n    <!-- 主要内容区域：动态表单 (只有登录用户才能看到) -->\n    <scroll-view scroll-y class=\"scroll-content\" v-else-if=\"isLoggedIn && formConfig && formConfig.length > 0\">\n      \n      <!-- 活动名称显示区域 -->\n      <view class=\"event-title-section\" v-if=\"eventInfo\">\n        <text class=\"event-title\">{{ eventInfo.title }}</text>\n      </view>\n\n      <!-- 动态表单 -->\n      <view class=\"form-container\">\n        <up-form\n          :model=\"formData\"\n          :rules=\"formRules\"\n          ref=\"formRef\"\n          labelPosition=\"top\"\n          labelWidth=\"auto\"\n          :labelStyle=\"{\n             fontFamily: 'Alibaba PuHuiTi 3.0-55 Regular',\n                 fontWeight: 'normal',\n                 fontSize: '28rpx',\n                 color: '#23232A',\n                 lineHeight: 'normal'\n            }\"\n        >\n          <!-- 动态渲染表单项 -->\n          <template v-for=\"(item, index) in formConfig\" :key=\"index\">\n            \n            <!-- 单行输入框 -->\n            <up-form-item \n              v-if=\"item.type === 'input'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-input\n                v-model=\"formData[item.field]\"\n                :placeholder=\"item.props?.placeholder || '请输入'\"\n                :clearable=\"true\"\n                :maxlength=\"item.props?.maxlength || 100\"\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-input>\n            </up-form-item>\n\n            <!-- 多行文本输入 -->\n            <up-form-item \n              v-else-if=\"item.type === 'textarea'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-textarea\n                v-model=\"formData[item.field]\"\n                :placeholder=\"item.props?.placeholder || '请输入'\"\n                :maxlength=\"item.props?.maxlength || 500\"\n                height=\"120\"\n                count\n                customStyle=\"width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-textarea>\n            </up-form-item>\n\n            <!-- 下拉选择框 -->\n            <up-form-item \n              v-else-if=\"item.type === 'select'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-input\n                :value=\"getSelectDisplayValue(item.field, item.options)\"\n                :placeholder=\"item.props?.placeholder || '请选择'\"\n                readonly\n                suffixIcon=\"arrow-down-fill\"\n                @click=\"openPicker(item)\"\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-input>\n            </up-form-item>\n\n            <!-- 数字输入框 -->\n            <up-form-item \n              v-else-if=\"item.type === 'number'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-input\n                v-model=\"formData[item.field]\"\n                :placeholder=\"item.props?.placeholder || '请输入数字'\"\n                type=\"number\"\n                :clearable=\"true\"\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-input>\n            </up-form-item>\n\n            <!-- 手机号输入框 -->\n            <up-form-item \n              v-else-if=\"item.type === 'phone'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-input\n                v-model=\"formData[item.field]\"\n                :placeholder=\"item.props?.placeholder || '请输入手机号'\"\n                type=\"number\"\n                :clearable=\"true\"\n                :maxlength=\"11\"\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-input>\n            </up-form-item>\n\n            <!-- 邮箱输入框 -->\n            <up-form-item \n              v-else-if=\"item.type === 'email'\"\n              :label=\"item.label\"\n              :prop=\"item.field\"\n              :required=\"item.required\"\n              class=\"form-item\"\n              :style=\"{ marginBottom: '92rpx' }\"\n            >\n              <up-input\n                v-model=\"formData[item.field]\"\n                :placeholder=\"item.props?.placeholder || '请输入邮箱地址'\"\n                :clearable=\"true\"\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\n              ></up-input>\n            </up-form-item>\n\n          </template>\n        </up-form>\n      </view>\n\n      <!-- 底部留白，为固定按钮腾出空间 -->\n      <view class=\"bottom-spacer\"></view>\n    </scroll-view>\n\n    <!-- 底部提交按钮 (只有登录用户且有表单时才显示) -->\n    <view class=\"bottom-action-bar\" v-if=\"isLoggedIn && !isLoading && formConfig && formConfig.length > 0\">\n      <up-button \n        type=\"primary\"\n        shape=\"circle\"\n        size=\"large\"\n        :loading=\"isSubmitting\"\n        :disabled=\"isSubmitting\"\n        @click=\"handleSubmit\"\n        customStyle=\"width: 702rpx; height: 76rpx; background: #023F98; border-radius: 8rpx; border-color: #023F98; font-size: 28rpx;\"\n      >\n        {{ isSubmitting ? '提交中...' : '提交报名信息' }}\n      </up-button>\n    </view>\n\n    <!-- 选择器弹窗 -->\n    <up-picker\n      ref=\"pickerRef\"\n      :show=\"pickerShow\"\n      :columns=\"pickerColumns\"\n      @confirm=\"onPickerConfirm\"\n      @cancel=\"pickerShow = false\"\n      keyName=\"label\"\n    ></up-picker>\n\n    <!-- 报名确认弹窗 -->\n    <view v-if=\"showConfirmModal\" class=\"confirm-modal-overlay\" @click=\"closeConfirmModal\">\n      <view class=\"confirm-modal-content\" @click.stop>\n        <!-- 警告图标和标题 -->\n        <view class=\"modal-header\">\n          <image class=\"warning-icon\" :src=\"ordersWarningIconUrl\" mode=\"aspectFit\"></image>\n          <text class=\"modal-title\">操作提示</text>\n        </view>\n\n        <!-- 提示内容 -->\n        <view class=\"modal-body\">\n          <text class=\"modal-message\">确认提交报名信息？</text>\n        </view>\n\n        <!-- 按钮组 -->\n        <view class=\"modal-footer\">\n          <view class=\"modal-btn cancel-btn\" @click=\"closeConfirmModal\">\n            暂不提交\n          </view>\n          <view class=\"modal-btn confirm-btn\" @click=\"confirmSubmitRegistration\">\n            确认提交\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport {reactive, ref} from 'vue';\nimport {onLoad, onShow} from '@dcloudio/uni-app';\nimport {getFormDefinitionApi, submitRegistrationApi} from '@/pages_sub/pages_event/api/data/registration.js';\nimport {getEventDetailApi} from '@/api/data/event.js';\n\n// 响应式数据\nconst eventId = ref(null);\nconst eventInfo = ref(null); // 新增：活动信息\nconst formConfig = ref([]);\nconst formData = reactive({});\nconst formRules = reactive({});\nconst isLoading = ref(true);\nconst isSubmitting = ref(false);\nconst isLoggedIn = ref(false); // 新增：登录状态管理\n\n// 选择器相关\nconst pickerShow = ref(false);\nconst pickerColumns = ref([]);\nconst currentPickerField = ref('');\n\n// 确认弹窗相关\nconst showConfirmModal = ref(false);\n\n// 表单引用\nconst formRef = ref();\nconst pickerRef = ref();\n\n// 检查登录状态的函数\nconst checkLoginStatus = () => {\n  const token = uni.getStorageSync('token');\n  return !!token; // 转换为布尔值\n};\n\n// 页面加载 - 只处理基础参数，不进行登录检查\nonLoad(async (options) => {\n  eventId.value = options.id;\n  \n  if (!eventId.value) {\n    uni.$u.toast('活动信息错误');\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n    return;\n  }\n  \n  // 注意：这里不再调用 loadFormDefinition()，移动到 onShow 中\n});\n\n// 页面显示时的逻辑 - 核心登录检查逻辑\nonShow(async () => {\n  console.log('=== 报名页面 onShow 触发 ===');\n  \n  // 每次页面显示时都重新检查登录状态\n  const currentLoginStatus = checkLoginStatus();\n  isLoggedIn.value = currentLoginStatus;\n  \n  console.log('当前登录状态:', currentLoginStatus);\n  console.log('当前token:', uni.getStorageSync('token'));\n  \n  if (!currentLoginStatus) {\n    // 用户未登录，立即返回上一页（detail页面）\n    console.log('用户未登录，返回上一页让detail页面处理登录跳转');\n    isLoading.value = false; // 停止加载状态\n    \n    uni.showToast({\n      title: '请先登录后报名',\n      icon: 'none',\n      duration: 1500\n    });\n    \n    // 立即返回上一页，避免在registration页面进行登录跳转\n    setTimeout(() => {\n      console.log('返回上一页（detail页面）');\n      uni.navigateBack({\n        fail: () => {\n          // 如果返回失败，说明可能是直接访问的registration页面\n          console.log('返回失败，跳转到首页');\n          uni.switchTab({ url: '/pages/index/index' });\n        }\n      });\n    }, 800);\n    \n  } else {\n    // 用户已登录，可以正常进行报名操作\n    console.log('用户已登录，开始加载活动信息和表单数据');\n    \n    // 加载活动信息（无论是否已加载过都重新加载，确保信息是最新的）\n    if (eventId.value) {\n      await loadEventInfo();\n    }\n    \n    // 只有在已登录的情况下才加载表单\n    if (eventId.value && (!formConfig.value || formConfig.value.length === 0)) {\n      await loadFormDefinition();\n    } else {\n      // 如果表单已经加载过，直接停止loading状态\n      isLoading.value = false;\n    }\n  }\n});\n\n// 跳转到登录页的方法\nconst goToLogin = () => {\n  console.log('手动跳转到登录页');\n  uni.navigateTo({\n    url: '/pages/login/index'\n  });\n};\n\n// 智能导航回退处理函数\nconst handleNavBack = () => {\n  console.log('=== 开始智能导航回退处理 ===');\n  \n  // 第一步：尝试正常回退\n  uni.navigateBack({\n    success: () => {\n      console.log('正常回退成功');\n    },\n    fail: (err) => {\n      console.warn('⚠️ 正常回退失败:', err);\n      \n      // 第二步：尝试跳转到活动列表页面\n      uni.navigateTo({\n        url: '/pages/event/index',\n        success: () => {\n          console.log('跳转到活动列表页面成功');\n        },\n        fail: (err2) => {\n          console.warn('跳转到活动列表页面失败:', err2);\n          \n          // 第三步：最后的兜底方案，跳转到首页\n          uni.switchTab({\n            url: '/pages/index/index',\n            success: () => {\n              console.log('跳转到首页成功');\n            },\n            fail: (err3) => {\n              console.error('所有导航方案都失败了:', err3);\n              uni.showToast({\n                title: '导航失败，请重新打开小程序',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\n\n// 加载活动信息\nconst loadEventInfo = async () => {\n  try {\n    const response = await getEventDetailApi(eventId.value);\n    if (response.code === 200 && response.data) {\n      eventInfo.value = response.data;\n      console.log('成功加载活动信息:', eventInfo.value);\n    } else {\n      throw new Error(response.msg || '获取活动信息失败');\n    }\n  } catch (error) {\n    console.error('加载活动信息失败:', error);\n    // 不显示错误提示，使用默认标题即可\n  }\n};\n\n// 加载表单定义\nconst loadFormDefinition = async () => {\n  try {\n    isLoading.value = true;\n    \n    const response = await getFormDefinitionApi(eventId.value);\n    \n    if (response.code === 200 && response.data) {\n      // 处理后端返回的数据：response.data 可能是JSON字符串，需要解析\n      let formDefinition;\n      \n      if (typeof response.data === 'string') {\n        try {\n          // 如果是字符串，需要解析成对象\n          formDefinition = JSON.parse(response.data);\n          console.log('解析后的表单定义:', formDefinition);\n        } catch (parseError) {\n          console.error('解析表单定义JSON失败:', parseError);\n          throw new Error('表单配置格式错误');\n        }\n      } else {\n        // 如果已经是对象，直接使用\n        formDefinition = response.data;\n      }\n      \n      // 获取字段数组\n      const fields = formDefinition.fields || [];\n      \n      if (Array.isArray(fields) && fields.length > 0) {\n        formConfig.value = fields;\n        console.log('成功加载表单配置，字段数量:', fields.length);\n        initFormData();\n        initFormRules();\n      } else {\n        console.warn('表单配置中没有字段或字段不是数组:', formDefinition);\n        formConfig.value = [];\n      }\n    } else {\n      throw new Error(response.msg || '获取表单配置失败');\n    }\n    \n  } catch (error) {\n    console.error('加载表单定义失败:', error);\n    uni.$u.toast(error.message || '加载表单失败，请稍后重试');\n    formConfig.value = [];\n  } finally {\n    isLoading.value = false;\n  }\n};\n\n// 初始化表单数据\nconst initFormData = () => {\n  formConfig.value.forEach(item => {\n    if (item.field) {\n      formData[item.field] = item.defaultValue || '';\n    }\n  });\n};\n\n// 初始化表单验证规则\nconst initFormRules = () => {\n  formConfig.value.forEach(item => {\n    if (item.field && item.required) {\n      const rules = [{\n        required: true,\n        message: `请${item.type === 'select' ? '选择' : '输入'}${item.label}`,\n        trigger: ['blur', 'change']\n      }];\n      \n      // 添加特定类型的验证规则\n      if (item.type === 'email') {\n        rules.push({\n          pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n          message: '请输入有效的邮箱地址',\n          trigger: ['blur']\n        });\n      } else if (item.type === 'phone') {\n        rules.push({\n          pattern: /^1[3-9]\\d{9}$/,\n          message: '请输入有效的手机号',\n          trigger: ['blur']\n        });\n      }\n      \n      formRules[item.field] = rules;\n    }\n  });\n};\n\n// 获取选择框显示值\nconst getSelectDisplayValue = (field, options) => {\n  const value = formData[field];\n  if (!value || !options) return '';\n  \n  const option = options.find(opt => opt.value === value);\n  return option ? option.label : '';\n};\n\n// 打开选择器\nconst openPicker = (item) => {\n  if (!item.options || !Array.isArray(item.options)) {\n    uni.$u.toast('选项配置错误');\n    return;\n  }\n  \n  currentPickerField.value = item.field;\n  pickerColumns.value = [item.options];\n  pickerShow.value = true;\n};\n\n// 选择器确认\nconst onPickerConfirm = (e) => {\n  const { value } = e;\n  if (value && value[0] && currentPickerField.value) {\n    formData[currentPickerField.value] = value[0].value;\n  }\n  pickerShow.value = false;\n};\n\n// 显示确认弹窗\nconst handleSubmit = async () => {\n  if (!formRef.value) {\n    uni.$u.toast('表单初始化失败');\n    return;\n  }\n\n  try {\n    // 表单验证\n    await formRef.value.validate();\n\n    // 验证通过后显示确认弹窗\n    showConfirmModal.value = true;\n\n  } catch (error) {\n    console.error('表单验证失败:', error);\n    uni.$u.toast('请检查表单填写是否正确');\n  }\n};\n\n// 关闭确认弹窗\nconst closeConfirmModal = () => {\n  showConfirmModal.value = false;\n};\n\n// 确认提交报名\nconst confirmSubmitRegistration = async () => {\n  closeConfirmModal();\n\n  try {\n    isSubmitting.value = true;\n\n    // 提交数据\n    const response = await submitRegistrationApi({\n      eventId: eventId.value,\n      formData: formData\n    });\n\n    if (response.code === 200) {\n      uni.$u.toast('报名成功！');\n\n      // 🔄 状态分离重构：更新本地存储的报名状态，确保与新的分离模式兼容\n      try {\n        const registrationStatus = uni.getStorageSync('registrationStatus') || {};\n        registrationStatus[eventId.value] = {\n          isRegistered: true,\n          timestamp: Date.now(),\n          formData: formData, // 可选：保存表单数据供后续查看\n          source: 'user_registration' // 标记数据来源\n        };\n        uni.setStorageSync('registrationStatus', registrationStatus);\n        console.log('已在本地存储中标记报名状态（状态分离模式）:', registrationStatus);\n      } catch (error) {\n        console.warn('保存本地报名状态失败:', error);\n        // 即使本地存储失败，也不影响报名流程\n      }\n\n              // 🚀 【数据实时更新方案】发送全局数据变化事件\n        // 延迟跳转，让用户看到成功提示，然后发送全局广播\n        setTimeout(() => {\n          console.log('发送数据变化广播事件...');\n\n          // 【关键步骤】发送全局事件，通知所有监听的页面数据已发生变化\n          uni.$emit('dataChanged');\n          console.log('已发送 dataChanged 事件');\n\n          // 返回到上一页（通常是活动详情页）\n          uni.navigateBack({\n            success: () => {\n              console.log('返回上一页成功');\n\n              // 页面返回成功后，再次发送事件确保页面能收到\n              setTimeout(() => {\n                uni.$emit('dataChanged');\n                console.log('页面返回后再次发送 dataChanged 事件');\n              }, 100);\n            },\n            fail: (error) => {\n              console.warn('页面返回失败:', error);\n              // 如果返回失败，跳转到首页\n              uni.switchTab({\n                url: '/pages/index/index'\n              });\n            }\n          });\n        }, 1500); // 让用户看到成功提示的时间\n\n    } else {\n      throw new Error(response.msg || '报名失败');\n    }\n\n  } catch (error) {\n    console.error('提交报名失败:', error);\n\n    if (error.message) {\n      uni.$u.toast(error.message);\n    } else {\n      uni.$u.toast('提交失败，请稍后重试');\n    }\n  } finally {\n    isSubmitting.value = false;\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.registration-page {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F2F4FA;\n}\n\n.loading-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #ffffff;\n  z-index: 999;\n}\n\n.login-prompt-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\n  min-height: 60vh;\n}\n\n.empty-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\n  min-height: 60vh;\n}\n\n.scroll-content {\n  flex: 1;\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\n  box-sizing: border-box;\n}\n\n.event-title-section {\n  margin: 106rpx 30rpx 40rpx;\n  text-align: center;\n}\n\n.event-title {\n  width: 468rpx;\n    height: 44rpx;\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n    font-weight: normal;\n    font-size: 32rpx;\n    color: #23232A;\n    font-style: normal;\n    text-transform: none;\n    /* 建议增加 line-height 使文字垂直居中 */\n    line-height: 44rpx; \n}\n\n.form-container {\n\tdisplay: flex;\n\t  justify-content: center;\n  background-color: transparent;\n  margin: 0 30rpx 30rpx 30rpx;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  box-shadow: none;\n}\n\n.bottom-spacer {\n  height: 40rpx;\n}\n\n\n\n.bottom-action-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  box-sizing: border-box;\n  z-index: 100;\n  \n  /*安全区域设置 */\n  height: calc(156rpx + env(safe-area-inset-bottom));\n  background-color: #FFFFFF;\n  border-top: 2rpx solid #EEEEEE;\n  border-radius: 0;\n  box-shadow: none;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  /* 设置左右内边距，并为底部安全区域留出空间 */\n  padding: 0 30rpx;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* uview-plus 组件样式覆盖 */\n:deep(.u-form-item__label) {\n  margin-bottom: 16rpx !important;\n}\n\n:deep(.u-input__content) {\n  min-height: 80rpx;\n}\n\n:deep(.u-textarea) {\n  background-color: transparent !important;\n  border-radius: 8rpx !important;\n}\n\n/* 报名确认弹窗样式 */\n.confirm-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.confirm-modal-content {\n  width: 654rpx;\n  height: 420rpx;\n  background: #FFFFFF;\n  border-radius: 16rpx;\n  position: relative;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n}\n\n.modal-header {\n  position: absolute;\n  top: 42rpx;\n  left: 48rpx;\n  display: flex;\n  align-items: center;\n\n  .warning-icon {\n    width: 48rpx;\n    height: 40rpx;\n    margin-right: 16rpx;\n  }\n\n  .modal-title {\n    width: 142rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 36rpx;\n    color: #23232A;\n    line-height: 44rpx;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n  }\n}\n\n.modal-body {\n  position: absolute;\n  top: 158rpx;\n  left: 48rpx;\n\n  .modal-message {\n    width: 558rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 32rpx;\n    color: #23232A;\n    line-height: 44rpx;\n    text-align: left;\n    font-style: normal;\n    text-transform: none;\n  }\n}\n\n.modal-footer {\n  position: absolute;\n  top: 316rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 24rpx;\n\n  .modal-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    transition: opacity 0.2s;\n\n    &:active {\n      opacity: 0.8;\n    }\n  }\n\n  .cancel-btn {\n      width: 292rpx;\n      height: 76rpx;\n      background: rgba(42, 97, 241, 0.1);\n      border-radius: 8rpx;\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n      font-weight: normal;\n      font-size: 28rpx;\n      color: #23232A;\n      line-height: 44rpx;\n  }\n\n  .confirm-btn {\n    width: 292rpx;\n    height: 76rpx;\n    background: #023F98;\n    border-radius: 8rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #ffffff;\n  }\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_event/registration.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onLoad", "onShow", "getEventDetailApi", "getFormDefinitionApi", "submitRegistrationApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6PA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,WAAWC,cAAAA,SAAS,CAAA,CAAE;AAC5B,UAAM,YAAYA,cAAAA,SAAS,CAAA,CAAE;AAC7B,UAAM,YAAYD,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,qBAAqBA,cAAAA,IAAI,EAAE;AAGjC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGlC,UAAM,UAAUA,cAAG,IAAA;AACnB,UAAM,YAAYA,cAAG,IAAA;AAGrB,UAAM,mBAAmB,MAAM;AAC7B,YAAM,QAAQE,cAAAA,MAAI,eAAe,OAAO;AACxC,aAAO,CAAC,CAAC;AAAA,IACX;AAGAC,kBAAM,OAAC,OAAO,YAAY;AACxB,cAAQ,QAAQ,QAAQ;AAExB,UAAI,CAAC,QAAQ,OAAO;AAClBD,sBAAAA,MAAI,GAAG,MAAM,QAAQ;AACrB,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AACP;AAAA,MACD;AAAA,IAGH,CAAC;AAGDE,kBAAAA,OAAO,YAAY;AACjBF,oBAAAA,MAAY,MAAA,OAAA,iDAAA,wBAAwB;AAGpC,YAAM,qBAAqB;AAC3B,iBAAW,QAAQ;AAEnBA,oBAAA,MAAA,MAAA,OAAA,iDAAY,WAAW,kBAAkB;AACzCA,0BAAY,MAAA,OAAA,iDAAA,YAAYA,oBAAI,eAAe,OAAO,CAAC;AAEnD,UAAI,CAAC,oBAAoB;AAEvBA,sBAAAA,MAAA,MAAA,OAAA,iDAAY,4BAA4B;AACxC,kBAAU,QAAQ;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAAA,oEAAY,iBAAiB;AAC7BA,wBAAAA,MAAI,aAAa;AAAA,YACf,MAAM,MAAM;AAEVA,4BAAAA,MAAY,MAAA,OAAA,iDAAA,YAAY;AACxBA,4BAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,YAC5C;AAAA,UACT,CAAO;AAAA,QACF,GAAE,GAAG;AAAA,MAEV,OAAS;AAELA,sBAAAA,MAAY,MAAA,OAAA,iDAAA,qBAAqB;AAGjC,YAAI,QAAQ,OAAO;AACjB,gBAAM,cAAa;AAAA,QACpB;AAGD,YAAI,QAAQ,UAAU,CAAC,WAAW,SAAS,WAAW,MAAM,WAAW,IAAI;AACzE,gBAAM,mBAAkB;AAAA,QAC9B,OAAW;AAEL,oBAAU,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAA,MAAA,OAAA,iDAAY,UAAU;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,oEAAY,oBAAoB;AAGhCA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS,MAAM;AACbA,wBAAAA,MAAY,MAAA,OAAA,iDAAA,QAAQ;AAAA,QACrB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,6FAAa,cAAc,GAAG;AAG9BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,SAAS,MAAM;AACbA,4BAAAA,MAAA,MAAA,OAAA,iDAAY,aAAa;AAAA,YAC1B;AAAA,YACD,MAAM,CAAC,SAAS;AACdA,4BAAA,MAAA,MAAA,QAAA,iDAAa,gBAAgB,IAAI;AAGjCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK;AAAA,gBACL,SAAS,MAAM;AACbA,gCAAAA,MAAY,MAAA,OAAA,iDAAA,SAAS;AAAA,gBACtB;AAAA,gBACD,MAAM,CAAC,SAAS;AACdA,gCAAA,MAAA,MAAA,SAAA,iDAAc,eAAe,IAAI;AACjCA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACtB,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACF,cAAM,WAAW,MAAMG,eAAAA,kBAAkB,QAAQ,KAAK;AACtD,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,oBAAU,QAAQ,SAAS;AAC3BH,wBAAY,MAAA,MAAA,OAAA,iDAAA,aAAa,UAAU,KAAK;AAAA,QAC9C,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,aAAa,KAAK;AAAA,MAEjC;AAAA,IACH;AAGA,UAAM,qBAAqB,YAAY;AACrC,UAAI;AACF,kBAAU,QAAQ;AAElB,cAAM,WAAW,MAAMI,4CAAAA,qBAAqB,QAAQ,KAAK;AAEzD,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAE1C,cAAI;AAEJ,cAAI,OAAO,SAAS,SAAS,UAAU;AACrC,gBAAI;AAEF,+BAAiB,KAAK,MAAM,SAAS,IAAI;AACzCJ,4BAAA,MAAA,MAAA,OAAA,iDAAY,aAAa,cAAc;AAAA,YACxC,SAAQ,YAAY;AACnBA,4BAAA,MAAA,MAAA,SAAA,iDAAc,iBAAiB,UAAU;AACzC,oBAAM,IAAI,MAAM,UAAU;AAAA,YAC3B;AAAA,UACT,OAAa;AAEL,6BAAiB,SAAS;AAAA,UAC3B;AAGD,gBAAM,SAAS,eAAe,UAAU;AAExC,cAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,GAAG;AAC9C,uBAAW,QAAQ;AACnBA,0BAAY,MAAA,MAAA,OAAA,iDAAA,kBAAkB,OAAO,MAAM;AAC3C;AACA;UACR,OAAa;AACLA,+FAAa,qBAAqB,cAAc;AAChD,uBAAW,QAAQ;UACpB;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,aAAa,KAAK;AAChCA,sBAAG,MAAC,GAAG,MAAM,MAAM,WAAW,cAAc;AAC5C,mBAAW,QAAQ;MACvB,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,iBAAW,MAAM,QAAQ,UAAQ;AAC/B,YAAI,KAAK,OAAO;AACd,mBAAS,KAAK,KAAK,IAAI,KAAK,gBAAgB;AAAA,QAC7C;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,iBAAW,MAAM,QAAQ,UAAQ;AAC/B,YAAI,KAAK,SAAS,KAAK,UAAU;AAC/B,gBAAM,QAAQ,CAAC;AAAA,YACb,UAAU;AAAA,YACV,SAAS,IAAI,KAAK,SAAS,WAAW,OAAO,IAAI,GAAG,KAAK,KAAK;AAAA,YAC9D,SAAS,CAAC,QAAQ,QAAQ;AAAA,UAClC,CAAO;AAGD,cAAI,KAAK,SAAS,SAAS;AACzB,kBAAM,KAAK;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,CAAC,MAAM;AAAA,YAC1B,CAAS;AAAA,UACT,WAAiB,KAAK,SAAS,SAAS;AAChC,kBAAM,KAAK;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,CAAC,MAAM;AAAA,YAC1B,CAAS;AAAA,UACF;AAED,oBAAU,KAAK,KAAK,IAAI;AAAA,QACzB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,OAAO,YAAY;AAChD,YAAM,QAAQ,SAAS,KAAK;AAC5B,UAAI,CAAC,SAAS,CAAC;AAAS,eAAO;AAE/B,YAAM,SAAS,QAAQ,KAAK,SAAO,IAAI,UAAU,KAAK;AACtD,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC,KAAK,WAAW,CAAC,MAAM,QAAQ,KAAK,OAAO,GAAG;AACjDA,sBAAAA,MAAI,GAAG,MAAM,QAAQ;AACrB;AAAA,MACD;AAED,yBAAmB,QAAQ,KAAK;AAChC,oBAAc,QAAQ,CAAC,KAAK,OAAO;AACnC,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,YAAM,EAAE,MAAO,IAAG;AAClB,UAAI,SAAS,MAAM,CAAC,KAAK,mBAAmB,OAAO;AACjD,iBAAS,mBAAmB,KAAK,IAAI,MAAM,CAAC,EAAE;AAAA,MAC/C;AACD,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,GAAG,MAAM,SAAS;AACtB;AAAA,MACD;AAED,UAAI;AAEF,cAAM,QAAQ,MAAM;AAGpB,yBAAiB,QAAQ;AAAA,MAE1B,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,GAAG,MAAM,aAAa;AAAA,MAC3B;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,4BAA4B,YAAY;AAC5C;AAEA,UAAI;AACF,qBAAa,QAAQ;AAGrB,cAAM,WAAW,MAAMK,kEAAsB;AAAA,UAC3C,SAAS,QAAQ;AAAA,UACjB;AAAA,QACN,CAAK;AAED,YAAI,SAAS,SAAS,KAAK;AACzBL,wBAAAA,MAAI,GAAG,MAAM,OAAO;AAGpB,cAAI;AACF,kBAAM,qBAAqBA,cAAG,MAAC,eAAe,oBAAoB,KAAK,CAAA;AACvE,+BAAmB,QAAQ,KAAK,IAAI;AAAA,cAClC,cAAc;AAAA,cACd,WAAW,KAAK,IAAK;AAAA,cACrB;AAAA;AAAA,cACA,QAAQ;AAAA;AAAA,YAClB;AACQA,0BAAAA,MAAI,eAAe,sBAAsB,kBAAkB;AAC3DA,0BAAA,MAAA,MAAA,OAAA,iDAAY,0BAA0B,kBAAkB;AAAA,UACzD,SAAQ,OAAO;AACdA,0BAAA,MAAA,MAAA,QAAA,iDAAa,eAAe,KAAK;AAAA,UAElC;AAIC,qBAAW,MAAM;AACfA,0BAAAA,MAAY,MAAA,OAAA,iDAAA,eAAe;AAG3BA,gCAAI,MAAM,aAAa;AACvBA,0BAAAA,MAAA,MAAA,OAAA,iDAAY,oBAAoB;AAGhCA,0BAAAA,MAAI,aAAa;AAAA,cACf,SAAS,MAAM;AACbA,8BAAAA,MAAY,MAAA,OAAA,iDAAA,SAAS;AAGrB,2BAAW,MAAM;AACfA,sCAAI,MAAM,aAAa;AACvBA,gCAAAA,oEAAY,0BAA0B;AAAA,gBACvC,GAAE,GAAG;AAAA,cACP;AAAA,cACD,MAAM,CAAC,UAAU;AACfA,8BAAa,MAAA,MAAA,QAAA,iDAAA,WAAW,KAAK;AAE7BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,KAAK;AAAA,gBACrB,CAAe;AAAA,cACF;AAAA,YACb,CAAW;AAAA,UACF,GAAE,IAAI;AAAA,QAEf,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,MAAM;AAAA,QACvC;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,KAAK;AAE9B,YAAI,MAAM,SAAS;AACjBA,wBAAAA,MAAI,GAAG,MAAM,MAAM,OAAO;AAAA,QAChC,OAAW;AACLA,wBAAAA,MAAI,GAAG,MAAM,YAAY;AAAA,QAC1B;AAAA,MACL,UAAY;AACR,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACznBA,GAAG,WAAW,eAAe;"}