"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const pages_sub_pages_event_api_data_registration = require("./api/data/registration.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  const _easycom_up_loading_page2 = common_vendor.resolveComponent("up-loading-page");
  const _easycom_up_image2 = common_vendor.resolveComponent("up-image");
  (_easycom_up_navbar2 + _easycom_up_loading_page2 + _easycom_up_image2)();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_up_loading_page = () => "../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js";
const _easycom_up_image = () => "../../uni_modules/uview-plus/components/u-image/u-image.js";
if (!Math) {
  (_easycom_up_navbar + _easycom_up_loading_page + _easycom_up_image + EventInfoCard + EventDetailContent + EventActionBar)();
}
const EventInfoCard = () => "../../components/event/EventInfoCard.js";
const EventDetailContent = () => "../../components/event/EventDetailContent.js";
const EventActionBar = () => "../../components/event/EventActionBar.js";
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const eventId = common_vendor.ref(null);
    const eventDetail = common_vendor.ref(null);
    const registrationStatus = common_vendor.ref("loading");
    const isLoading = common_vendor.ref(true);
    const loginCheckTrigger = common_vendor.ref(0);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(44);
    const totalTopPadding = common_vendor.computed(() => {
      return `${statusBarHeight.value + navBarHeight.value}px`;
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 0;
    });
    const getUserToken = () => {
      try {
        return common_vendor.index.getStorageSync("token") || null;
      } catch (e) {
        return null;
      }
    };
    const isLoggedIn = common_vendor.computed(() => {
      loginCheckTrigger.value;
      return !!getUserToken();
    });
    const buttonText = common_vendor.computed(() => {
      if (!eventDetail.value)
        return "加载中...";
      switch (registrationStatus.value) {
        case "loading":
          return "检查状态中...";
        case "registered":
          return "已报名";
        case "not_logged_in":
          return "立即报名";
        case "unregistered":
          if (eventDetail.value.status === 2 || eventDetail.value.status === 3)
            return "活动已结束";
          if (eventDetail.value.status === 4)
            return "活动进行中";
          return "立即报名";
        case "error":
          return "立即报名";
        default:
          return "立即报名";
      }
    });
    const isButtonDisabled = common_vendor.computed(() => {
      if (!eventDetail.value)
        return true;
      if (registrationStatus.value === "loading")
        return true;
      return eventDetail.value.status === 2 || eventDetail.value.status === 3;
    });
    const handleNavBack = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:140", "=== 开始智能导航回退处理 ===");
      common_vendor.index.navigateBack({
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:145", "正常回退成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:148", "正常回退失败:", err);
          common_vendor.index.navigateTo({
            url: "/pages/event/index",
            success: () => {
              common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:154", "跳转到活动列表页面成功");
            },
            fail: (err2) => {
              common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:157", "跳转到活动列表页面失败:", err2);
              common_vendor.index.switchTab({
                url: "/pages/index/index",
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:163", "跳转到首页成功");
                },
                fail: (err3) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_event/detail.vue:166", "所有导航方案都失败了:", err3);
                  common_vendor.index.showToast({
                    title: "导航失败，请重新打开小程序",
                    icon: "none"
                  });
                }
              });
            }
          });
        }
      });
    };
    const fetchRegistrationStatus = async () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:181", "=== 开始独立的报名状态检查 ===");
      try {
        const token = getUserToken();
        if (!token) {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:187", "用户未登录，设置状态为 not_logged_in");
          registrationStatus.value = "not_logged_in";
          return;
        }
        if (!eventId.value) {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:194", "缺少 eventId，无法检查报名状态");
          registrationStatus.value = "error";
          return;
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:199", "前置条件满足，开始调用 API 检查报名状态...");
        try {
          const localRegistrationStatus = common_vendor.index.getStorageSync("registrationStatus") || {};
          const localStatus = localRegistrationStatus[eventId.value];
          if (localStatus && localStatus.isRegistered) {
            common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:206", "从本地存储发现已报名状态，立即更新 UI");
            registrationStatus.value = "registered";
          }
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:210", "读取本地报名状态失败:", error);
        }
        const statusRes = await pages_sub_pages_event_api_data_registration.checkRegistrationStatusApi(eventId.value);
        if (statusRes.code === 200) {
          const isRegistered = statusRes.isRegistered || false;
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:220", "获取报名状态成功:", isRegistered);
          const newStatus = isRegistered ? "registered" : "unregistered";
          if (registrationStatus.value !== newStatus) {
            common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:226", "报名状态发生变化:", registrationStatus.value, "->", newStatus);
            registrationStatus.value = newStatus;
            if (newStatus === "registered" && registrationStatus.value !== "registered") {
              common_vendor.index.$u.toast("报名状态已更新");
            }
          } else {
            common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:234", "报名状态无变化，保持现状");
          }
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:238", "检查报名状态 API 返回错误:", statusRes);
          registrationStatus.value = "error";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/detail.vue:243", "报名状态检查失败:", error);
        registrationStatus.value = "error";
      }
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:247", "=== 报名状态检查完成，最终状态:", registrationStatus.value, " ===");
    };
    common_vendor.onLoad(async (options) => {
      eventId.value = options.id;
      if (!eventId.value) {
        common_vendor.index.$u.toast("活动不存在");
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
      common_vendor.index.$on("dataChanged", async () => {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:263", "📩 收到数据变化事件，重新获取活动详情...");
        if (eventId.value) {
          try {
            common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:267", "开始重新获取活动详情数据...");
            const detailRes = await api_data_event.getEventDetailApi(eventId.value);
            if (detailRes.code === 200) {
              const rawData = detailRes.data;
              rawData.details = rawData.details || "";
              rawData.summary = rawData.summary || "";
              rawData.title = rawData.title || "";
              rawData.location = rawData.location || "";
              rawData.coverImageUrl = rawData.coverImageUrl || "";
              eventDetail.value = rawData;
              common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:282", "活动详情数据已更新，最新报名人数:", rawData.registeredCount);
              await fetchRegistrationStatus();
              common_vendor.index.showToast({
                title: "数据已更新",
                icon: "success",
                duration: 1500
              });
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages_sub/pages_event/detail.vue:294", "重新获取活动详情失败:", error);
          }
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:297", "事件监听：缺少 eventId，跳过数据刷新");
        }
      });
      try {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:302", "开始加载活动详情，eventId:", eventId.value);
        const detailRes = await api_data_event.getEventDetailApi(eventId.value);
        if (detailRes.code === 200) {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:308", "活动详情API调用成功，开始处理数据...");
          const rawData = detailRes.data;
          if (!rawData.details || typeof rawData.details !== "string") {
            rawData.details = "";
          } else {
            rawData.details = rawData.details.trim();
          }
          if (!rawData.summary || typeof rawData.summary !== "string") {
            rawData.summary = "";
          } else {
            rawData.summary = rawData.summary.trim();
          }
          rawData.title = rawData.title || "";
          rawData.location = rawData.location || "";
          rawData.coverImageUrl = rawData.coverImageUrl || "";
          eventDetail.value = rawData;
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:334", "活动详情数据已完全加载并赋值:", {
            id: eventDetail.value.id,
            title: eventDetail.value.title,
            status: eventDetail.value.status
          });
        } else {
          throw new Error(detailRes.msg || "获取活动详情失败");
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:345", "活动详情加载完成，现在开始检查报名状态...");
        await fetchRegistrationStatus();
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:347", "报名状态检查完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/detail.vue:350", "获取活动详情失败", error);
        common_vendor.index.$u.toast(error.message || "加载失败，请稍后重试");
        registrationStatus.value = "error";
      } finally {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:356", "页面加载流程完成，停止loading状态");
        isLoading.value = false;
      }
    });
    common_vendor.onShow(async () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:363", "=== 活动详情页面 onShow 触发 ===");
      loginCheckTrigger.value++;
      const currentToken = getUserToken();
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:372", "当前登录状态:", !!currentToken);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:373", "当前token:", currentToken);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:374", "当前活动ID:", eventId.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:375", "活动详情是否已加载:", !!eventDetail.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:376", "页面是否还在加载中:", isLoading.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:377", "当前报名状态:", registrationStatus.value);
      try {
        const refreshFlag = common_vendor.index.getStorageSync("needRefreshRegistrationStatus");
        if (refreshFlag && refreshFlag.eventId === eventId.value) {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:383", "发现状态刷新标记，立即刷新报名状态:", refreshFlag);
          common_vendor.index.removeStorageSync("needRefreshRegistrationStatus");
          if (eventId.value) {
            await fetchRegistrationStatus();
            return;
          }
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages_sub/pages_event/detail.vue:393", "检查状态刷新标记失败:", e);
      }
      if (!isLoading.value && eventId.value) {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:398", "条件满足，开始重新检查报名状态...");
        await fetchRegistrationStatus();
      } else {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:401", "跳过报名状态检查，原因:", {
          isLoading: isLoading.value,
          hasEventId: !!eventId.value
        });
        if (isLoading.value && eventId.value) {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:408", "页面正在加载中，等待加载完成后再检查报名状态...");
          const unwatch = common_vendor.watch(isLoading, async (newVal) => {
            if (!newVal) {
              common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:411", "页面加载完成，现在开始检查报名状态...");
              await fetchRegistrationStatus();
              unwatch();
            }
          });
        }
      }
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("dataChanged");
    });
    common_vendor.onShareAppMessage(() => {
      var _a, _b;
      return {
        title: ((_a = eventDetail.value) == null ? void 0 : _a.title) || "精彩活动推荐",
        path: `/pages/event/detail?id=${eventId.value}`,
        imageUrl: utils_image.getFullImageUrl((_b = eventDetail.value) == null ? void 0 : _b.coverImageUrl)
      };
    });
    const handleRegistration = () => {
      var _a, _b, _c, _d, _e;
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:438", "=== 点击报名按钮 ===");
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:439", "按钮当前显示文字:", buttonText.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:440", "按钮是否禁用:", isButtonDisabled.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:441", "当前报名状态:", registrationStatus.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:446", "当前登录状态:", isLoggedIn.value);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:447", "当前token:", getUserToken());
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:448", "活动详情:", {
        id: (_a = eventDetail.value) == null ? void 0 : _a.id,
        title: (_b = eventDetail.value) == null ? void 0 : _b.title,
        status: (_c = eventDetail.value) == null ? void 0 : _c.status
      });
      loginCheckTrigger.value++;
      if (!isLoggedIn.value || registrationStatus.value === "not_logged_in") {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:459", "用户未登录，跳转到登录页");
        common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
        return;
      }
      common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:465", "用户已登录");
      if (registrationStatus.value === "registered") {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:469", "用户已报名此活动");
        common_vendor.index.$u.toast("您已报名此活动");
      } else if (registrationStatus.value === "unregistered" || registrationStatus.value === "error") {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:473", "用户未报名，跳转到报名页面");
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:474", "跳转参数:", {
          id: eventId.value,
          title: (_d = eventDetail.value) == null ? void 0 : _d.title
        });
        common_vendor.index.navigateTo({
          url: `/pages_sub/pages_event/registration?id=${eventId.value}&title=${encodeURIComponent(((_e = eventDetail.value) == null ? void 0 : _e.title) || "")}`
        });
      } else {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/detail.vue:484", "报名状态还在加载中，请稍后再试");
        common_vendor.index.$u.toast("正在检查报名状态，请稍后再试");
      }
    };
    const handleShare = () => {
      var _a, _b, _c;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: `/pages/event/detail?id=${eventId.value}`,
        title: ((_a = eventDetail.value) == null ? void 0 : _a.title) || "精彩活动推荐",
        summary: ((_b = eventDetail.value) == null ? void 0 : _b.summary) || "",
        imageUrl: utils_image.getFullImageUrl((_c = eventDetail.value) == null ? void 0 : _c.coverImageUrl),
        success: function(res) {
          common_vendor.index.$u.toast("分享成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages_sub/pages_event/detail.vue:504", "分享失败:", err);
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleNavBack),
        b: common_vendor.p({
          title: "活动详情",
          fixed: true,
          safeAreaInsetTop: true,
          bgColor: "#ffffff",
          leftIcon: "arrow-left",
          leftIconColor: "#333333",
          titleStyle: "color: #333333; font-weight: bold;"
        }),
        c: isLoading.value
      }, isLoading.value ? {
        d: common_vendor.p({
          loadingText: "正在加载活动详情...",
          loadingMode: "spinner"
        })
      } : {}, {
        e: !isLoading.value && eventDetail.value
      }, !isLoading.value && eventDetail.value ? {
        f: common_vendor.p({
          src: common_vendor.unref(utils_image.getFullImageUrl)(eventDetail.value.coverImageUrl),
          mode: "aspectFill",
          height: "420rpx",
          width: "100%"
        }),
        g: common_vendor.p({
          eventDetail: eventDetail.value
        }),
        h: common_vendor.p({
          eventDetail: eventDetail.value
        }),
        i: totalTopPadding.value
      } : {}, {
        j: !isLoading.value && eventDetail.value
      }, !isLoading.value && eventDetail.value ? {
        k: common_vendor.o(handleShare),
        l: common_vendor.o(handleRegistration),
        m: common_vendor.p({
          eventDetail: eventDetail.value,
          isLoading: isLoading.value,
          registrationStatus: registrationStatus.value,
          isButtonDisabled: isButtonDisabled.value,
          buttonText: buttonText.value
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-64edfaab"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_event/detail.js.map
