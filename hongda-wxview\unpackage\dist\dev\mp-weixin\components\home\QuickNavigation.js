"use strict";
const common_vendor = require("../../common/vendor.js");
const api_platform_nav = require("../../api/platform/nav.js");
const utils_config = require("../../utils/config.js");
const utils_navigation = require("../../utils/navigation.js");
const _sfc_main = {
  __name: "QuickNavigation",
  setup(__props) {
    const baseUrl = utils_config.IMAGE_BASE_URL;
    const navList = common_vendor.ref([]);
    const fetchNavData = async () => {
      try {
        const res = await api_platform_nav.getNavList("HOME_QUICK_NAV");
        if (res.data) {
          navList.value = res.data.slice(0, 8);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取快捷导航失败:", error);
      }
    };
    const handleNavClick = (navItem) => {
      utils_navigation.navigateTo(navItem);
    };
    common_vendor.onMounted(() => {
      fetchNavData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: navList.value.length > 0
      }, navList.value.length > 0 ? {
        b: common_vendor.f(navList.value, (item, k0, i0) => {
          return {
            a: common_vendor.unref(baseUrl) + item.iconUrl,
            b: common_vendor.t(item.title),
            c: item.id,
            d: common_vendor.o(($event) => handleNavClick(item), item.id)
          };
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-213218ad"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
