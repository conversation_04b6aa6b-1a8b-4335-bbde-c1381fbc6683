{"version": 3, "file": "article.js", "sources": ["api/content/article.js"], "sourcesContent": ["import {\r\n\tget\r\n} from '@/utils/request' // 假设您的request.js导出了具名get方法\r\n\r\n/**\r\n * 分页获取资讯文章列表\r\n * @param {object} params - 查询参数对象\r\n */\r\nexport function getArticleList(params) {\r\n\t// 创建一个参数副本，避免直接修改原始响应式对象\r\n\tconst processedParams = {\r\n\t\t...params\r\n\t};\r\n\r\n\t// [KEY LOGIC]\r\n\t// 如果 tagIds 为 null、undefined 或空字符串，则从请求参数中删除它。\r\n\t// 这是非常好的实践，可以避免向后端发送无意义的空参数。\r\n\t// 当选择 \"全部\" 时, params.tagIds 为 null, 此条件生效。\r\n\tif (!processedParams.tagIds) {\r\n\t\tdelete processedParams.tagIds;\r\n\t}\r\n\r\n\t// [OPTIONAL REFINEMENT] 同样处理 title\r\n\tif (!processedParams.title || processedParams.title.trim() === '') {\r\n\t\tdelete processedParams.title;\r\n\t}\r\n\r\n\treturn get('/content/articles', processedParams);\r\n}\r\n\r\n/**\r\n * 获取单篇资讯文章详情\r\n * @param {number | string} id - 文章的ID\r\n */\r\nexport function getArticleDetail(id) {\r\n\t// [修正] url只保留从 /content 开始的部分\r\n\treturn get(`/content/article/${id}`);\r\n}"], "names": ["get"], "mappings": ";;AAQO,SAAS,eAAe,QAAQ;AAEtC,QAAM,kBAAkB;AAAA,IACvB,GAAG;AAAA,EACL;AAMC,MAAI,CAAC,gBAAgB,QAAQ;AAC5B,WAAO,gBAAgB;AAAA,EACvB;AAGD,MAAI,CAAC,gBAAgB,SAAS,gBAAgB,MAAM,KAAM,MAAK,IAAI;AAClE,WAAO,gBAAgB;AAAA,EACvB;AAED,SAAOA,cAAG,IAAC,qBAAqB,eAAe;AAChD;AAMO,SAAS,iBAAiB,IAAI;AAEpC,SAAOA,cAAG,IAAC,oBAAoB,EAAE,EAAE;AACpC;;;"}