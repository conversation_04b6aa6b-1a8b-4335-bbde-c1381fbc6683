{"version": 3, "file": "index.js", "sources": ["uni_modules/uview-plus/libs/luch-request/adapters/index.js"], "sourcesContent": ["import buildURL from '../helpers/buildURL'\r\nimport buildFullPath from '../core/buildFullPath'\r\nimport settle from '../core/settle'\r\nimport { isUndefined } from '../utils'\r\n\r\n/**\r\n * 返回可选值存在的配置\r\n * @param {Array} keys - 可选值数组\r\n * @param {Object} config2 - 配置\r\n * @return {{}} - 存在的配置项\r\n */\r\nconst mergeKeys = (keys, config2) => {\r\n    const config = {}\r\n    keys.forEach((prop) => {\r\n        if (!isUndefined(config2[prop])) {\r\n            config[prop] = config2[prop]\r\n        }\r\n    })\r\n    return config\r\n}\r\nexport default (config) => new Promise((resolve, reject) => {\r\n    const fullPath = buildURL(buildFullPath(config.baseURL, config.url), config.params)\r\n    const _config = {\r\n        url: fullPath,\r\n        header: config.header,\r\n        complete: (response) => {\r\n            config.fullPath = fullPath\r\n            response.config = config\r\n            try {\r\n                // 对可能字符串不是json 的情况容错\r\n                if (typeof response.data === 'string') {\r\n                    response.data = JSON.parse(response.data)\r\n                }\r\n                // eslint-disable-next-line no-empty\r\n            } catch (e) {\r\n            }\r\n            settle(resolve, reject, response)\r\n        }\r\n    }\r\n    let requestTask\r\n    if (config.method === 'UPLOAD') {\r\n        delete _config.header['content-type']\r\n        delete _config.header['Content-Type']\r\n        const otherConfig = {\r\n        // #ifdef MP-ALIPAY\r\n            fileType: config.fileType,\r\n            // #endif\r\n            filePath: config.filePath,\r\n            name: config.name\r\n        }\r\n        const optionalKeys = [\r\n        // #ifdef APP-PLUS || H5\r\n            'files',\r\n            // #endif\r\n            // #ifdef H5\r\n            'file',\r\n            // #endif\r\n            // #ifdef H5 || APP-PLUS\r\n            'timeout',\r\n            // #endif\r\n            'formData'\r\n        ]\r\n        requestTask = uni.uploadFile({ ..._config, ...otherConfig, ...mergeKeys(optionalKeys, config) })\r\n    } else if (config.method === 'DOWNLOAD') {\r\n        // #ifdef H5 || APP-PLUS\r\n        if (!isUndefined(config.timeout)) {\r\n            _config.timeout = config.timeout\r\n        }\r\n        // #endif\r\n        requestTask = uni.downloadFile(_config)\r\n    } else {\r\n        const optionalKeys = [\r\n            'data',\r\n            'method',\r\n            // #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN\r\n            'timeout',\r\n            // #endif\r\n            'dataType',\r\n            // #ifndef MP-ALIPAY\r\n            'responseType',\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            'sslVerify',\r\n            // #endif\r\n            // #ifdef H5\r\n            'withCredentials',\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            'firstIpv4'\r\n        // #endif\r\n        ]\r\n        requestTask = uni.request({ ..._config, ...mergeKeys(optionalKeys, config) })\r\n    }\r\n    if (config.getTask) {\r\n        config.getTask(requestTask, config)\r\n    }\r\n})\r\n"], "names": ["isUndefined", "buildURL", "buildFullPath", "settle", "uni"], "mappings": ";;;;;;AAWA,MAAM,YAAY,CAAC,MAAM,YAAY;AACjC,QAAM,SAAS,CAAE;AACjB,OAAK,QAAQ,CAAC,SAAS;AACnB,QAAI,CAACA,6CAAW,YAAC,QAAQ,IAAI,CAAC,GAAG;AAC7B,aAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,IAC9B;AAAA,EACT,CAAK;AACD,SAAO;AACX;AACA,MAAe,UAAA,CAAC,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,QAAM,WAAWC,iEAASC,0DAAAA,cAAc,OAAO,SAAS,OAAO,GAAG,GAAG,OAAO,MAAM;AAClF,QAAM,UAAU;AAAA,IACZ,KAAK;AAAA,IACL,QAAQ,OAAO;AAAA,IACf,UAAU,CAAC,aAAa;AACpB,aAAO,WAAW;AAClB,eAAS,SAAS;AAClB,UAAI;AAEA,YAAI,OAAO,SAAS,SAAS,UAAU;AACnC,mBAAS,OAAO,KAAK,MAAM,SAAS,IAAI;AAAA,QAC3C;AAAA,MAEJ,SAAQ,GAAG;AAAA,MACX;AACDC,gEAAO,SAAS,QAAQ,QAAQ;AAAA,IACnC;AAAA,EACJ;AACD,MAAI;AACJ,MAAI,OAAO,WAAW,UAAU;AAC5B,WAAO,QAAQ,OAAO,cAAc;AACpC,WAAO,QAAQ,OAAO,cAAc;AACpC,UAAM,cAAc;AAAA,MAIhB,UAAU,OAAO;AAAA,MACjB,MAAM,OAAO;AAAA,IAChB;AACD,UAAM,eAAe;AAAA,MAUjB;AAAA,IACH;AACD,kBAAcC,cAAG,MAAC,WAAW,EAAE,GAAG,SAAS,GAAG,aAAa,GAAG,UAAU,cAAc,MAAM,EAAC,CAAE;AAAA,EACvG,WAAe,OAAO,WAAW,YAAY;AAMrC,kBAAcA,cAAAA,MAAI,aAAa,OAAO;AAAA,EAC9C,OAAW;AACH,UAAM,eAAe;AAAA,MACjB;AAAA,MACA;AAAA,MAEA;AAAA,MAEA;AAAA,MAEA;AAAA,IAWH;AACD,kBAAcA,cAAAA,MAAI,QAAQ,EAAE,GAAG,SAAS,GAAG,UAAU,cAAc,MAAM,GAAG;AAAA,EAC/E;AACD,MAAI,OAAO,SAAS;AAChB,WAAO,QAAQ,aAAa,MAAM;AAAA,EACrC;AACL,CAAC;;"}