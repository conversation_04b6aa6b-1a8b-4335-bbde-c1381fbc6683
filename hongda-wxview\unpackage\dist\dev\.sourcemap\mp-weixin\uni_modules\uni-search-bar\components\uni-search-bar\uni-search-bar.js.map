{"version": 3, "file": "uni-search-bar.js", "sources": ["uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3VuaS1zZWFyY2gtYmFyL2NvbXBvbmVudHMvdW5pLXNlYXJjaC1iYXIvdW5pLXNlYXJjaC1iYXIudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"uni-searchbar\">\r\n\t\t<view :style=\"{borderRadius:radius+'px',backgroundColor: bgColor}\" class=\"uni-searchbar__box\"\r\n\t\t\t@click=\"searchClick\">\r\n\t\t\t<view class=\"uni-searchbar__box-icon-search\">\r\n\t\t\t\t<slot name=\"searchIcon\">\r\n\t\t\t\t\t<uni-icons color=\"#c0c4cc\" size=\"18\" type=\"search\" />\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<input v-if=\"show || searchVal\" :focus=\"showSync\" :disabled=\"readonly\" :placeholder=\"placeholderText\" :maxlength=\"maxlength\"\r\n\t\t\t\tclass=\"uni-searchbar__box-search-input\" confirm-type=\"search\" type=\"text\" v-model=\"searchVal\" :style=\"{color:textColor}\"\r\n\t\t\t\t@confirm=\"confirm\" @blur=\"blur\" @focus=\"emitFocus\"/>\r\n\t\t\t<text v-else class=\"uni-searchbar__text-placeholder\">{{ placeholder }}</text>\r\n\t\t\t<view v-if=\"show && (clearButton==='always'||clearButton==='auto'&&searchVal!=='') &&!readonly\"\r\n\t\t\t\tclass=\"uni-searchbar__box-icon-clear\" @click=\"clear\">\r\n\t\t\t\t<slot name=\"clearIcon\">\r\n\t\t\t\t\t<uni-icons color=\"#c0c4cc\" size=\"20\" type=\"clear\" />\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<text @click=\"cancel\" class=\"uni-searchbar__cancel\"\r\n\t\t\tv-if=\"cancelButton ==='always' || show && cancelButton ==='auto'\">{{cancelTextI18n}}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tinitVueI18n\r\n\t} from '@dcloudio/uni-i18n'\r\n\timport messages from './i18n/index.js'\r\n\tconst {\r\n\t\tt\r\n\t} = initVueI18n(messages)\r\n\r\n\t/**\r\n\t * SearchBar 搜索栏\r\n\t * @description 搜索栏组件，通常用于搜索商品、文章等\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=866\r\n\t * @property {Number} radius 搜索栏圆角\r\n\t * @property {Number} maxlength 输入最大长度\r\n\t * @property {String} placeholder 搜索栏Placeholder\r\n\t * @property {String} clearButton = [always|auto|none] 是否显示清除按钮\r\n\t * \t@value always 一直显示\r\n\t * \t@value auto 输入框不为空时显示\r\n\t * \t@value none 一直不显示\r\n\t * @property {String} cancelButton = [always|auto|none] 是否显示取消按钮\r\n\t * \t@value always 一直显示\r\n\t * \t@value auto 输入框不为空时显示\r\n\t * \t@value none 一直不显示\r\n\t * @property {String} cancelText 取消按钮的文字\r\n\t * @property {String} bgColor 输入框背景颜色\r\n\t * @property {String} textColor 输入文字颜色\r\n\t * @property {Boolean} focus 是否自动聚焦\r\n\t * @property {Boolean} readonly 组件只读，不能有任何操作，只做展示\r\n\t * @pages_event {Function} confirm uniSearchBar 的输入框 confirm 事件，返回参数为uniSearchBar的value，e={value:Number}\r\n\t * @pages_event {Function} input uniSearchBar 的 value 改变时触发事件，返回参数为uniSearchBar的value，e=value\r\n\t * @pages_event {Function} cancel 点击取消按钮时触发事件，返回参数为uniSearchBar的value，e={value:Number}\r\n\t * @pages_event {Function} clear 点击清除按钮时触发事件，返回参数为uniSearchBar的value，e={value:Number}\r\n\t * @pages_event {Function} blur input失去焦点时触发事件，返回参数为uniSearchBar的value，e={value:Number}\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: \"UniSearchBar\",\r\n\t\temits: ['input', 'update:modelValue', 'clear', 'cancel', 'confirm', 'blur', 'focus'],\r\n\t\tprops: {\r\n\t\t\tplaceholder: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tradius: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\tclearButton: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"auto\"\r\n\t\t\t},\r\n\t\t\tcancelButton: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"auto\"\r\n\t\t\t},\r\n\t\t\tcancelText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#F8F8F8\"\r\n\t\t\t},\r\n\t\t\ttextColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#000000\"\r\n\t\t\t},\r\n\t\t\tmaxlength: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tfocus: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\treadonly: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: false,\r\n\t\t\t\tshowSync: false,\r\n\t\t\t\tsearchVal: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcancelTextI18n() {\r\n\t\t\t\treturn this.cancelText || t(\"uni-search-bar.cancel\")\r\n\t\t\t},\r\n\t\t\tplaceholderText() {\r\n\t\t\t\treturn this.placeholder || t(\"uni-search-bar.placeholder\")\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// #ifndef VUE3\r\n\t\t\tvalue: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.searchVal = newVal\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.show = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tmodelValue: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.searchVal = newVal\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.show = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tfocus: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tif(this.readonly) return\r\n\t\t\t\t\t\tthis.show = true;\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.showSync = true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsearchVal(newVal, oldVal) {\r\n\t\t\t\tthis.$emit(\"input\", newVal)\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.$emit(\"update:modelValue\", newVal)\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsearchClick() {\r\n\t\t\t\tif(this.readonly) return\r\n\t\t\t\tif (this.show) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.show = true;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.showSync = true\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclear() {\r\n\t\t\t\tthis.searchVal = \"\"\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$emit(\"clear\", { value: \"\" })\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcancel() {\r\n\t\t\t\tif(this.readonly) return\r\n\t\t\t\tthis.$emit(\"cancel\", {\r\n\t\t\t\t\tvalue: this.searchVal\r\n\t\t\t\t});\r\n\t\t\t\tthis.searchVal = \"\"\r\n\t\t\t\tthis.show = false\r\n\t\t\t\tthis.showSync = false\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tuni.hideKeyboard()\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.key.hideSoftKeybord()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tconfirm() {\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.key.hideSoftKeybord()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$emit(\"confirm\", {\r\n\t\t\t\t\tvalue: this.searchVal\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tblur() {\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.key.hideSoftKeybord()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.$emit(\"blur\", {\r\n\t\t\t\t\tvalue: this.searchVal\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\temitFocus(e) {\r\n\t\t\t\tthis.$emit(\"focus\", e.detail)\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-searchbar-height: 36px;\r\n\r\n\t.uni-searchbar {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tposition: relative;\r\n\t\tpadding: 10px;\r\n\t\t// background-color: #fff;\r\n\t}\r\n\r\n\t.uni-searchbar__box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\tjustify-content: left;\r\n\t\t/* #endif */\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\theight: $uni-searchbar-height;\r\n\t\tpadding: 5px 8px 5px 0px;\r\n\t}\r\n\r\n\t.uni-searchbar__box-icon-search {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\t// width: 32px;\r\n\t\tpadding: 0 8px;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tcolor: #B3B3B3;\r\n\t}\r\n\r\n\t.uni-searchbar__box-search-input {\r\n\t\tflex: 1;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 5px;\r\n\t\tmargin-top: 1px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbackground-color: inherit;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-searchbar__box-icon-clear {\r\n\t\talign-items: center;\r\n\t\tline-height: 24px;\r\n\t\tpadding-left: 8px;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-searchbar__text-placeholder {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #B3B3B3;\r\n\t\tmargin-left: 5px;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.uni-searchbar__cancel {\r\n\t\tpadding-left: 10px;\r\n\t\tline-height: $uni-searchbar-height;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333333;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue'\nwx.createComponent(Component)"], "names": ["initVueI18n", "messages", "uni"], "mappings": ";;;AA8BC,MAAM;AAAA,EACL;IACGA,cAAAA,YAAYC,4DAAAA,QAAQ;AA6BxB,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,qBAAqB,SAAS,UAAU,WAAW,QAAQ,OAAO;AAAA,EACnF,OAAO;AAAA,IACN,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,iBAAiB;AAChB,aAAO,KAAK,cAAc,EAAE,uBAAuB;AAAA,IACnD;AAAA,IACD,kBAAkB;AACjB,aAAO,KAAK,eAAe,EAAE,4BAA4B;AAAA,IAC1D;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IAaN,YAAY;AAAA,MACX,WAAW;AAAA,MACX,QAAQ,QAAQ;AACf,aAAK,YAAY;AACjB,YAAI,QAAQ;AACX,eAAK,OAAO;AAAA,QACb;AAAA,MACD;AAAA,IACA;AAAA,IAED,OAAO;AAAA,MACN,WAAW;AAAA,MACX,QAAQ,QAAQ;AACf,YAAI,QAAQ;AACX,cAAG,KAAK;AAAU;AAClB,eAAK,OAAO;AACZ,eAAK,UAAU,MAAM;AACpB,iBAAK,WAAW;AAAA,WAChB;AAAA,QACF;AAAA,MACD;AAAA,IACA;AAAA,IACD,UAAU,QAAQ,QAAQ;AACzB,WAAK,MAAM,SAAS,MAAM;AAE1B,WAAK,MAAM,qBAAqB,MAAM;AAAA,IAEvC;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,cAAc;AACb,UAAG,KAAK;AAAU;AAClB,UAAI,KAAK,MAAM;AACd;AAAA,MACD;AACA,WAAK,OAAO;AACZ,WAAK,UAAU,MAAM;AACpB,aAAK,WAAW;AAAA,OAChB;AAAA,IACD;AAAA,IACD,QAAQ;AACP,WAAK,YAAY;AACjB,WAAK,UAAU,MAAM;AACpB,aAAK,MAAM,SAAS,EAAE,OAAO,GAAC,CAAG;AAAA,OACjC;AAAA,IACD;AAAA,IACD,SAAS;AACR,UAAG,KAAK;AAAU;AAClB,WAAK,MAAM,UAAU;AAAA,QACpB,OAAO,KAAK;AAAA,MACb,CAAC;AACD,WAAK,YAAY;AACjB,WAAK,OAAO;AACZ,WAAK,WAAW;AAEhBC,oBAAAA,MAAI,aAAa;AAAA,IAKjB;AAAA,IACD,UAAU;AAETA,oBAAG,MAAC,aAAY;AAKhB,WAAK,MAAM,WAAW;AAAA,QACrB,OAAO,KAAK;AAAA,OACZ;AAAA,IACD;AAAA,IACD,OAAO;AAENA,oBAAG,MAAC,aAAY;AAKhB,WAAK,MAAM,QAAQ;AAAA,QAClB,OAAO,KAAK;AAAA,OACZ;AAAA,IACD;AAAA,IACD,UAAU,GAAG;AACZ,WAAK,MAAM,SAAS,EAAE,MAAM;AAAA,IAC7B;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnOF,GAAG,gBAAgB,SAAS;"}