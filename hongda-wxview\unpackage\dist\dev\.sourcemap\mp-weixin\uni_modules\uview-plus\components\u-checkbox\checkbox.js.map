{"version": 3, "file": "checkbox.js", "sources": ["uni_modules/uview-plus/components/u-checkbox/checkbox.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-23 21:06:59\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkbox.js\r\n */\r\nexport default {\r\n    // checkbox组件\r\n    checkbox: {\r\n        name: '',\r\n        shape: '',\r\n        size: '',\r\n        checkbox: false,\r\n        disabled: '',\r\n        activeColor: '',\r\n        inactiveColor: '',\r\n        iconSize: '',\r\n        iconColor: '',\r\n        label: '',\r\n        labelSize: '',\r\n        labelColor: '',\r\n        labelDisabled: ''\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAClB;AACL;;"}