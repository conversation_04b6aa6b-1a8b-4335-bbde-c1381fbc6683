{"version": 3, "file": "u-subsection.js", "sources": ["uni_modules/uview-plus/components/u-subsection/u-subsection.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN1YnNlY3Rpb24vdS1zdWJzZWN0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view\r\n        class=\"u-subsection\"\r\n        ref=\"u-subsection\"\r\n        :class=\"[`u-subsection--${mode}`]\"\r\n        :style=\"[addStyle(customStyle), wrapperStyle]\"\r\n    >\r\n        <view\r\n            class=\"u-subsection__bar cursor-pointer\"\r\n            ref=\"u-subsection__bar\"\r\n            :style=\"[barStyle]\"\r\n            :class=\"[\r\n                mode === 'button' && 'u-subsection--button__bar',\r\n                innerCurrent === 0 &&\r\n                    mode === 'subsection' &&\r\n                    'u-subsection__bar--first',\r\n                innerCurrent > 0 &&\r\n                innerCurrent < list.length - 1 &&\r\n                    mode === 'subsection' &&\r\n                    'u-subsection__bar--center',\r\n                innerCurrent === list.length - 1 &&\r\n                    mode === 'subsection' &&\r\n                    'u-subsection__bar--last',\r\n            ]\"\r\n        ></view>\r\n        <view\r\n            class=\"u-subsection__item cursor-pointer\"\r\n            :class=\"[\r\n                `u-subsection__item--${index}`,\r\n                index < list.length - 1 &&\r\n                    'u-subsection__item--no-border-right',\r\n                index === 0 && 'u-subsection__item--first',\r\n                index === list.length - 1 && 'u-subsection__item--last',\r\n                getTextViewDisableClass(index),\r\n            ]\"\r\n            :ref=\"`u-subsection__item--${index}`\"\r\n            :style=\"[itemStyle(index)]\"\r\n            @tap=\"clickHandler(index)\"\r\n            v-for=\"(item, index) in list\"\r\n            :key=\"index\"\r\n        >\r\n            <text\r\n                class=\"u-subsection__item__text\"\r\n                :class=\"[disabled ? 'u-subsection--disabled' : '']\"\r\n                :style=\"[textStyle(index,item)]\"\r\n                >{{ getText(item) }}</text\r\n            >\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n// #ifdef APP-NVUE\r\nconst dom = uni.requireNativePlugin(\"dom\");\r\nconst animation = uni.requireNativePlugin(\"animation\");\r\n// #endif\r\nimport { props } from \"./props.js\";\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addStyle, addUnit, sleep } from '../../libs/function/index';\r\n/**\r\n * Subsection 分段器\r\n * @description 该分段器一般用于用户从几个选项中选择某一个的场景\r\n * @tutorial https://ijry.github.io/uview-plus/components/subsection.html\r\n * @property {Array}\t\t\tlist\t\t\t        tab的数据\r\n * @property {String ｜ Number}\tcurrent\t\t\t        当前活动的tab的index（默认 0 ）\r\n * @property {String}\t\t\tactiveColor\t\t        激活时的颜色（默认 '#3c9cff' ）\r\n * @property {String}\t\t\tinactiveColor\t        未激活时的颜色（默认 '#303133' ）\r\n * @property {String}\t\t\tmode\t\t\t        模式选择，mode=button为按钮形式，mode=subsection时为分段模式（默认 'button' ）\r\n * @property {String ｜ Number}\tfontSize\t\t        字体大小，单位px（默认 12 ）\r\n * @property {Boolean}\t\t\tbold\t\t\t        激活选项的字体是否加粗（默认 true ）\r\n * @property {String}\t\t\tbgColor\t\t\t        组件背景颜色，mode为button时有效（默认 '#eeeeef' ）\r\n * @property {Object}\t\t\tcustomStyle\t\t        定义需要用到的外部样式\r\n * @property {String}\t        keyName\t                从`list`元素对象中读取的键名（默认 'name' ）\r\n * @property {String}\t        activeColorKeyName      从`list`元素对象中读取激活时的颜色（默认 'activeColorKey' ）  如果存在字段 优先级大于 activeColor\r\n * @property {String}\t        inactiveColorKeyName    从`list`元素对象中读取未激活时的颜色 （默认 'inactiveColorKey' ）如果存在字段 优先级大于 inactiveColor\r\n * @property {Boolean}\t        disabled                是否禁用分段器 （默认 false ）\r\n *\r\n * @pages_event {Function} change\t\t分段器选项发生改变时触发  回调 index：选项的index索引值，从0开始\r\n * @example <u-subsection :list=\"list\" :current=\"curNow\" @change=\"sectionChange\"></u-subsection>\r\n */\r\nexport default {\r\n    name: \"u-subsection\",\r\n    mixins: [mpMixin, mixin, props],\r\n    data() {\r\n        return {\r\n            // 组件尺寸\r\n            itemRect: {\r\n                width: 0,\r\n                height: 0,\r\n            },\r\n            innerCurrent: '',\r\n            windowResizeCallback: {}\r\n        };\r\n    },\r\n    watch: {\r\n        list(newValue, oldValue) {\r\n            this.init();\r\n        },\r\n        current: {\r\n            immediate: true,\r\n            handler(n) {\r\n                if (n !== this.innerCurrent) {\r\n                    this.innerCurrent = n\r\n                }\r\n                // #ifdef APP-NVUE\r\n                // 在安卓nvue上，如果通过translateX进行位移，到最后一个时，会导致右侧无法绘制圆角\r\n                // 故用animation模块进行位移\r\n                const ref = this.$refs?.[\"u-subsection__bar\"]?.ref;\r\n                // 不存在ref的时候(理解为第一次初始化时，需要渲染dom，进行一定延时再获取ref)，这里的100ms是经过测试得出的结果(某些安卓需要延时久一点)，勿随意修改\r\n                sleep(ref ? 0 : 100).then(() => {\r\n                    animation.transition(this.$refs[\"u-subsection__bar\"].ref, {\r\n                        styles: {\r\n                            transform: `translateX(${\r\n                                n * this.itemRect.width\r\n                            }px)`,\r\n                            transformOrigin: \"center center\",\r\n                        },\r\n                        duration: 300,\r\n                    });\r\n                });\r\n                // #endif\r\n            },\r\n        },\r\n    },\r\n    computed: {\r\n        wrapperStyle() {\r\n            const style = {};\r\n            // button模式时，设置背景色\r\n            if (this.mode === \"button\") {\r\n                style.backgroundColor = this.bgColor;\r\n            }\r\n            return style;\r\n        },\r\n        // 滑块的样式\r\n        barStyle() {\r\n            const style = {};\r\n            style.width = `${this.itemRect.width}px`;\r\n            style.height = `${this.itemRect.height}px`;\r\n            // 通过translateX移动滑块，其移动的距离为索引*item的宽度\r\n            // #ifndef APP-NVUE\r\n            style.transform = `translateX(${\r\n                this.innerCurrent * this.itemRect.width\r\n            }px)`;\r\n            // #endif\r\n            if (this.mode === \"subsection\") {\r\n                // 在subsection模式下，需要动态设置滑块的圆角，因为移动滑块使用的是translateX，无法通过父元素设置overflow: hidden隐藏滑块的直角\r\n                style.backgroundColor = this.activeColor;\r\n            }\r\n            return style;\r\n        },\r\n        // 分段器item的样式\r\n        itemStyle(index) {\r\n            return (index) => {\r\n                const style = {};\r\n                if (this.mode === \"subsection\") {\r\n                    // 设置border的样式\r\n                    style.borderColor = this.activeColor;\r\n                    style.borderWidth = \"1px\";\r\n                    style.borderStyle = \"solid\";\r\n                }\r\n                return style;\r\n            };\r\n        },\r\n        // 分段器文字颜色\r\n        textStyle(index,item) {\r\n            return (index,item) => {\r\n                const style = {};\r\n                style.fontWeight =\r\n                    this.bold && this.innerCurrent === index ? \"bold\" : \"normal\";\r\n                style.fontSize = addUnit(this.fontSize);\r\n\r\n                let activeColorTemp = null;\r\n                let inactiveColorTemp = null;\r\n                // 如果是对象并且设置了对应的背景色字段 则优先使用设置的字段\r\n                if(typeof item === 'object' && item[this.activeColorKeyName]){\r\n                    activeColorTemp = item[this.activeColorKeyName];\r\n                }\r\n                if(typeof item === 'object' && item[this.inactiveColorKeyName]){\r\n                    inactiveColorTemp = item[this.inactiveColorKeyName];\r\n                }\r\n\r\n                // subsection模式下，激活时默认为白色的文字\r\n                if (this.mode === \"subsection\") {\r\n                    // 判断当前是否激活\r\n                    if(this.innerCurrent === index){\r\n                        // 判断当前是否有自定义的颜色\r\n                        style.color = activeColorTemp ? activeColorTemp : '#FFF'\r\n                        // style.color = activeColorTemp ? activeColorTemp : this.activeColor\r\n                    }\r\n                    else{\r\n                        // 判断当前是否有自定义的颜色\r\n                        style.color = inactiveColorTemp ? inactiveColorTemp : this.inactiveColor;\r\n                    }\r\n                }\r\n                else {\r\n                    // button模式下，激活时文字颜色默认为activeColor\r\n                    if(this.innerCurrent === index){\r\n                        // 判断当前是否有自定义的颜色\r\n                        style.color = activeColorTemp ? activeColorTemp : this.activeColor\r\n                    }\r\n                    else{\r\n                        // 判断当前是否有自定义的颜色\r\n                        style.color = inactiveColorTemp ? inactiveColorTemp : this.inactiveColor;\r\n                    }\r\n                }\r\n                return style;\r\n            };\r\n        },\r\n    },\r\n    mounted() {\r\n        this.init();\r\n        this.windowResizeCallback = (res) => {\r\n            this.init();\r\n        }\r\n        uni.onWindowResize(this.windowResizeCallback)\r\n    },\r\n    beforeUnmount() {\r\n        uni.offWindowResize(this.windowResizeCallback)\r\n    },\r\n\temits: [\"change\", \"update:current\"],\r\n    methods: {\r\n        addStyle,\r\n        init() {\r\n            this.innerCurrent = this.current\r\n            sleep().then(() => this.getRect());\r\n        },\r\n\t\t// 判断展示文本\r\n\t\tgetText(item) {\r\n\t\t\treturn typeof item === 'object' ? item[this.keyName] : item\r\n\t\t},\r\n        // 获取组件的尺寸\r\n        getRect() {\r\n            // #ifndef APP-NVUE\r\n            this.$uGetRect(\".u-subsection__item--0\").then((size) => {\r\n                this.itemRect = size;\r\n            });\r\n            // #endif\r\n\r\n            // #ifdef APP-NVUE\r\n            const ref = this.$refs[\"u-subsection__item--0\"][0];\r\n            ref &&\r\n                dom.getComponentRect(ref, (res) => {\r\n                    this.itemRect = res.size;\r\n                });\r\n            // #endif\r\n        },\r\n        clickHandler(index) {\r\n            // 防止某些平台 css 无法阻止点击事件 在此处拦截\r\n            if(this.disabled){\r\n                return\r\n            }\r\n            this.innerCurrent = index;\r\n\t\t\tthis.$emit('update:current', index);\r\n            this.$emit(\"change\", index);\r\n        },\r\n        /**\r\n         * 获取当前文字区域的 class禁用样式\r\n         * @param index\r\n         */\r\n        getTextViewDisableClass(index){\r\n            // 禁用状态下\r\n            if(this.disabled){\r\n                // 判断模式\r\n                if(this.mode === 'button'){\r\n                    return 'item-button--disabled'\r\n                }\r\n                else{\r\n                    return 'item-subsection--disabled'\r\n                }\r\n            }\r\n            return '';\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.u-subsection {\r\n    @include flex;\r\n    position: relative;\r\n    overflow: hidden;\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\tbox-sizing: border-box;\r\n\t/* #endif */\r\n\r\n    &--button {\r\n        height: 34px;\r\n        background-color: rgb(238, 238, 239);\r\n        padding: 3px;\r\n        border-radius: 4px;\r\n        align-items: stretch;\r\n\r\n        &__bar {\r\n            background-color: #ffffff;\r\n            border-radius: 4px !important;\r\n        }\r\n    }\r\n\r\n    &--subsection {\r\n        height: 32px;\r\n    }\r\n\r\n    &__bar {\r\n        position: absolute;\r\n        /* #ifndef APP-NVUE */\r\n        transition-property: transform, color;\r\n        transition-duration: 0.3s;\r\n        transition-timing-function: ease-in-out;\r\n        /* #endif */\r\n\r\n        &--first {\r\n            border-top-left-radius: 4px;\r\n            border-bottom-left-radius: 4px;\r\n            border-top-right-radius: 0px;\r\n            border-bottom-right-radius: 0px;\r\n        }\r\n\r\n        &--center {\r\n            border-top-left-radius: 0px;\r\n            border-bottom-left-radius: 0px;\r\n            border-top-right-radius: 0px;\r\n            border-bottom-right-radius: 0px;\r\n        }\r\n\r\n        &--last {\r\n            border-top-left-radius: 0px;\r\n            border-bottom-left-radius: 0px;\r\n            border-top-right-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n    }\r\n\r\n    &__item {\r\n        @include flex;\r\n        flex: 1;\r\n        justify-content: center;\r\n        align-items: center;\r\n        // vue环境下，需要设置相对定位，因为滑块为绝对定位，item需要在滑块的上面\r\n        position: relative;\r\n\r\n        &--no-border-right {\r\n            border-right-width: 0 !important;\r\n        }\r\n\r\n        &--first {\r\n            border-top-left-radius: 4px;\r\n            border-bottom-left-radius: 4px;\r\n        }\r\n\r\n        &--last {\r\n            border-top-right-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        &__text {\r\n            font-size: 12px;\r\n            line-height: 14px;\r\n            @include flex;\r\n            align-items: center;\r\n            transition-property: color;\r\n            transition-duration: 0.3s;\r\n        }\r\n    }\r\n\r\n    // 禁用标志\r\n    //\r\n    //&--subsectio--disabled{\r\n    //    cursor: no-drop;\r\n    //    background: #FFFFFF !important;\r\n    //    color: #BDBDBD !important;\r\n    //    border-color: #BDBDBD !important;\r\n    //}\r\n    //\r\n    //&--button--disabled{\r\n    //    cursor: no-drop;\r\n    //    color: #BDBDBD !important;\r\n    //    border-color: #BDBDBD !important;\r\n    //}\r\n\r\n}\r\n\r\n.item-button--disabled{\r\n    cursor: no-drop;\r\n    color: #BDBDBD !important;\r\n    border-color: #BDBDBD !important;\r\n    text{\r\n        color: #BDBDBD !important;\r\n    }\r\n}\r\n.item-subsection--disabled{\r\n    cursor: no-drop;\r\n    background: #FFFFFF !important;\r\n    color: #BDBDBD !important;\r\n    border-color: #BDBDBD !important;\r\n    text{\r\n        color: #BDBDBD !important;\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-subsection/u-subsection.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "index", "item", "addUnit", "uni", "addStyle", "sleep"], "mappings": ";;;;;;AAiFA,MAAK,YAAU;AAAA,EACX,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,wDAAK;AAAA,EAC9B,OAAO;AACH,WAAO;AAAA;AAAA,MAEH,UAAU;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACX;AAAA,MACD,cAAc;AAAA,MACd,sBAAsB,CAAC;AAAA;EAE9B;AAAA,EACD,OAAO;AAAA,IACH,KAAK,UAAU,UAAU;AACrB,WAAK,KAAI;AAAA,IACZ;AAAA,IACD,SAAS;AAAA,MACL,WAAW;AAAA,MACX,QAAQ,GAAG;AACP,YAAI,MAAM,KAAK,cAAc;AACzB,eAAK,eAAe;AAAA,QACxB;AAAA,MAkBH;AAAA,IACJ;AAAA,EACJ;AAAA,EACD,UAAU;AAAA,IACN,eAAe;AACX,YAAM,QAAQ,CAAA;AAEd,UAAI,KAAK,SAAS,UAAU;AACxB,cAAM,kBAAkB,KAAK;AAAA,MACjC;AACA,aAAO;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AACP,YAAM,QAAQ,CAAA;AACd,YAAM,QAAQ,GAAG,KAAK,SAAS,KAAK;AACpC,YAAM,SAAS,GAAG,KAAK,SAAS,MAAM;AAGtC,YAAM,YAAY,cACd,KAAK,eAAe,KAAK,SAAS,KACrC;AAED,UAAI,KAAK,SAAS,cAAc;AAE5B,cAAM,kBAAkB,KAAK;AAAA,MACjC;AACA,aAAO;AAAA,IACV;AAAA;AAAA,IAED,UAAU,OAAO;AACb,aAAO,CAACC,WAAU;AACd,cAAM,QAAQ,CAAA;AACd,YAAI,KAAK,SAAS,cAAc;AAE5B,gBAAM,cAAc,KAAK;AACzB,gBAAM,cAAc;AACpB,gBAAM,cAAc;AAAA,QACxB;AACA,eAAO;AAAA;IAEd;AAAA;AAAA,IAED,UAAU,OAAM,MAAM;AAClB,aAAO,CAACA,QAAMC,UAAS;AACnB,cAAM,QAAQ,CAAA;AACd,cAAM,aACF,KAAK,QAAQ,KAAK,iBAAiBD,SAAQ,SAAS;AACxD,cAAM,WAAWE,0CAAAA,QAAQ,KAAK,QAAQ;AAEtC,YAAI,kBAAkB;AACtB,YAAI,oBAAoB;AAExB,YAAG,OAAOD,UAAS,YAAYA,MAAK,KAAK,kBAAkB,GAAE;AACzD,4BAAkBA,MAAK,KAAK,kBAAkB;AAAA,QAClD;AACA,YAAG,OAAOA,UAAS,YAAYA,MAAK,KAAK,oBAAoB,GAAE;AAC3D,8BAAoBA,MAAK,KAAK,oBAAoB;AAAA,QACtD;AAGA,YAAI,KAAK,SAAS,cAAc;AAE5B,cAAG,KAAK,iBAAiBD,QAAM;AAE3B,kBAAM,QAAQ,kBAAkB,kBAAkB;AAAA,UAEtD,OACI;AAEA,kBAAM,QAAQ,oBAAoB,oBAAoB,KAAK;AAAA,UAC/D;AAAA,QACJ,OACK;AAED,cAAG,KAAK,iBAAiBA,QAAM;AAE3B,kBAAM,QAAQ,kBAAkB,kBAAkB,KAAK;AAAA,UAC3D,OACI;AAEA,kBAAM,QAAQ,oBAAoB,oBAAoB,KAAK;AAAA,UAC/D;AAAA,QACJ;AACA,eAAO;AAAA;IAEd;AAAA,EACJ;AAAA,EACD,UAAU;AACN,SAAK,KAAI;AACT,SAAK,uBAAuB,CAAC,QAAQ;AACjC,WAAK,KAAI;AAAA,IACb;AACAG,wBAAI,eAAe,KAAK,oBAAoB;AAAA,EAC/C;AAAA,EACD,gBAAgB;AACZA,wBAAI,gBAAgB,KAAK,oBAAoB;AAAA,EAChD;AAAA,EACJ,OAAO,CAAC,UAAU,gBAAgB;AAAA,EAC/B,SAAS;AAAA,IACL,UAAAC,0CAAQ;AAAA,IACR,OAAO;AACH,WAAK,eAAe,KAAK;AACzBC,gDAAAA,MAAO,EAAC,KAAK,MAAM,KAAK,QAAS,CAAA;AAAA,IACpC;AAAA;AAAA,IAEP,QAAQ,MAAM;AACb,aAAO,OAAO,SAAS,WAAW,KAAK,KAAK,OAAO,IAAI;AAAA,IACvD;AAAA;AAAA,IAEK,UAAU;AAEN,WAAK,UAAU,wBAAwB,EAAE,KAAK,CAAC,SAAS;AACpD,aAAK,WAAW;AAAA,MACpB,CAAC;AAAA,IAUJ;AAAA,IACD,aAAa,OAAO;AAEhB,UAAG,KAAK,UAAS;AACb;AAAA,MACJ;AACA,WAAK,eAAe;AAC7B,WAAK,MAAM,kBAAkB,KAAK;AACzB,WAAK,MAAM,UAAU,KAAK;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,wBAAwB,OAAM;AAE1B,UAAG,KAAK,UAAS;AAEb,YAAG,KAAK,SAAS,UAAS;AACtB,iBAAO;AAAA,QACX,OACI;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACH;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjRA,GAAG,gBAAgB,SAAS;"}