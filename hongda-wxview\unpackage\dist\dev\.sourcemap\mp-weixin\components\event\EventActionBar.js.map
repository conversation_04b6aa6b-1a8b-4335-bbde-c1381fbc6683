{"version": 3, "file": "EventActionBar.js", "sources": ["components/event/EventActionBar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRBY3Rpb25CYXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"bottom-action-bar\" v-if=\"!isLoading && eventDetail\">\n    <up-button\n      @click=\"$emit('share')\"\n      :customStyle=\"{\n        width: '214rpx',\n        height: '76rpx',\n        margin: '0',\n        padding: '0',\n        border: 'none',\n        backgroundColor: 'rgba(42, 97, 241, 0.2)',\n        borderRadius: '38rpx'\n      }\"\n    >\n      <view class=\"share-button-content\">\n        <image class=\"share-icon\" :src=\"shareIconUrl\" mode=\"aspectFit\"></image>\n        <text class=\"share-text\">分享</text>\n      </view>\n    </up-button>\n\n    <up-button\n      type=\"primary\"\n      :disabled=\"isButtonDisabled\"\n      @click=\"$emit('register')\"\n      :customStyle=\"{\n        width: '464rpx',\n        height: '76rpx',\n        margin: '0',\n        backgroundColor: registrationStatus === 'registered' ? '#C4CFD1' : (isButtonDisabled ? '#cccccc' : '#023F98'),\n        borderColor: registrationStatus === 'registered' ? '#C4CFD1' : (isButtonDisabled ? '#cccccc' : '#023F98'),\n        borderRadius: '38rpx',\n        color: '#ffffff',\n        fontFamily: 'Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif',\n        fontWeight: 'normal',\n        fontSize: '28rpx'\n      }\"\n    >\n      {{ buttonText }}\n    </up-button>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\ndefineProps({\n  eventDetail: { type: Object, required: true },\n  isLoading: { type: Boolean, default: false },\n  registrationStatus: { type: String, required: true },\n  isButtonDisabled: { type: Boolean, required: true },\n  buttonText: { type: String, required: true }\n})\n\n// 静态资源 URL（不再使用本地兜底）\nconst shareIconUrl = ref('')\n\n// 组件挂载时读取静态资源配置\nonMounted(() => {\n  const assets = uni.getStorageSync('staticAssets')\n  \n  shareIconUrl.value = assets?.detail_icon_share || ''\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.bottom-action-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  box-sizing: border-box;\n  z-index: 100;\n  height: calc(156rpx + env(safe-area-inset-bottom));\n  background-color: #FFFFFF;\n  border-top: 2rpx solid #EEEEEE;\n  border-radius: 0;\n  box-shadow: none;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 30rpx;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.share-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 8rpx;\n}\n\n.share-text {\n  width: 56rpx;\n  height: 44rpx;\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #023F98;\n  line-height: 44rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n}\n\n.share-button-content {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n:deep(.up-button--square) {\n  border-radius: 10rpx !important;\n}\n\n:deep(.up-button--primary) {\n  border-radius: 44rpx !important;\n}\n</style>\n\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventActionBar.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAsDA,UAAM,eAAeA,cAAG,IAAC,EAAE;AAG3BC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,mBAAa,SAAQ,iCAAQ,sBAAqB;AAAA,IACpD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5DD,GAAG,gBAAgB,SAAS;"}