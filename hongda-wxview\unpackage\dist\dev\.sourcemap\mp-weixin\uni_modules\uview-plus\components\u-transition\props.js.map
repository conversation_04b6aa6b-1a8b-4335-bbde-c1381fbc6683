{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-transition/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否展示组件\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.transition.show\r\n        },\r\n        // 使用的动画模式\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.transition.mode\r\n        },\r\n        // 动画的执行时间，单位ms\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.transition.duration\r\n        },\r\n        // 使用的动画过渡函数\r\n        timingFunction: {\r\n            type: String,\r\n            default: () => defProps.transition.timingFunction\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA,EACJ;AACL,CAAC;;"}