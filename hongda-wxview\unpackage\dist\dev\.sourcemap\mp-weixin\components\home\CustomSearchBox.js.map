{"version": 3, "file": "CustomSearchBox.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9DdXN0b21TZWFyY2hCb3gudnVl"], "sourcesContent": ["<template>\n\t<view class=\"custom-search-box\">\n\t\t<view class=\"search-content\">\n\t\t\t<input\n\t\t\t\tv-model=\"searchValue\"\n\t\t\t\t:placeholder=\"placeholder\"\n\t\t\t\t:placeholder-style=\"placeholderStyle\"\n\t\t\t\tclass=\"search-input\"\n\t\t\t\t@input=\"handleInput\"\n\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\tconfirm-type=\"search\"\n\t\t\t/>\n\t\t\t<view class=\"search-icon\" @click.stop=\"handleSearchClick\">\n\t\t\t\t<u-icon name=\"search\" size=\"24\" color=\"#999999\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, watch } from 'vue';\n\n// 定义组件props\nconst props = defineProps({\n\t// 搜索框占位符文本\n\tplaceholder: {\n\t\ttype: String,\n\t\tdefault: '搜索活动'\n\t},\n\t// 双向绑定的搜索值\n\tmodelValue: {\n\t\ttype: String,\n\t\tdefault: ''\n\t}\n});\n\n// 占位符样式（统一与页面设计规格保持一致）\nconst placeholderStyle = 'width: 112rpx; height: 44rpx; font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30; font-weight: normal; font-size: 28rpx; color: #9B9A9A; line-height: 44rpx; text-align: left; font-style: normal; text-transform: none;';\n\n// 定义组件事件\nconst emit = defineEmits(['update:modelValue', 'search', 'input']);\n\n// 内部搜索值\nconst searchValue = ref(props.modelValue);\n\n/**\n * 监听modelValue变化，同步到内部状态\n */\nwatch(() => props.modelValue, (newValue) => {\n\tsearchValue.value = newValue;\n});\n\n/**\n * 监听内部搜索值变化，同步到父组件\n */\nwatch(searchValue, (newValue) => {\n\temit('update:modelValue', newValue);\n});\n\n/**\n * 处理搜索事件\n * @param {Event} event - 事件对象\n */\nconst handleSearch = (event) => {\n\tconst value = event.detail?.value || searchValue.value;\n\temit('search', value);\n};\n\n/**\n * 处理输入事件\n * @param {Event} event - 事件对象\n */\nconst handleInput = (event) => {\n\tconst value = event.detail?.value || event.target?.value;\n\tsearchValue.value = value;\n\temit('input', value);\n};\n\n/**\n * 处理搜索图标点击事件\n * 仅在点击图标或键盘搜索时触发搜索\n */\nconst handleSearchClick = () => {\n\temit('search', searchValue.value);\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.custom-search-box {\n\twidth: 446rpx;\n\theight: 60rpx;\n\tcursor: pointer;\n\n\t.search-content {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t\tborder-radius: 30rpx;\n\t\tbackground: #FFFFFF;\n\t\tbox-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.05);\n\t\tborder: none;\n\t\tbox-sizing: border-box;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:focus-within {\n\t\t\tbox-shadow: 0rpx 0rpx 25rpx 0rpx rgba(2,63,152,0.1);\n\t\t}\n\n\t\t.search-input {\n\t\t\tflex: 1;\n\t\t\theight: 100%;\n\t\t\tborder: none;\n\t\t\toutline: none;\n\t\t\tbackground: transparent;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #333333;\n\t\t\tpadding: 0;\n\t\t\tmargin: 0;\n\n\t\t\t&::placeholder {\n\t\t\t\tcolor: #999999;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\t\t}\n\n\t\t.search-icon {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\twidth: 40rpx;\n\t\t\theight: 40rpx;\n\t\t\tflex-shrink: 0;\n\t\t\tcursor: pointer;\n\t\t\ttransition: all 0.2s ease;\n\t\t\tborder-radius: 50%;\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\t\t}\n\t}\n}\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "watch"], "mappings": ";;;;;;;;;;AAqCA,MAAM,mBAAmB;;;;;;;;;;;;;;;;;AAdzB,UAAM,QAAQ;AAiBd,UAAM,OAAO;AAGb,UAAM,cAAcA,cAAG,IAAC,MAAM,UAAU;AAKxCC,kBAAK,MAAC,MAAM,MAAM,YAAY,CAAC,aAAa;AAC3C,kBAAY,QAAQ;AAAA,IACrB,CAAC;AAKDA,kBAAAA,MAAM,aAAa,CAAC,aAAa;AAChC,WAAK,qBAAqB,QAAQ;AAAA,IACnC,CAAC;AAMD,UAAM,eAAe,CAAC,UAAU;;AAC/B,YAAM,UAAQ,WAAM,WAAN,mBAAc,UAAS,YAAY;AACjD,WAAK,UAAU,KAAK;AAAA,IACrB;AAMA,UAAM,cAAc,CAAC,UAAU;;AAC9B,YAAM,UAAQ,WAAM,WAAN,mBAAc,YAAS,WAAM,WAAN,mBAAc;AACnD,kBAAY,QAAQ;AACpB,WAAK,SAAS,KAAK;AAAA,IACpB;AAMA,UAAM,oBAAoB,MAAM;AAC/B,WAAK,UAAU,YAAY,KAAK;AAAA,IACjC;;;;;;;;;;;;;;;;;;;ACnFA,GAAG,gBAAgB,SAAS;"}