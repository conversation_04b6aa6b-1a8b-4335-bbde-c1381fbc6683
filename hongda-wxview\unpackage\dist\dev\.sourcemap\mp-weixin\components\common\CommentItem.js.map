{"version": 3, "file": "CommentItem.js", "sources": ["components/common/CommentItem.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvY29tbW9uL0NvbW1lbnRJdGVtLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"comment-item\">\r\n    <view class=\"top-comment\">\r\n      <view class=\"comment-avatar\">\r\n        <image :src=\"comment.avatarUrl || '/static/images/default-avatar.png'\" class=\"avatar-img\"></image>\r\n      </view>\r\n      <view class=\"comment-content\">\r\n        <view class=\"comment-header\">\r\n          <text class=\"comment-author\">{{ comment.nickname || '匿名用户' }}</text>\r\n          <text class=\"comment-time\">{{ formatDateTime(comment.createTime) }}</text>\r\n        </view>\r\n        <text class=\"comment-text\">{{ comment.content }}</text>\r\n          <view class=\"comment-actions\">\r\n            <view class=\"action-item\" @click.stop=\"handleReplyClick(comment)\">\r\n            <text class=\"iconfont icon-message-circle\"></text>\r\n            <text>回复</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"replies-container\" v-if=\"flatReplies.length > 0\">\r\n      <view class=\"reply-list\">\r\n        <view\r\n            v-for=\"reply in displayedReplies\"\r\n            :key=\"reply.id\"\r\n            class=\"reply-item\"\r\n        >\r\n          <view class=\"reply-avatar\">\r\n            <image :src=\"reply.avatarUrl || '/static/images/default-avatar.png'\" class=\"reply-avatar-img\"></image>\r\n          </view>\r\n\r\n          <view class=\"reply-content\">\r\n            <view class=\"reply-header\">\r\n              <text class=\"reply-author\">{{ reply.nickname || '匿名用户' }}</text>\r\n              <view class=\"reply-to\" v-if=\"reply.replyToNickname\">\r\n                <text class=\"reply-text\">回复</text>\r\n                <text class=\"reply-target\">@{{ reply.replyToNickname }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <text class=\"reply-text-content\">{{ reply.content }}</text>\r\n\r\n            <view class=\"reply-footer\">\r\n              <text class=\"reply-time\">{{ formatDateTime(reply.createTime) }}</text>\r\n              <view class=\"reply-actions\">\r\n                <view class=\"action-item\" @click.stop=\"handleReplyClick(reply)\">\r\n                  <text class=\"iconfont icon-message-circle\"></text>\r\n                  <text>回复</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"expand-toggle\" v-if=\"flatReplies.length > maxDisplayReplies\" @click=\"toggleExpand\">\r\n        <text class=\"expand-text\">\r\n          {{ isExpanded ? `收起回复` : `共${flatReplies.length}条回复 >` }}\r\n        </text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n// 【已移除】不再需要 formatRelativeTime\r\n// import { formatRelativeTime } from '@/utils/date.js';\r\n\r\nconst props = defineProps({\r\n  comment: {\r\n    type: Object,\r\n    required: true\r\n  },\r\n  maxDisplayReplies: {\r\n    type: Number,\r\n    default: 3\r\n  }\r\n});\r\n\r\nconst emit = defineEmits(['reply']);\r\n\r\n/**\r\n * 【新增】日期时间格式化函数\r\n * @param {string | Date} time - 需要格式化的时间\r\n * @returns {string} 格式化后的字符串，例如 \"2025-07-21 10:32:21\"\r\n */\r\nconst formatDateTime = (time) => {\r\n  if (!time) return '';\r\n  const date = new Date(time);\r\n\r\n  const Y = date.getFullYear();\r\n  const M = (date.getMonth() + 1).toString().padStart(2, '0');\r\n  const D = date.getDate().toString().padStart(2, '0');\r\n\r\n  const h = date.getHours().toString().padStart(2, '0');\r\n  const m = date.getMinutes().toString().padStart(2, '0');\r\n  const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n  return `${Y}-${M}-${D} ${h}:${m}:${s}`;\r\n};\r\n\r\n\r\n// 展开状态\r\nconst isExpanded = ref(false);\r\n\r\n// 将树形回复数据扁平化的核心函数\r\nconst flattenReplies = (children) => {\r\n  const result = [];\r\n  const traverse = (nodes) => {\r\n    if (!nodes || !Array.isArray(nodes)) return;\r\n    nodes.forEach(node => {\r\n      result.push({\r\n        ...node,\r\n        replyToNickname: node.replyToNickname || null\r\n      });\r\n      if (node.children && node.children.length > 0) {\r\n        traverse(node.children);\r\n      }\r\n    });\r\n  };\r\n  traverse(children);\r\n  return result;\r\n};\r\n\r\n// 扁平化的回复列表\r\nconst flatReplies = computed(() => {\r\n  if (!props.comment.children || !Array.isArray(props.comment.children)) {\r\n    return [];\r\n  }\r\n  return flattenReplies(props.comment.children);\r\n});\r\n\r\n// 根据展开状态决定显示的回复列表\r\nconst displayedReplies = computed(() => {\r\n  if (isExpanded.value || flatReplies.value.length <= props.maxDisplayReplies) {\r\n    return flatReplies.value;\r\n  }\r\n  return flatReplies.value.slice(0, props.maxDisplayReplies);\r\n});\r\n\r\n// 切换展开状态\r\nconst toggleExpand = () => {\r\n  isExpanded.value = !isExpanded.value;\r\n};\r\n\r\n// 跳转登录前记录当前页面，供登录后返回\r\nconst getCurrentPageUrl = () => {\r\n  try {\r\n    const pages = getCurrentPages();\r\n    const current = pages[pages.length - 1];\r\n    const route = '/' + current.route;\r\n    const options = current.options || {};\r\n    const query = Object.keys(options)\r\n      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)\r\n      .join('&');\r\n    return query ? `${route}?${query}` : route;\r\n  } catch (e) {\r\n    return '';\r\n  }\r\n};\r\n\r\n// 统一的登录校验\r\nconst ensureLoggedIn = () => {\r\n  try {\r\n    const token = uni.getStorageSync('token');\r\n    if (!token) {\r\n      const backUrl = getCurrentPageUrl();\r\n      try { if (backUrl) uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}\r\n      uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n      return false;\r\n    }\r\n    return true;\r\n  } catch (e) {\r\n    uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n    return false;\r\n  }\r\n};\r\n\r\n// 点击回复时先校验登录\r\nconst handleReplyClick = (target) => {\r\n  if (!ensureLoggedIn()) return;\r\n  emit('reply', target);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 样式部分无需修改，但为了完整性一并提供 --- */\r\n.comment-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 顶级评论样式 */\r\n.top-comment {\r\n  display: flex;\r\n  padding: 16px;\r\n  gap: 12px;\r\n}\r\n\r\n.comment-avatar {\r\n  flex-shrink: 0;\r\n\r\n  .avatar-img {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.comment-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.comment-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 8px;\r\n\r\n  .comment-author {\r\n    font-size: 15px;\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .comment-time {\r\n    font-size: 13px;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.comment-text {\r\n  display: block;\r\n  font-size: 16px;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  margin-bottom: 12px;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.comment-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 24px;\r\n\r\n  .action-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    font-size: 14px;\r\n    color: #999;\r\n    cursor: pointer;\r\n\r\n    .iconfont {\r\n      font-size: 16px;\r\n    }\r\n\r\n    &:active {\r\n      opacity: 0.7;\r\n    }\r\n  }\r\n}\r\n\r\n/* 回复容器样式 */\r\n.replies-container {\r\n  margin: 0 10px 0 52px; /* 左侧对齐头像右边缘 */\r\n  background: #f7f8fa;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.reply-list {\r\n  padding: 4px 0;\r\n}\r\n\r\n.reply-item {\r\n  display: flex;\r\n  padding: 12px 16px;\r\n  gap: 10px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.reply-avatar {\r\n  flex-shrink: 0;\r\n\r\n  .reply-avatar-img {\r\n    width: 32px;\r\n    height: 32px;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.reply-content {\r\n  width: 100%;\r\n}\r\n\r\n.reply-header {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  margin-bottom: 8px;\r\n\r\n  .reply-author {\r\n    font-size: 14px;\r\n    color: #576b95;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .reply-to {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    background: #e8f4fd;\r\n    padding: 2px 8px;\r\n    border-radius: 12px;\r\n\r\n    .reply-text {\r\n      font-size: 12px;\r\n      color: #576b95;\r\n    }\r\n\r\n    .reply-target {\r\n      font-size: 12px;\r\n      color: #007bff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.reply-text-content {\r\n  display: block;\r\n  font-size: 15px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n  margin-bottom: 8px;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.reply-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n\r\n  .reply-time {\r\n    font-size: 12px;\r\n    color: #999;\r\n  }\r\n\r\n  .reply-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n\r\n    .action-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 3px;\r\n      font-size: 13px;\r\n      color: #999;\r\n      cursor: pointer;\r\n\r\n      .iconfont {\r\n        font-size: 14px;\r\n      }\r\n\r\n      &:active {\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 展开/收起按钮 */\r\n.expand-toggle {\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  border-top: 1px solid #eee;\r\n  background: #fafbfc;\r\n  cursor: pointer;\r\n\r\n  .expand-text {\r\n    font-size: 14px;\r\n    color: #576b95;\r\n  }\r\n\r\n  &:active {\r\n    background: #f0f1f3;\r\n  }\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/common/CommentItem.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;;;;;;AAsEA,UAAM,QAAQ;AAWd,UAAM,OAAO;AAOb,UAAM,iBAAiB,CAAC,SAAS;AAC/B,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,OAAO,IAAI,KAAK,IAAI;AAE1B,YAAM,IAAI,KAAK;AACf,YAAM,KAAK,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC1D,YAAM,IAAI,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAEnD,YAAM,IAAI,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACpD,YAAM,IAAI,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACtD,YAAM,IAAI,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAEtD,aAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IACtC;AAIA,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,iBAAiB,CAAC,aAAa;AACnC,YAAM,SAAS,CAAA;AACf,YAAM,WAAW,CAAC,UAAU;AAC1B,YAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,KAAK;AAAG;AACrC,cAAM,QAAQ,UAAQ;AACpB,iBAAO,KAAK;AAAA,YACV,GAAG;AAAA,YACH,iBAAiB,KAAK,mBAAmB;AAAA,UACjD,CAAO;AACD,cAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAC7C,qBAAS,KAAK,QAAQ;AAAA,UACvB;AAAA,QACP,CAAK;AAAA,MACL;AACE,eAAS,QAAQ;AACjB,aAAO;AAAA,IACT;AAGA,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,UAAI,CAAC,MAAM,QAAQ,YAAY,CAAC,MAAM,QAAQ,MAAM,QAAQ,QAAQ,GAAG;AACrE,eAAO;MACR;AACD,aAAO,eAAe,MAAM,QAAQ,QAAQ;AAAA,IAC9C,CAAC;AAGD,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,UAAI,WAAW,SAAS,YAAY,MAAM,UAAU,MAAM,mBAAmB;AAC3E,eAAO,YAAY;AAAA,MACpB;AACD,aAAO,YAAY,MAAM,MAAM,GAAG,MAAM,iBAAiB;AAAA,IAC3D,CAAC;AAGD,UAAM,eAAe,MAAM;AACzB,iBAAW,QAAQ,CAAC,WAAW;AAAA,IACjC;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI;AACF,cAAM,QAAQ;AACd,cAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AACtC,cAAM,QAAQ,MAAM,QAAQ;AAC5B,cAAM,UAAU,QAAQ,WAAW;AACnC,cAAM,QAAQ,OAAO,KAAK,OAAO,EAC9B,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAC3E,KAAK,GAAG;AACX,eAAO,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK;AAAA,MACtC,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI;AACF,cAAM,QAAQC,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACV,gBAAM,UAAU;AAChB,cAAI;AAAE,gBAAI;AAASA,4BAAAA,MAAI,eAAe,iBAAiB,OAAO;AAAA,UAAE,SAAU,GAAG;AAAA,UAAE;AAC/EA,wBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AACtD,iBAAO;AAAA,QACR;AACD,eAAO;AAAA,MACR,SAAQ,GAAG;AACVA,sBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AACtD,eAAO;AAAA,MACR;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,UAAI,CAAC,eAAc;AAAI;AACvB,WAAK,SAAS,MAAM;AAAA,IACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvLA,GAAG,gBAAgB,SAAS;"}