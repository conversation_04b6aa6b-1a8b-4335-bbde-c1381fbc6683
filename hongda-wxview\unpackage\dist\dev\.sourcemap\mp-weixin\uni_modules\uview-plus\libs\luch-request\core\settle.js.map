{"version": 3, "file": "settle.js", "sources": ["uni_modules/uview-plus/libs/luch-request/core/settle.js"], "sourcesContent": ["/**\r\n * Resolve or reject a Promise based on response status.\r\n *\r\n * @param {Function} resolve A function that resolves the promise.\r\n * @param {Function} reject A function that rejects the promise.\r\n * @param {object} response The response.\r\n */\r\nexport default function settle(resolve, reject, response) {\r\n    const { validateStatus } = response.config\r\n    const status = response.statusCode\r\n    if (status && (!validateStatus || validateStatus(status))) {\r\n        resolve(response)\r\n    } else {\r\n        reject(response)\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AAOe,SAAS,OAAO,SAAS,QAAQ,UAAU;AACtD,QAAM,EAAE,mBAAmB,SAAS;AACpC,QAAM,SAAS,SAAS;AACxB,MAAI,WAAW,CAAC,kBAAkB,eAAe,MAAM,IAAI;AACvD,YAAQ,QAAQ;AAAA,EACxB,OAAW;AACH,WAAO,QAAQ;AAAA,EAClB;AACL;;"}