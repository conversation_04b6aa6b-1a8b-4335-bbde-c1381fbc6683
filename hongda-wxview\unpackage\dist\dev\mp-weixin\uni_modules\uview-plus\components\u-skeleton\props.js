"use strict";
const uni_modules_uviewPlus_libs_vue = require("../../libs/vue.js");
const uni_modules_uviewPlus_libs_config_props = require("../../libs/config/props.js");
const props = uni_modules_uviewPlus_libs_vue.defineMixin({
  props: {
    // 是否展示骨架组件
    loading: {
      type: <PERSON><PERSON>an,
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.loading
    },
    // 是否开启动画效果
    animate: {
      type: <PERSON><PERSON><PERSON>,
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.animate
    },
    // 段落占位图行数
    rows: {
      type: [String, Number],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.rows
    },
    // 段落占位图的宽度
    rowsWidth: {
      type: [String, Number, Array],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.rowsWidth
    },
    // 段落占位图的高度
    rowsHeight: {
      type: [String, Number, Array],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.rowsHeight
    },
    // 是否展示标题占位图
    title: {
      type: Boolean,
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.title
    },
    // 段落标题的宽度
    titleWidth: {
      type: [String, Number],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.titleWidth
    },
    // 段落标题的高度
    titleHeight: {
      type: [String, Number],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.titleHeight
    },
    // 是否展示头像占位图
    avatar: {
      type: Boolean,
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.avatar
    },
    // 头像占位图大小
    avatarSize: {
      type: [String, Number],
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.avatarSize
    },
    // 头像占位图的形状，circle-圆形，square-方形
    avatarShape: {
      type: String,
      default: () => uni_modules_uviewPlus_libs_config_props.props.skeleton.avatarShape
    }
  }
});
exports.props = props;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-skeleton/props.js.map
