{"version": 3, "file": "u-swiper-indicator.js", "sources": ["uni_modules/uview-plus/components/u-swiper-indicator/u-swiper-indicator.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN3aXBlci1pbmRpY2F0b3IvdS1zd2lwZXItaW5kaWNhdG9yLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"u-swiper-indicator\">\r\n\t\t<view\r\n\t\t\tclass=\"u-swiper-indicator__wrapper\"\r\n\t\t\tv-if=\"indicatorMode === 'line'\"\r\n\t\t\t:class=\"[`u-swiper-indicator__wrapper--${indicatorMode}`]\"\r\n\t\t\t:style=\"{\r\n\t\t\t\twidth: addUnit(lineWidth * length),\r\n\t\t\t\tbackgroundColor: indicatorInactiveColor\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-swiper-indicator__wrapper--line__bar\"\r\n\t\t\t\t:style=\"[lineStyle]\"\r\n\t\t\t></view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-swiper-indicator__wrapper\"\r\n\t\t\tv-if=\"indicatorMode === 'dot'\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-swiper-indicator__wrapper__dot\"\r\n\t\t\t\tv-for=\"(item, index) in length\"\r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t:class=\"[index === current && 'u-swiper-indicator__wrapper__dot--active']\"\r\n\t\t\t\t:style=\"[dotStyle(index)]\"\r\n\t\t\t>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit } from '../../libs/function/index';\r\n\t/**\r\n\t * SwiperIndicator 轮播图指示器\r\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/swiper.html\r\n\t * @property {String | Number}\tlength\t\t\t\t\t轮播的长度（默认 0 ）\r\n\t * @property {String | Number}\tcurrent\t\t\t\t\t当前处于活动状态的轮播的索引（默认 0 ）\r\n\t * @property {String}\t\t\tindicatorActiveColor\t指示器非激活颜色\r\n\t * @property {String}\t\t\tindicatorInactiveColor\t指示器的激活颜色\r\n\t * @property {String}\t\t\tindicatorMode\t\t\t指示器模式（默认 'line' ）\r\n\t * @example\t<u-swiper :list=\"list4\" indicator keyName=\"url\" :autoplay=\"false\"></u-swiper>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-swiper-indicator',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlineWidth: 22\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 指示器为线型的样式\r\n\t\t\tlineStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.width = addUnit(this.lineWidth)\r\n\t\t\t\tstyle.transform = `translateX(${ addUnit(this.current * this.lineWidth) })`\r\n\t\t\t\tstyle.backgroundColor = this.indicatorActiveColor\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 指示器为点型的样式\r\n\t\t\tdotStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tlet style = {}\r\n\t\t\t\t\tstyle.backgroundColor = index === this.current ? this.indicatorActiveColor : this.indicatorInactiveColor\r\n\t\t\t\t\treturn style\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddUnit\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-swiper-indicator {\r\n\r\n\t\t&__wrapper {\r\n\t\t\t@include flex;\r\n\r\n\t\t\t&--line {\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t\theight: 4px;\r\n\r\n\t\t\t\t&__bar {\r\n\t\t\t\t\twidth: 22px;\r\n\t\t\t\t\theight: 4px;\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t\t\ttransition: transform 0.3s;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__dot {\r\n\t\t\t\twidth: 5px;\r\n\t\t\t\theight: 5px;\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t\tmargin: 0 4px;\r\n\r\n\t\t\t\t&--active {\r\n\t\t\t\t\twidth: 12px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-swiper-indicator/u-swiper-indicator.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit"], "mappings": ";;;;;;AAgDC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,6DAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,YAAY;AACX,UAAI,QAAQ,CAAC;AACb,YAAM,QAAQC,kDAAQ,KAAK,SAAS;AACpC,YAAM,YAAY,cAAeA,0CAAO,QAAC,KAAK,UAAU,KAAK,SAAS,CAAA;AACtE,YAAM,kBAAkB,KAAK;AAC7B,aAAO;AAAA,IACP;AAAA;AAAA,IAED,WAAW;AACV,aAAO,WAAS;AACf,YAAI,QAAQ,CAAC;AACb,cAAM,kBAAkB,UAAU,KAAK,UAAU,KAAK,uBAAuB,KAAK;AAClF,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,SAAAA,0CAAM;AAAA,EACP;AACD;;;;;;;;;;;;;;;;;;;;;;AC5ED,GAAG,gBAAgB,SAAS;"}