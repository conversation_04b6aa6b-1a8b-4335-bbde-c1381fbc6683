{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-button/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否细边框\r\n        hairline: {\r\n            type: <PERSON><PERSON>an,\r\n            default: () => defProps.button.hairline\r\n        },\r\n        // 按钮的预置样式，info，primary，error，warning，success\r\n        type: {\r\n            type: String,\r\n            default: () => defProps.button.type\r\n        },\r\n        // 按钮尺寸，large，normal，small，mini\r\n        size: {\r\n            type: String,\r\n            default: () => defProps.button.size\r\n        },\r\n        // 按钮形状，circle（两边为半圆），square（带圆角）\r\n        shape: {\r\n            type: String,\r\n            default: () => defProps.button.shape\r\n        },\r\n        // 按钮是否镂空\r\n        plain: {\r\n            type: <PERSON>olean,\r\n            default: () => defProps.button.plain\r\n        },\r\n        // 是否禁止状态\r\n        disabled: {\r\n            type: Boolean,\r\n            default: () => defProps.button.disabled\r\n        },\r\n        // 是否加载中\r\n        loading: {\r\n            type: Boolean,\r\n            default: () => defProps.button.loading\r\n        },\r\n        // 加载中提示文字\r\n        loadingText: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.loadingText\r\n        },\r\n        // 加载状态图标类型\r\n        loadingMode: {\r\n            type: String,\r\n            default: () => defProps.button.loadingMode\r\n        },\r\n        // 加载图标大小\r\n        loadingSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.loadingSize\r\n        },\r\n        // 开放能力，具体请看uniapp稳定关于button组件部分说明\r\n        // https://uniapp.dcloud.io/component/button\r\n        openType: {\r\n            type: String,\r\n            default: () => defProps.button.openType\r\n        },\r\n        // 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n        // 取值为submit（提交表单），reset（重置表单）\r\n        formType: {\r\n            type: String,\r\n            default: () => defProps.button.formType\r\n        },\r\n        // 打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效\r\n        // 只微信小程序、QQ小程序有效\r\n        appParameter: {\r\n            type: String,\r\n            default: () => defProps.button.appParameter\r\n        },\r\n        // 指定是否阻止本节点的祖先节点出现点击态，微信小程序有效\r\n        hoverStopPropagation: {\r\n            type: Boolean,\r\n            default: () => defProps.button.hoverStopPropagation\r\n        },\r\n        // 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文。只微信小程序有效\r\n        lang: {\r\n            type: String,\r\n            default: () => defProps.button.lang\r\n        },\r\n        // 会话来源，open-type=\"contact\"时有效。只微信小程序有效\r\n        sessionFrom: {\r\n            type: String,\r\n            default: () => defProps.button.sessionFrom\r\n        },\r\n        // 会话内消息卡片标题，open-type=\"contact\"时有效\r\n        // 默认当前标题，只微信小程序有效\r\n        sendMessageTitle: {\r\n            type: String,\r\n            default: () => defProps.button.sendMessageTitle\r\n        },\r\n        // 会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效\r\n        // 默认当前分享路径，只微信小程序有效\r\n        sendMessagePath: {\r\n            type: String,\r\n            default: () => defProps.button.sendMessagePath\r\n        },\r\n        // 会话内消息卡片图片，open-type=\"contact\"时有效\r\n        // 默认当前页面截图，只微信小程序有效\r\n        sendMessageImg: {\r\n            type: String,\r\n            default: () => defProps.button.sendMessageImg\r\n        },\r\n        // 是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，\r\n        // 用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效\r\n        showMessageCard: {\r\n            type: Boolean,\r\n            default: () => defProps.button.showMessageCard\r\n        },\r\n        // 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n        dataName: {\r\n            type: String,\r\n            default: () => defProps.button.dataName\r\n        },\r\n        // 节流，一定时间内只能触发一次\r\n        throttleTime: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.throttleTime\r\n        },\r\n        // 按住后多久出现点击态，单位毫秒\r\n        hoverStartTime: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.hoverStartTime\r\n        },\r\n        // 手指松开后点击态保留时间，单位毫秒\r\n        hoverStayTime: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.hoverStayTime\r\n        },\r\n        // 按钮文字，之所以通过props传入，是因为slot传入的话\r\n        // nvue中无法控制文字的样式\r\n        text: {\r\n            type: [String, Number],\r\n            default: () => defProps.button.text\r\n        },\r\n        // 按钮图标\r\n        icon: {\r\n            type: String,\r\n            default: () => defProps.button.icon\r\n        },\r\n        // 按钮图标\r\n        iconColor: {\r\n            type: String,\r\n            default: () => defProps.button.icon\r\n        },\r\n        // 按钮颜色，支持传入linear-gradient渐变色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.button.color\r\n        },\r\n        // 停止冒泡\r\n        stop: {\r\n            type: Boolean,\r\n            default: () => defProps.button.stop\r\n        },\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}