<view class="{{['uni-easyinput', ac && 'uni-easyinput-error']}}" style="{{ad}}"><view class="{{['uni-easyinput__content', aa]}}" style="{{ab}}"><uni-icons wx:if="{{a}}" class="content-clear-icon" bindclick="{{b}}" u-i="3878d0b3-0" bind:__l="__l" u-p="{{c}}"></uni-icons><slot name="left"></slot><textarea wx:if="{{d}}" class="{{['uni-easyinput__content-textarea', e && 'input-padding']}}" name="{{f}}" value="{{g}}" placeholder="{{h}}" placeholderStyle="{{i}}" disabled="{{j}}" placeholder-class="uni-easyinput__placeholder-class" maxlength="{{k}}" focus="{{l}}" autoHeight="{{m}}" cursor-spacing="{{n}}" adjust-position="{{o}}" bindinput="{{p}}" bindblur="{{q}}" bindfocus="{{r}}" bindconfirm="{{s}}" bindkeyboardheightchange="{{t}}"></textarea><block wx:else><input wx:if="{{r0}}" type="{{v}}" class="uni-easyinput__content-input" style="{{w}}" name="{{x}}" value="{{y}}" password="{{z}}" placeholder="{{A}}" placeholderStyle="{{B}}" placeholder-class="uni-easyinput__placeholder-class" disabled="{{C}}" maxlength="{{D}}" focus="{{E}}" confirmType="{{F}}" cursor-spacing="{{G}}" adjust-position="{{H}}" bindfocus="{{I}}" bindblur="{{J}}" bindinput="{{K}}" bindconfirm="{{L}}" bindkeyboardheightchange="{{M}}"/></block><block wx:if="{{N}}"><uni-icons wx:if="{{O}}" class="{{['content-clear-icon', P && 'is-textarea-icon']}}" bindclick="{{Q}}" u-i="3878d0b3-1" bind:__l="__l" u-p="{{R}}"></uni-icons></block><block wx:if="{{S}}"><uni-icons wx:if="{{T}}" class="content-clear-icon" bindclick="{{U}}" u-i="3878d0b3-2" bind:__l="__l" u-p="{{V}}"></uni-icons></block><block wx:else><uni-icons wx:if="{{W}}" class="{{['content-clear-icon', X && 'is-textarea-icon']}}" bindclick="{{Y}}" u-i="3878d0b3-3" bind:__l="__l" u-p="{{Z}}"></uni-icons></block><slot name="right"></slot></view></view>