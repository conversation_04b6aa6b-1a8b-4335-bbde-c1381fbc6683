{"version": 3, "file": "date.js", "sources": ["utils/date.js"], "sourcesContent": ["/**\r\n * 日期格式化工具函数\r\n */\r\n\r\n/**\r\n * 统一解析日期，兼容 iOS 不支持的格式\r\n * @param {string|Date} input - 日期字符串或Date对象\r\n * @returns {Date} 解析后的 Date 对象\r\n */\r\nexport function parseDate(input) {\r\n    if (!input) return new Date('Invalid Date');\r\n    if (typeof input === 'string') {\r\n        const s = String(input).trim();\r\n        // 1) 已是 ISO 带 T 或带时区，保持不变（iOS 支持）\r\n        if (s.includes('T')) {\r\n            return new Date(s);\r\n        }\r\n        // 2) 含有空格的 \"yyyy-MM-dd HH:mm:ss\" → 改为 \"yyyy/MM/dd HH:mm:ss\"（iOS 支持）\r\n        if (s.includes(' ')) {\r\n            return new Date(s.replace(/-/g, '/'));\r\n        }\r\n        // 3) 仅日期的 \"yyyy-MM-dd\" → iOS 原生支持，可直接使用\r\n        return new Date(s);\r\n    }\r\n    return new Date(input);\r\n}\r\n\r\n/**\r\n * 格式化日期\r\n * @param {string|Date} date - 日期字符串或Date对象\r\n * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD'\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatDate(date, format = 'YYYY-MM-DD') {\r\n\tconst d = parseDate(date);\r\n\tif (isNaN(d.getTime())) return '';\r\n\r\n\tconst year = d.getFullYear();\r\n\tconst month = String(d.getMonth() + 1).padStart(2, '0');\r\n\tconst day = String(d.getDate()).padStart(2, '0');\r\n\tconst hours = String(d.getHours()).padStart(2, '0');\r\n\tconst minutes = String(d.getMinutes()).padStart(2, '0');\r\n\tconst seconds = String(d.getSeconds()).padStart(2, '0');\r\n\r\n\treturn format\r\n\t\t.replace(/yyyy/i, year)\r\n\t\t.replace('MM', month)\r\n\t\t.replace(/dd/i, day)\r\n\t\t.replace(/hh/i, hours)\r\n\t\t.replace('mm', minutes)\r\n\t\t.replace(/ss/i, seconds);\r\n}\r\n\r\n/**\r\n * 格式化日期为 \"YYYY-MM-DD 周X\" 格式（活动列表专用）\r\n * @param {string|Date} isoString - ISO日期字符串或Date对象\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatEventDate(isoString) {\r\n\tconst date = parseDate(isoString);\r\n\tif (isNaN(date.getTime())) return '';\r\n\r\n\tconst year = date.getFullYear();\r\n\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\tconst day = String(date.getDate()).padStart(2, '0');\r\n\tconst week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()];\r\n\treturn `${year}-${month}-${day} ${week}`;\r\n}\r\n\r\n/**\r\n * 格式化日期时间为 \"MM-DD HH:mm\" 格式\r\n * @param {string|Date} isoString - ISO日期字符串或Date对象\r\n * @returns {string} 格式化后的日期时间字符串\r\n */\r\nexport function formatEventDateTime(isoString) {\r\n\tconst date = parseDate(isoString);\r\n\tif (isNaN(date.getTime())) return '';\r\n\r\n\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\tconst day = String(date.getDate()).padStart(2, '0');\r\n\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\treturn `${month}-${day} ${hours}:${minutes}`;\r\n}\r\n\r\n/**\r\n * 格式化为相对时间（如：刚刚、1分钟前、1小时前等）\r\n * @param {string|Date} date - 日期字符串或Date对象\r\n * @returns {string} 相对时间字符串\r\n */\r\nexport function formatRelativeTime(date) {\r\n\tconst d = parseDate(date);\r\n\tif (isNaN(d.getTime())) return '';\r\n\r\n\tconst now = new Date();\r\n\tconst diff = now.getTime() - d.getTime();\r\n\r\n\tconst minute = 60 * 1000;\r\n\tconst hour = 60 * minute;\r\n\tconst day = 24 * hour;\r\n\tconst month = 30 * day;\r\n\tconst year = 365 * day;\r\n\r\n\tif (diff < minute) {\r\n\t\treturn '刚刚';\r\n\t} else if (diff < hour) {\r\n\t\treturn Math.floor(diff / minute) + '分钟前';\r\n\t} else if (diff < day) {\r\n\t\treturn Math.floor(diff / hour) + '小时前';\r\n\t} else if (diff < month) {\r\n\t\treturn Math.floor(diff / day) + '天前';\r\n\t} else if (diff < year) {\r\n\t\treturn Math.floor(diff / month) + '个月前';\r\n\t} else {\r\n\t\treturn Math.floor(diff / year) + '年前';\r\n\t}\r\n}\r\n\r\n/**\r\n * 判断是否为今天\r\n * @param {string|Date} date - 日期字符串或Date对象\r\n * @returns {boolean} 是否为今天\r\n */\r\nexport function isToday(date) {\r\n\tconst d = parseDate(date);\r\n\tif (isNaN(d.getTime())) return false;\r\n\r\n\tconst today = new Date();\r\n\treturn d.toDateString() === today.toDateString();\r\n}\r\n\r\n/**\r\n * 获取友好的日期显示\r\n * @param {string|Date} date - 日期字符串或Date对象\r\n * @returns {string} 友好的日期字符串\r\n */\r\nexport function getFriendlyDate(date) {\r\n\tconst d = parseDate(date);\r\n\tif (isNaN(d.getTime())) return '';\r\n\r\n\tif (isToday(d)) {\r\n\t\treturn '今天 ' + formatDate(d, 'HH:mm');\r\n\t}\r\n\r\n\tconst yesterday = new Date();\r\n\tyesterday.setDate(yesterday.getDate() - 1);\r\n\tif (d.toDateString() === yesterday.toDateString()) {\r\n\t\treturn '昨天 ' + formatDate(d, 'HH:mm');\r\n\t}\r\n\r\n\tconst thisYear = new Date().getFullYear();\r\n\tif (d.getFullYear() === thisYear) {\r\n\t\treturn formatDate(d, 'MM-DD HH:mm');\r\n\t}\r\n\r\n\treturn formatDate(d, 'YYYY-MM-DD');\r\n}"], "names": [], "mappings": ";AASO,SAAS,UAAU,OAAO;AAC7B,MAAI,CAAC;AAAO,WAAO,oBAAI,KAAK,cAAc;AAC1C,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,IAAI,OAAO,KAAK,EAAE,KAAI;AAE5B,QAAI,EAAE,SAAS,GAAG,GAAG;AACjB,aAAO,IAAI,KAAK,CAAC;AAAA,IACpB;AAED,QAAI,EAAE,SAAS,GAAG,GAAG;AACjB,aAAO,IAAI,KAAK,EAAE,QAAQ,MAAM,GAAG,CAAC;AAAA,IACvC;AAED,WAAO,IAAI,KAAK,CAAC;AAAA,EACpB;AACD,SAAO,IAAI,KAAK,KAAK;AACzB;AAQO,SAAS,WAAW,MAAM,SAAS,cAAc;AACvD,QAAM,IAAI,UAAU,IAAI;AACxB,MAAI,MAAM,EAAE,QAAO,CAAE;AAAG,WAAO;AAE/B,QAAM,OAAO,EAAE;AACf,QAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,QAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAC/C,QAAM,QAAQ,OAAO,EAAE,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,QAAM,UAAU,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACtD,QAAM,UAAU,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEtD,SAAO,OACL,QAAQ,SAAS,IAAI,EACrB,QAAQ,MAAM,KAAK,EACnB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,KAAK,EACpB,QAAQ,MAAM,OAAO,EACrB,QAAQ,OAAO,OAAO;AACzB;AAOO,SAAS,gBAAgB,WAAW;AAC1C,QAAM,OAAO,UAAU,SAAS;AAChC,MAAI,MAAM,KAAK,QAAO,CAAE;AAAG,WAAO;AAElC,QAAM,OAAO,KAAK;AAClB,QAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,QAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,QAAM,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,EAAE,KAAK,OAAQ,CAAA;AACrE,SAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AACvC;;;;"}