{"version": 3, "file": "ad.js", "sources": ["api/platform/ad.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * 根据位置代码获取广告列表\r\n * @param {string} positionCode - 广告位置代码 (例如: 'HOME_BANNER')\r\n * @param {object} params - 其他查询参数 (可选)\r\n */\r\nexport function getAdListByPositionApi(positionCode, params = {}) {\r\n\t// 合并参数，确保正确传递给后端\r\n\tconst queryParams = {\r\n\t\tpositionCode,\r\n\t\tstatus: 1, // 只获取启用状态的广告\r\n\t\tpageSize: 10, // 默认获取10条\r\n\t\t...params\r\n\t};\r\n\t\r\n\tconsole.log('广告API请求参数:', queryParams);\r\n\treturn http.get('/ad/list', queryParams);\r\n} "], "names": ["uni", "http"], "mappings": ";;;AAOO,SAAS,uBAAuB,cAAc,SAAS,IAAI;AAEjE,QAAM,cAAc;AAAA,IACnB;AAAA,IACA,QAAQ;AAAA;AAAA,IACR,UAAU;AAAA;AAAA,IACV,GAAG;AAAA,EACL;AAECA,gBAAY,MAAA,MAAA,OAAA,4BAAA,cAAc,WAAW;AACrC,SAAOC,mBAAK,IAAI,YAAY,WAAW;AACxC;;"}