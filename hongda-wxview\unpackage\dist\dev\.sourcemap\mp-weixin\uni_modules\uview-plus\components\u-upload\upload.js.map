{"version": 3, "file": "upload.js", "sources": ["uni_modules/uview-plus/components/u-upload/upload.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:09:50\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/upload.js\r\n */\r\nexport default {\r\n\t// upload组件\r\n\tupload: {\r\n\t\taccept: 'image',\r\n\t\textension: [],\r\n\t\tcapture: ['album', 'camera'],\r\n\t\tcompressed: true,\r\n\t\tcamera: 'back',\r\n\t\tmaxDuration: 60,\r\n\t\tuploadIcon: 'camera-fill',\r\n\t\tuploadIconColor: '#D3D4D6',\r\n\t\tuseBeforeRead: false,\r\n\t\tpreviewFullImage: true,\r\n\t\tmaxCount: 52,\r\n\t\tdisabled: false,\r\n\t\timageMode: 'aspectFill',\r\n\t\tname: '',\r\n\t\tsizeType: ['original', 'compressed'],\r\n\t\tmultiple: false,\r\n\t\tdeletable: true,\r\n\t\tmaxSize: Number.MAX_VALUE,\r\n\t\tfileList: [],\r\n\t\tuploadText: '',\r\n\t\twidth: 80,\r\n\t\theight: 80,\r\n\t\tpreviewImage: true,\r\n\t\tautoDelete: false,\r\n\t\tautoUpload: false,\r\n\t\tautoUploadApi: '',\r\n\t\tautoUploadAuthUrl: '',\r\n\t\tautoUploadDriver: '',\r\n\t\tautoUploadHeader: {},\r\n\t\tgetVideoThumb: false,\r\n\t\tcustomAfterAutoUpload: false,\r\n\t\tvideoPreviewObjectFit: 'cover'\r\n\t}\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEd,QAAQ;AAAA,IACP,QAAQ;AAAA,IACR,WAAW,CAAE;AAAA,IACb,SAAS,CAAC,SAAS,QAAQ;AAAA,IAC3B,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU,CAAC,YAAY,YAAY;AAAA,IACnC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS,OAAO;AAAA,IAChB,UAAU,CAAE;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,kBAAkB,CAAE;AAAA,IACpB,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACvB;AACF;;"}