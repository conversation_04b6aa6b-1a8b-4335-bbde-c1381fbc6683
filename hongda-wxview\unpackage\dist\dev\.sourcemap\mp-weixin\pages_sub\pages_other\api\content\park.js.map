{"version": 3, "file": "park.js", "sources": ["pages_sub/pages_other/api/content/park.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * 根据ID获取园区详情\r\n * @param {number | string} id - 园区ID\r\n */\r\nexport function getParkDetail(id) {\r\n  const url = `/park/${id}`;\r\n  return http.get(url);\r\n}\r\n\r\n\r\n"], "names": ["http"], "mappings": ";;AAMO,SAAS,cAAc,IAAI;AAChC,QAAM,MAAM,SAAS,EAAE;AACvB,SAAOA,cAAI,KAAC,IAAI,GAAG;AACrB;;"}