{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n// 1. 导入API方法\r\nimport { getAllAssets } from '@/api/platform/asset.js';\r\n\r\nexport default {\r\n  onLaunch: function () {\r\n    console.log('App Launch')\r\n\r\n    // 2. 在App启动时，获取、缓存并加载动态资源\r\n    this.fetchAndCacheAssets();\r\n\r\n    // 保留uview-plus图标字体的加载\r\n    this.loadIconFont();\r\n  },\r\n  onShow: function () {\r\n    console.log('App Show')\r\n  },\r\n  onHide: function () {\r\n    console.log('App Hide')\r\n  },\r\n  methods: {\r\n    /**\r\n     * 获取、缓存静态资源，并加载动态字体\r\n     */\r\n    async fetchAndCacheAssets() {\r\n      try {\r\n        const response = await getAllAssets();\r\n        if (response.code === 200 && Array.isArray(response.data)) {\r\n          const assetMap = response.data.reduce((map, item) => {\r\n            if (item.assetKey && item.assetUrl) {\r\n              map[item.assetKey] = item.assetUrl;\r\n            }\r\n            return map;\r\n          }, {});\r\n\r\n          uni.setStorageSync('staticAssets', assetMap);\r\n          console.log('小程序静态资源已更新并缓存成功！', assetMap);\r\n\r\n          // 缓存成功后，立即尝试加载动态字体\r\n          this.loadDynamicFonts(assetMap);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取小程序静态资源失败', error);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载从后台配置的动态字体\r\n     * @param {object} assets - 从缓存中获取的资源对象\r\n     */\r\n    loadDynamicFonts(assets) {\r\n      // 定义我们要加载的字体信息\r\n      const fontToLoad = {\r\n        // 【关键修正】这个 key 必须与您在后台设置的“资源唯一键”完全一致\r\n        key: 'Puhuiti_fonts',\r\n        // 这是我们在CSS中将要使用的字体名称\r\n        family: 'Alibaba PuHuiTi 3.0',\r\n        // 字体粗细 (根据您上传的字体文件设定)\r\n        weight: '400'\r\n      };\r\n\r\n      // 检查资源对象中是否存在我们需要的字体\r\n      if (assets && assets[fontToLoad.key]) {\r\n        const fontUrl = assets[fontToLoad.key];\r\n\r\n        // 调用 uni.loadFontFace API 进行加载\r\n        uni.loadFontFace({\r\n          global: true, // 设置为全局字体\r\n          family: fontToLoad.family,\r\n          source: `url(\"${fontUrl}\")`,\r\n          desc: {\r\n            weight: fontToLoad.weight\r\n          },\r\n          success() {\r\n            console.log(`动态字体 [${fontToLoad.family}] 加载成功!`);\r\n          },\r\n          fail(err) {\r\n            console.error(`动态字体 [${fontToLoad.family}] 加载失败:`, err);\r\n          }\r\n        });\r\n      } else {\r\n        console.warn(`未在资源配置中找到字体资源: ${fontToLoad.key}`);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载uView图标字体 (保留)\r\n     */\r\n    loadIconFont() {\r\n      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\r\n      uni.loadFontFace({\r\n        global: true,\r\n        family: 'uicon-iconfont',\r\n        source: 'url(\"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf\")',\r\n        success() {\r\n          console.log('uview-plus图标字体加载成功');\r\n        },\r\n        fail(err) {\r\n          console.error('uview-plus图标字体加载失败:', err);\r\n          uni.loadFontFace({\r\n            global: true,\r\n            family: 'uicon-iconfont',\r\n            source: 'url(\"https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf\")',\r\n            success() {\r\n              console.log('备用图标字体加载成功');\r\n            },\r\n            fail(err2) {\r\n              console.error('备用图标字体也加载失败:', err2);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * 【已移除】loadPuHuiTiFonts 方法已被移除，因为字体现在是动态加载的\r\n     */\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 每个页面公共css */\r\n@import \"@/uni_modules/uview-plus/index.scss\";\r\n\r\n/* 防止页面跳转时内容残留 */\r\nuni-page-wrapper {\r\n  overflow: hidden !important;\r\n}\r\nuni-page-body {\r\n  overflow: hidden !important;\r\n}\r\n\r\n/* 这里的字体栈保持不变，小程序会优先使用 uni.loadFontFace 加载的字体 */\r\nbody, page, view, text {\r\n  font-family: 'Alibaba PuHuiTi 3.0', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica', 'sans-serif';\r\n}\r\n</style>\r\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nimport uviewPlus from '@/uni_modules/uview-plus'\r\n\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\r\n\t// 保留您原有的 uview-plus 插件配置\r\n\tapp.use(uviewPlus, () => {\r\n\t\treturn {\r\n\t\t\toptions: {\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\tunit: 'rpx'\r\n\t\t\t\t},\r\n\t\t\t\tprops: {\r\n\t\t\t\t\t// ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\r\n// #endif"], "names": ["uni", "getAllAssets", "createSSRApp", "App", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAK,YAAU;AAAA,EACb,UAAU,WAAY;AACpBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AAGxB,SAAK,oBAAmB;AAGxB,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,WAAW,MAAMC,mBAAAA;AACvB,YAAI,SAAS,SAAS,OAAO,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,gBAAM,WAAW,SAAS,KAAK,OAAO,CAAC,KAAK,SAAS;AACnD,gBAAI,KAAK,YAAY,KAAK,UAAU;AAClC,kBAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,YAC5B;AACA,mBAAO;AAAA,UACR,GAAE,CAAE,CAAA;AAELD,wBAAAA,MAAI,eAAe,gBAAgB,QAAQ;AAC3CA,wBAAY,MAAA,MAAA,OAAA,iBAAA,oBAAoB,QAAQ;AAGxC,eAAK,iBAAiB,QAAQ;AAAA,QAChC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iBAAc,eAAe,KAAK;AAAA,MACpC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,iBAAiB,QAAQ;AAEvB,YAAM,aAAa;AAAA;AAAA,QAEjB,KAAK;AAAA;AAAA,QAEL,QAAQ;AAAA;AAAA,QAER,QAAQ;AAAA;AAIV,UAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACpC,cAAM,UAAU,OAAO,WAAW,GAAG;AAGrCA,sBAAAA,MAAI,aAAa;AAAA,UACf,QAAQ;AAAA;AAAA,UACR,QAAQ,WAAW;AAAA,UACnB,QAAQ,QAAQ,OAAO;AAAA,UACvB,MAAM;AAAA,YACJ,QAAQ,WAAW;AAAA,UACpB;AAAA,UACD,UAAU;AACRA,8DAAY,SAAS,WAAW,MAAM,SAAS;AAAA,UAChD;AAAA,UACD,KAAK,KAAK;AACRA,0BAAAA,MAAA,MAAA,SAAA,iBAAc,SAAS,WAAW,MAAM,WAAW,GAAG;AAAA,UACxD;AAAA,QACF,CAAC;AAAA,aACI;AACLA,2DAAa,kBAAkB,WAAW,GAAG,EAAE;AAAA,MACjD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AAEbA,oBAAAA,MAAI,aAAa;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AACRA,wBAAAA,MAAY,MAAA,OAAA,iBAAA,oBAAoB;AAAA,QACjC;AAAA,QACD,KAAK,KAAK;AACRA,wBAAA,MAAA,MAAA,SAAA,iBAAc,uBAAuB,GAAG;AACxCA,wBAAAA,MAAI,aAAa;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,UAAU;AACRA,4BAAAA,MAAA,MAAA,OAAA,kBAAY,YAAY;AAAA,YACzB;AAAA,YACD,KAAK,MAAM;AACTA,4BAAc,MAAA,MAAA,SAAA,kBAAA,gBAAgB,IAAI;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IAEF;AAAA;AAAA;AAAA;AAAA,EAKH;AACF;ACtGO,SAAS,YAAY;AAC3B,QAAM,MAAME,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,4BAAAA,WAAW,MAAM;AACxB,WAAO;AAAA,MACN,SAAS;AAAA,QACR,QAAQ;AAAA,UACP,MAAM;AAAA,QACN;AAAA,QACD,OAAO;AAAA;AAAA,QAEN;AAAA,MACD;AAAA,IACD;AAAA,EACH,CAAE;AAED,SAAO;AAAA,IACN;AAAA,EACA;AACF;;;"}