{"version": 3, "file": "code.js", "sources": ["uni_modules/uview-plus/components/u-code/code.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:55:27\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/code.js\r\n */\r\n\r\nexport default {\r\n    // code 组件\r\n    code: {\r\n        seconds: 60,\r\n        startText: '获取验证码',\r\n        changeText: 'X秒重新获取',\r\n        endText: '重新获取',\r\n        keepRunning: false,\r\n        uniqueKey: ''\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AAUA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,aAAa;AAAA,IACb,WAAW;AAAA,EACd;AACL;;"}