"use strict";
const common_vendor = require("../../common/vendor.js");
const pages_sub_pages_other_api_content_park = require("./api/content/park.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  (_easycom_uni_load_more2 + _easycom_uni_icons2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_icons)();
}
const navBarPaddingBottomRpx = 20;
const _sfc_main = {
  __name: "park_detail",
  setup(__props) {
    const navBarPaddingBottomPx = common_vendor.index.upx2px(navBarPaddingBottomRpx);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const getNavBarInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        statusBarHeight.value = menuButtonInfo.top;
        navBarHeight.value = menuButtonInfo.height;
        headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
      } catch (e) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        navBarHeight.value = 44;
        headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    const baseUrl = utils_config.IMAGE_BASE_URL;
    const park = common_vendor.ref(null);
    const loading = common_vendor.ref(true);
    common_vendor.onLoad(async (options) => {
      getNavBarInfo();
      if (!options.id) {
        common_vendor.index.showToast({ title: "参数错误", icon: "none" });
        common_vendor.index.navigateBack();
        return;
      }
      try {
        const res = await pages_sub_pages_other_api_content_park.getParkDetail(options.id);
        park.value = res.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/park_detail.vue:107", error);
      } finally {
        loading.value = false;
      }
    });
    const goToCountryDetail = () => {
      if (park.value && park.value.countryId) {
        common_vendor.index.navigateTo({
          url: `/pages_sub/pages_country/detail?id=${park.value.countryId}`
        });
      }
    };
    common_vendor.onShareAppMessage(() => {
      var _a, _b;
      return {
        title: ((_a = park.value) == null ? void 0 : _a.name) || "园区详情",
        path: `/pages_sub/pages_other/park_detail?id=${((_b = park.value) == null ? void 0 : _b.id) || ""}`
      };
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loading.value
      }, loading.value ? {
        b: common_vendor.p({
          status: "loading"
        })
      } : park.value ? {
        d: statusBarHeight.value + "px",
        e: common_vendor.p({
          type: "left",
          color: "#000000",
          size: "22"
        }),
        f: common_vendor.o(goBack),
        g: common_vendor.t(park.value.name || "园区详情"),
        h: navBarHeight.value + "px",
        i: headerHeight.value + "px",
        j: common_vendor.unref(baseUrl) + park.value.coverImageUrl,
        k: common_vendor.t(park.value.name),
        l: common_vendor.p({
          type: "location-filled",
          size: "16",
          color: "#999"
        }),
        m: common_vendor.t(park.value.location),
        n: common_vendor.p({
          type: "paperclip",
          size: "16",
          color: "#999"
        }),
        o: common_vendor.t(park.value.mainIndustries),
        p: common_vendor.t(park.value.summary),
        q: park.value.content,
        r: common_vendor.t(park.value.countryName),
        s: common_vendor.p({
          type: "right",
          size: "16",
          color: "#2979ff"
        }),
        t: common_vendor.o(goToCountryDetail),
        v: headerHeight.value + "px"
      } : {}, {
        c: park.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1d11aec2"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/park_detail.js.map
