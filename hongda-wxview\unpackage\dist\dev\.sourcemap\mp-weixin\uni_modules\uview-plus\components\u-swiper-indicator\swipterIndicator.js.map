{"version": 3, "file": "swipterIndicator.js", "sources": ["uni_modules/uview-plus/components/u-swiper-indicator/swipterIndicator.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:22:07\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiperIndicator.js\r\n */\r\nexport default {\r\n    // swiperIndicator 组件\r\n    swiperIndicator: {\r\n        length: 0,\r\n        current: 0,\r\n        indicatorActiveColor: '',\r\n        indicatorInactiveColor: '',\r\n\t\tindicatorMode: 'line'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,mBAAA;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IAC9B,eAAe;AAAA,EACZ;AACL;;"}