{"version": 3, "file": "list.js", "sources": ["uni_modules/uview-plus/components/u-list/list.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:14:53\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/list.js\r\n */\r\nexport default {\r\n    // list 组件\r\n    list: {\r\n        showScrollbar: false,\r\n        lowerThreshold: 50,\r\n        upperThreshold: 0,\r\n        scrollTop: 0,\r\n        offsetAccuracy: 10,\r\n        enableFlex: false,\r\n        pagingEnabled: false,\r\n        scrollable: true,\r\n        scrollIntoView: '',\r\n        scrollWithAnimation: false,\r\n        enableBackToTop: false,\r\n        height: 0,\r\n        width: 0,\r\n        preLoadScreen: 1\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,eAAe;AAAA,EAClB;AACL;;"}