<view class="orders-page data-v-79fff75c"><up-navbar wx:if="{{a}}" class="data-v-79fff75c" u-i="79fff75c-0" bind:__l="__l" u-p="{{a}}"/><scroll-view scroll-y class="orders-list-scroll data-v-79fff75c" bindscrolltolower="{{l}}" refresher-enabled refresher-triggered="{{m}}" bindrefresherrefresh="{{n}}"><view wx:if="{{b}}" class="loading-state data-v-79fff75c"><up-loading-page wx:if="{{c}}" class="data-v-79fff75c" u-i="79fff75c-1" bind:__l="__l" u-p="{{c}}"/></view><view wx:elif="{{d}}" class="empty-state data-v-79fff75c"><up-empty wx:if="{{h}}" class="data-v-79fff75c" u-s="{{['bottom']}}" u-i="79fff75c-2" bind:__l="__l" u-p="{{h}}"><up-button wx:if="{{e}}" class="data-v-79fff75c" bindclick="{{f}}" u-i="79fff75c-3,79fff75c-2" bind:__l="__l" u-p="{{g}}" slot="bottom"/></up-empty></view><view wx:else class="orders-list data-v-79fff75c"><view wx:for="{{i}}" wx:for-item="item" wx:key="m" class="order-card data-v-79fff75c" bindtap="{{item.n}}"><view class="card-left data-v-79fff75c"><image src="{{item.a}}" mode="aspectFill" class="event-image data-v-79fff75c" lazy-load="{{true}}" binderror="{{item.b}}" bindload="{{item.c}}"/></view><view class="card-right data-v-79fff75c"><text class="event-title data-v-79fff75c">{{item.d}}</text><view class="event-info registration-time data-v-79fff75c"><text class="time-label data-v-79fff75c">报名时间：</text><text class="time-value data-v-79fff75c">{{item.e}}</text></view><view class="card-bottom-row data-v-79fff75c"><view class="data-v-79fff75c"><view wx:if="{{item.f}}" class="status-with-bg data-v-79fff75c"><image class="status-bg-image data-v-79fff75c" src="{{item.g}}" mode="aspectFit"></image><text class="status-text data-v-79fff75c">已报名</text></view><view wx:elif="{{item.h}}" class="status-cancelled data-v-79fff75c"><text class="status-text data-v-79fff75c">已取消</text></view><view wx:else class="{{['data-v-79fff75c', 'registration-status-tag', item.j]}}">{{item.i}}</view></view></view></view><view wx:if="{{item.k}}" class="cancel-btn-absolute data-v-79fff75c" catchtap="{{item.l}}"> 取消报名 </view></view></view><view wx:if="{{j}}" class="loadmore-wrapper data-v-79fff75c"><up-loadmore wx:if="{{k}}" class="data-v-79fff75c" u-i="79fff75c-4" bind:__l="__l" u-p="{{k}}"/></view></scroll-view><custom-tab-bar wx:if="{{o}}" class="data-v-79fff75c" u-i="79fff75c-5" bind:__l="__l" u-p="{{o}}"/><view wx:if="{{p}}" class="cancel-modal-overlay data-v-79fff75c" bindtap="{{v}}"><view class="cancel-modal-content data-v-79fff75c" catchtap="{{t}}"><view class="modal-header data-v-79fff75c"><image class="warning-icon data-v-79fff75c" src="{{q}}" mode="aspectFit"></image><text class="modal-title data-v-79fff75c">操作提示</text></view><view class="modal-body data-v-79fff75c"><text class="modal-message data-v-79fff75c">是否取消报名？</text></view><view class="modal-footer data-v-79fff75c"><view class="modal-btn cancel-btn data-v-79fff75c" bindtap="{{r}}"> 暂不取消 </view><view class="modal-btn confirm-btn data-v-79fff75c" bindtap="{{s}}"> 确认取消 </view></view></view></view></view>