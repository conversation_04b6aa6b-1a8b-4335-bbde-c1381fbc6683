{"version": 3, "file": "utils.js", "sources": ["uni_modules/uview-plus/libs/luch-request/utils.js"], "sourcesContent": ["'use strict'\r\n\r\n// utils is a library of generic helper functions non-specific to axios\r\n\r\nconst { toString } = Object.prototype\r\n\r\n/**\r\n * Determine if a value is an Array\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is an Array, otherwise false\r\n */\r\nexport function isArray(val) {\r\n    return toString.call(val) === '[object Array]'\r\n}\r\n\r\n/**\r\n * Determine if a value is an Object\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is an Object, otherwise false\r\n */\r\nexport function isObject(val) {\r\n    return val !== null && typeof val === 'object'\r\n}\r\n\r\n/**\r\n * Determine if a value is a Date\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is a Date, otherwise false\r\n */\r\nexport function isDate(val) {\r\n    return toString.call(val) === '[object Date]'\r\n}\r\n\r\n/**\r\n * Determine if a value is a URLSearchParams object\r\n *\r\n * @param {Object} val The value to test\r\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\r\n */\r\nexport function isURLSearchParams(val) {\r\n    return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams\r\n}\r\n\r\n/**\r\n * Iterate over an Array or an Object invoking a function for each item.\r\n *\r\n * If `obj` is an Array callback will be called passing\r\n * the value, index, and complete array for each item.\r\n *\r\n * If 'obj' is an Object callback will be called passing\r\n * the value, key, and complete object for each property.\r\n *\r\n * @param {Object|Array} obj The object to iterate\r\n * @param {Function} fn The callback to invoke for each item\r\n */\r\nexport function forEach(obj, fn) {\r\n    // Don't bother if no value provided\r\n    if (obj === null || typeof obj === 'undefined') {\r\n        return\r\n    }\r\n\r\n    // Force an array if not already something iterable\r\n    if (typeof obj !== 'object') {\r\n    /* eslint no-param-reassign:0 */\r\n        obj = [obj]\r\n    }\r\n\r\n    if (isArray(obj)) {\r\n    // Iterate over array values\r\n        for (let i = 0, l = obj.length; i < l; i++) {\r\n            fn.call(null, obj[i], i, obj)\r\n        }\r\n    } else {\r\n    // Iterate over object keys\r\n        for (const key in obj) {\r\n            if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n                fn.call(null, obj[key], key, obj)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 是否为boolean 值\r\n * @param val\r\n * @returns {boolean}\r\n */\r\nexport function isBoolean(val) {\r\n    return typeof val === 'boolean'\r\n}\r\n\r\n/**\r\n * 是否为真正的对象{} new Object\r\n * @param {any} obj - 检测的对象\r\n * @returns {boolean}\r\n */\r\nexport function isPlainObject(obj) {\r\n    return Object.prototype.toString.call(obj) === '[object Object]'\r\n}\r\n\r\n/**\r\n * Function equal to merge with the difference being that no reference\r\n * to original objects is kept.\r\n *\r\n * @see merge\r\n * @param {Object} obj1 Object to merge\r\n * @returns {Object} Result of all merge properties\r\n */\r\nexport function deepMerge(/* obj1, obj2, obj3, ... */) {\r\n    const result = {}\r\n    function assignValue(val, key) {\r\n        if (typeof result[key] === 'object' && typeof val === 'object') {\r\n            result[key] = deepMerge(result[key], val)\r\n        } else if (typeof val === 'object') {\r\n            result[key] = deepMerge({}, val)\r\n        } else {\r\n            result[key] = val\r\n        }\r\n    }\r\n    for (let i = 0, l = arguments.length; i < l; i++) {\r\n        forEach(arguments[i], assignValue)\r\n    }\r\n    return result\r\n}\r\n\r\nexport function isUndefined(val) {\r\n    return typeof val === 'undefined'\r\n}\r\n"], "names": [], "mappings": ";AAIA,MAAM,EAAE,SAAQ,IAAK,OAAO;AAQrB,SAAS,QAAQ,KAAK;AACzB,SAAO,SAAS,KAAK,GAAG,MAAM;AAClC;AAQO,SAAS,SAAS,KAAK;AAC1B,SAAO,QAAQ,QAAQ,OAAO,QAAQ;AAC1C;AAQO,SAAS,OAAO,KAAK;AACxB,SAAO,SAAS,KAAK,GAAG,MAAM;AAClC;AAQO,SAAS,kBAAkB,KAAK;AACnC,SAAO,OAAO,oBAAoB,eAAe,eAAe;AACpE;AAcO,SAAS,QAAQ,KAAK,IAAI;AAE7B,MAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC5C;AAAA,EACH;AAGD,MAAI,OAAO,QAAQ,UAAU;AAEzB,UAAM,CAAC,GAAG;AAAA,EACb;AAED,MAAI,QAAQ,GAAG,GAAG;AAEd,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,SAAG,KAAK,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,IAC/B;AAAA,EACT,OAAW;AAEH,eAAW,OAAO,KAAK;AACnB,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAChD,WAAG,KAAK,MAAM,IAAI,GAAG,GAAG,KAAK,GAAG;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACL;AAgBO,SAAS,cAAc,KAAK;AAC/B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACnD;AAUO,SAAS,YAAuC;AACnD,QAAM,SAAS,CAAE;AACjB,WAAS,YAAY,KAAK,KAAK;AAC3B,QAAI,OAAO,OAAO,GAAG,MAAM,YAAY,OAAO,QAAQ,UAAU;AAC5D,aAAO,GAAG,IAAI,UAAU,OAAO,GAAG,GAAG,GAAG;AAAA,IACpD,WAAmB,OAAO,QAAQ,UAAU;AAChC,aAAO,GAAG,IAAI,UAAU,CAAA,GAAI,GAAG;AAAA,IAC3C,OAAe;AACH,aAAO,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ;AACD,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC9C,YAAQ,UAAU,CAAC,GAAG,WAAW;AAAA,EACpC;AACD,SAAO;AACX;AAEO,SAAS,YAAY,KAAK;AAC7B,SAAO,OAAO,QAAQ;AAC1B;;;;;;;;;"}