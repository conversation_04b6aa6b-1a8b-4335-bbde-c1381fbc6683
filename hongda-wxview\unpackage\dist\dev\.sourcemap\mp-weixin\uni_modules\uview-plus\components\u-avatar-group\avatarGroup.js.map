{"version": 3, "file": "avatarGroup.js", "sources": ["uni_modules/uview-plus/components/u-avatar-group/avatarGroup.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:49:55\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/avatarGroup.js\r\n */\r\nexport default {\r\n    // avatarGroup 组件\r\n    avatarGroup: {\r\n        urls: [],\r\n        maxCount: 5,\r\n        shape: 'circle',\r\n        mode: 'scaleToFill',\r\n        showMore: true,\r\n        size: 40,\r\n        keyName: '',\r\n        gap: 0.5,\r\n\t\textraValue: 0\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM,CAAE;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,KAAK;AAAA,IACX,YAAY;AAAA,EACT;AACL;;"}