<u-popup wx:if="{{n}}" class="data-v-0a64a93b" u-s="{{['d']}}" bindclose="{{m}}" u-i="0a64a93b-0" bind:__l="__l" u-p="{{n}}"><view class="reply-popup data-v-0a64a93b"><view class="popup-header data-v-0a64a93b"><text wx:if="{{a}}" class="popup-title data-v-0a64a93b">回复 @{{b}}</text><text class="popup-close data-v-0a64a93b" bindtap="{{c}}">×</text></view><view class="popup-body data-v-0a64a93b"><block wx:if="{{r0}}"><textarea class="reply-input data-v-0a64a93b" placeholder="写下你的回复..." auto-height="{{true}}" maxlength="300" focus="{{d}}" value="{{e}}" bindinput="{{f}}"></textarea></block><view class="popup-footer data-v-0a64a93b"><text class="reply-counter data-v-0a64a93b">{{g}}/300</text><button class="{{['reply-submit', 'data-v-0a64a93b', i && 'reply-submit-active']}}" disabled="{{j}}" loading="{{k}}" bindtap="{{l}}">{{h}}</button></view></view></view></u-popup>