{"version": 3, "file": "modal.js", "sources": ["uni_modules/uview-plus/components/u-modal/modal.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:15:59\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/modal.js\r\n */\r\nexport default {\r\n    // modal 组件\r\n    modal: {\r\n        show: false,\r\n        title: '',\r\n        content: '',\r\n        confirmText: '确认',\r\n        cancelText: '取消',\r\n        showConfirmButton: true,\r\n        showCancelButton: false,\r\n        confirmColor: '#2979ff',\r\n        cancelColor: '#606266',\r\n        buttonReverse: false,\r\n        zoom: true,\r\n        asyncClose: false,\r\n        closeOnClickOverlay: false,\r\n        negativeTop: 0,\r\n        width: '650rpx',\r\n        confirmButtonShape: '',\r\n        duration: 400,\r\n        contentTextAlign: 'left',\r\n        asyncCloseTip: '操作中...',\r\n        asyncCancelClose: false,\r\n        contentStyle: {}\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc,CAAE;AAAA,EACnB;AACL;;"}