{"version": 3, "file": "asset.js", "sources": ["api/platform/asset.js"], "sourcesContent": ["import { get } from '@/utils/request.js'; // 导入您项目封装的请求方法\r\n\r\n/**\r\n * 获取所有小程序静态资源\r\n * @returns {Promise}\r\n */\r\nexport function getAllAssets() {\r\n    // 调用我们刚刚在后端创建的接口\r\n    // request.js 中已经拼接了 /api/v1 前缀，所以这里直接写 /assets/all\r\n    return get('/assets/all');\r\n}"], "names": ["get"], "mappings": ";;AAMO,SAAS,eAAe;AAG3B,SAAOA,cAAAA,IAAI,aAAa;AAC5B;;"}