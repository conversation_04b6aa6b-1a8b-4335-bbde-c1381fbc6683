{"version": 3, "file": "u-picker.js", "sources": ["uni_modules/uview-plus/components/u-picker/u-picker.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXBpY2tlci91LXBpY2tlci52dWU"], "sourcesContent": ["<template>\r\n    <view class=\"u-picker-wraper\">\r\n\t\t<view v-if=\"hasInput\" class=\"u-picker-input cursor-pointer\" @click=\"onShowByClickInput\">\r\n\t\t\t<slot :value=\"inputLabel\">\r\n\t\t\t</slot>\r\n\t\t\t<slot name=\"trigger\" :value=\"inputLabel\">\r\n\t\t\t</slot>\r\n\t\t\t<up-input\r\n\t\t\t\tv-if=\"!$slots['default'] && !$slots['$default'] && !$slots['trigger']\"\r\n\t\t\t\t:readonly=\"true\"\r\n\t\t\t\tv-model=\"inputLabel\"\r\n\t\t\t\tv-bind=\"inputPropsInner\">\r\n\t\t\t</up-input>\r\n\t\t\t<div class=\"input-cover\"></div>\r\n\t\t</view>\r\n\t\t<u-popup\r\n\t\t\t:show=\"show || (hasInput && showByClickInput)\"\r\n\t\t\t:mode=\"popupMode\"\r\n\t\t\t:zIndex=\"zIndex\"\r\n\t\t\t:bgColor=\"bgColor\"\r\n\t\t\t:round=\"round\"\r\n\t\t\t:duration=\"duration\"\r\n\t\t\t:overlayOpacity=\"overlayOpacity\"\r\n\t\t\t@close=\"closeHandler\"\r\n\t\t>\r\n\t\t\t<view class=\"u-picker\">\r\n\t\t\t\t<u-toolbar\r\n\t\t\t\t\tv-if=\"showToolbar\"\r\n\t\t\t\t\t:cancelColor=\"cancelColor\"\r\n\t\t\t\t\t:confirmColor=\"confirmColor\"\r\n\t\t\t\t\t:cancelText=\"cancelText\"\r\n\t\t\t\t\t:confirmText=\"confirmText\"\r\n\t\t\t\t\t:title=\"title\"\r\n\t\t\t\t\t:rightSlot=\"toolbarRightSlot ? true : false\"\r\n\t\t\t\t\t@cancel=\"cancel\"\r\n\t\t\t\t\t@confirm=\"confirm\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<template #right>\r\n\t\t\t\t\t\t<slot name=\"toolbar-right\"></slot>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</u-toolbar>\r\n\t\t\t\t<slot name=\"toolbar-bottom\"></slot>\r\n\t\t\t\t<picker-view\r\n\t\t\t\t\tclass=\"u-picker__view\"\r\n\t\t\t\t\t:indicatorStyle=\"`height: ${addUnit(itemHeight, 'px')}`\"\r\n\t\t\t\t\t:value=\"innerIndex\"\r\n\t\t\t\t\t:immediateChange=\"immediateChange\"\r\n\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\theight: `${addUnit(visibleItemCount * itemHeight, 'px')}`\r\n\t\t\t\t\t}\"\r\n\t\t\t\t\t@change=\"changeHandler\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<picker-view-column\r\n\t\t\t\t\t\tv-for=\"(item, index) in innerColumns\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"u-picker__view__column\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-if=\"testArray(item)\"\r\n\t\t\t\t\t\t\tclass=\"u-picker__view__column__item u-line-1\"\r\n\t\t\t\t\t\t\t:class=\"[index1 === innerIndex[index] && 'u-picker__view__column__item--selected']\"\r\n\t\t\t\t\t\t\tv-for=\"(item1, index1) in item\"\r\n\t\t\t\t\t\t\t:key=\"index1\"\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\theight: addUnit(itemHeight, 'px'),\r\n\t\t\t\t\t\t\t\tlineHeight: addUnit(itemHeight, 'px'),\r\n\t\t\t\t\t\t\t\tfontWeight: index1 === innerIndex[index] ? 'bold' : 'normal',\r\n\t\t\t\t\t\t\t\tdisplay: 'block'\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t>{{ getItemText(item1) }}</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-if=\"loading\"\r\n\t\t\t\t\tclass=\"u-picker--loading\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<u-loading-icon mode=\"circle\"></u-loading-icon>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * u-picker\r\n * @description 选择器\r\n * @property {Boolean}\t\t\tshow\t\t\t\t是否显示picker弹窗（默认 false ）\r\n * @property {Boolean}\t\t\tshowToolbar\t\t\t是否显示顶部的操作栏（默认 true ）\r\n * @property {String}\t\t\ttitle\t\t\t\t顶部标题\r\n * @property {Array}\t\t\tcolumns\t\t\t\t对象数组，设置每一列的数据\r\n * @property {Boolean}\t\t\tloading\t\t\t\t是否显示加载中状态（默认 false ）\r\n * @property {String | Number}\titemHeight\t\t\t各列中，单个选项的高度（默认 44 ）\r\n * @property {String}\t\t\tcancelText\t\t\t取消按钮的文字（默认 '取消' ）\r\n * @property {String}\t\t\tconfirmText\t\t\t确认按钮的文字（默认 '确定' ）\r\n * @property {String}\t\t\tcancelColor\t\t\t取消按钮的颜色（默认 '#909193' ）\r\n * @property {String}\t\t\tconfirmColor\t\t确认按钮的颜色（默认 '#3c9cff' ）\r\n * @property {String | Number}\tvisibleItemCount\t每列中可见选项的数量（默认 5 ）\r\n * @property {String}\t\t\tkeyName\t\t\t\t选项对象中，需要展示的属性键名（默认 'text' ）\r\n * @property {Boolean}\t\t\tcloseOnClickOverlay\t是否允许点击遮罩关闭选择器（默认 false ）\r\n * @property {Array}\t\t\tdefaultIndex\t\t各列的默认索引\r\n * @property {Boolean}\t\t\timmediateChange\t\t是否在手指松开时立即触发change事件（默认 true ）\r\n * @property {String | Number}\tround\t\t\t\t圆角值（默认 0）\r\n * @property {String }\t        bgColor\t\t\t\t背景色值（默认 '' ）\r\n * @property {String | Number}\tduration\t\t\t动画时长，单位ms （默认 300 ）\r\n * @property {String | Number}\toverlayDuration\t\t遮罩层动画时长，单位ms （默认 350 ）\r\n * @pages_event {Function} close\t\t关闭选择器时触发\r\n * @pages_event {Function} cancel\t\t点击取消按钮触发\r\n * @pages_event {Function} change\t\t当选择值变化时触发\r\n * @pages_event {Function} confirm\t点击确定按钮，返回当前选择的值\r\n */\r\nimport { props } from './props';\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addUnit, deepClone, sleep } from '../../libs/function/index';\r\nimport test from '../../libs/function/test';\r\nexport default {\r\n\tname: 'u-picker',\r\n\tmixins: [mpMixin, mixin, props],\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 上一次选择的列索引\r\n\t\t\tlastIndex: [],\r\n\t\t\t// 索引值 ，对应picker-view的value\r\n\t\t\tinnerIndex: [],\r\n\t\t\t// 各列的值\r\n\t\t\tinnerColumns: [],\r\n\t\t\t// 上一次的变化列索引\r\n\t\t\tcolumnIndex: 0,\r\n            showByClickInput: false,\r\n\t\t\tcurrentActiveValue: [] //当前用户选中，但是还没确认的值，用户没做change操作时候，点击确认可以默认选中第一个\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听columns参数的变化\r\n\t\tcolumns: {\r\n\t\t\timmediate: true,\r\n\t\t\tdeep:true,\r\n\t\t\thandler(n) {\r\n\t\t\t\tthis.setColumns(n)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 监听默认索引的变化，重新设置对应的值\r\n\t\tdefaultIndex: {\r\n\t\t\timmediate: true,\r\n\t\t\tdeep:true,\r\n\t\t\thandler(n,o) {\r\n\t\t\t\t// 修复uniapp调用子组件直接:defaultIndex=\"[0]\"这样写\r\n\t\t\t\t// v-model的值变化时候导致defaultIndexwatch也会执行的问题\r\n\t\t\t\t//单纯vue不会出现\r\n\t\t\t\tif (!o || n.join(\"/\") != o.join(\"/\")) {\r\n\t\t\t\t\tthis.setIndexs(n, true)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmodelValue: {\r\n\t\t\timmediate: true,\r\n\t\t\tdeep:true,\r\n\t\t\thandler(n,o) {\r\n\t\t\t\t// 修复uniapp调用子组件直接:defaultIndex=\"[0]\"这样写\r\n\t\t\t\t// v-model的值变化时候导致defaultIndexwatch也会执行的问题\r\n\t\t\t\t//单纯vue不会出现\r\n\t\t\t\tif (!o || n.join(\"/\") != o.join(\"/\")) {\r\n\t\t\t\t\tlet arr = [];\r\n\t\t\t\t\tif (n != null) {\r\n\t\t\t\t\t\tn.forEach((element, index) => {\r\n\t\t\t\t\t\t\tlet currentCols = this.getColumnValues(index)\r\n\t\t\t\t\t\t\tif (currentCols && Object.prototype.toString.call(currentCols) === '[object Object]') {\r\n\t\t\t\t\t\t\t\tcurrentCols.forEach((item, index2) => {\r\n\t\t\t\t\t\t\t\t\tif (item[this.keyName] == element) {\r\n\t\t\t\t\t\t\t\t\t\tarr.push(index2)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tcurrentCols.forEach((item, index2) => {\r\n\t\t\t\t\t\t\t\t\tif (item == element) {\r\n\t\t\t\t\t\t\t\t\t\tarr.push(index2)\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// alert(arr)\r\n\t\t\t\t\t\tif (arr.length == 0 && this.defaultIndex) {\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.setIndexs(arr, true)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\temits: ['close', 'cancel', 'confirm', 'change', 'update:modelValue', 'update:show'],\r\n    computed: {\r\n\t\t// input的props\r\n\t\tinputPropsInner() {\r\n\t\t\treturn {\r\n\t\t\t\tborder: this.inputBorder,\r\n\t\t\t\tplaceholder: this.placeholder,\r\n\t\t\t\tdisabled: this.disabled,\r\n\t\t\t\tdisabledColor: this.disabledColor,\r\n\t\t\t\t...this.inputProps\r\n\t\t\t}\r\n\t\t},\r\n\t\t//已选&&已确认的值显示在input上面的文案\r\n\t\tinputLabel() {\r\n\t\t\tlet firstItem = this.innerColumns[0] && this.innerColumns[0][0];\r\n\t\t\t// //区分是不是对象数组\r\n\t\t\tif (firstItem && Object.prototype.toString.call(firstItem) === '[object Object]') {\r\n\t\t\t\tlet res = this.innerColumns[0].filter(item => this.modelValue.includes(item['id']))\r\n\t\t\t\tres = res.map(item => item[this.keyName]);\r\n\t\t\t\treturn res.join(\"/\");\r\n\r\n\t\t\t} else {\r\n\t\t\t\t//用户确定的值，才显示到输入框\r\n\t\t\t\treturn this.modelValue.join(\"/\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t//已选，待确认的值\r\n\t\tinputValue() {\r\n\t\t\tlet items = this.innerColumns.map((item, index) => item[this.innerIndex[index]])\r\n\t\t\tlet res = []\r\n\t\t\t//区分是不是对象数组\r\n\t\t\tif (items[0] && Object.prototype.toString.call(items[0]) === '[object Object]') {\r\n\t\t\t\t//对象数组返回属性值集合\r\n\t\t\t\titems.forEach(element => {\r\n\t\t\t\t\tres.push(element && element[this.valueName])\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t//非对象数组返回元素集合\r\n\t\t\t\titems.forEach((element, index) => {\r\n\t\t\t\t\tres.push(element)\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\treturn res\r\n\t\t}\r\n    },\r\n\tmethods: {\r\n\t\taddUnit,\r\n\t\ttestArray: test.array,\r\n\t\tonShowByClickInput(){\r\n\t\t\tif(!this.disabled){\r\n\t\t\t\tthis.showByClickInput=!this.showByClickInput;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取item需要显示的文字，判别为对象还是文本\r\n\t\tgetItemText(item) {\r\n\t\t\tif (test.object(item)) {\r\n\t\t\t\treturn item[this.keyName]\r\n\t\t\t} else {\r\n\t\t\t\treturn item\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 关闭选择器\r\n\t\tcloseHandler() {\r\n\t\t\tif (this.closeOnClickOverlay) {\r\n                if (this.hasInput) {\r\n                    this.showByClickInput = false\r\n                }\r\n\t\t\t\tthis.setDefault()\r\n\t\t\t\tthis.$emit('update:show', false)\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 点击工具栏的取消按钮\r\n\t\tcancel() {\r\n            if (this.hasInput) {\r\n                this.showByClickInput = false\r\n            }\r\n\t\t\tthis.setDefault()\r\n\t\t\tthis.$emit('update:show', false)\r\n\t\t\tthis.$emit('cancel')\r\n\t\t},\r\n\t\tsetDefault() {\r\n\t\t\tlet arr = [0]\r\n\t\t\tif (this.lastIndex.length == 0) {\r\n\t\t\t\t//如果有默认值&&默认值的数组长度是正确的，就用默认值\r\n\t\t\t\tif (Array.isArray(this.defaultIndex) && this.defaultIndex.length == this.innerColumns.length) {\r\n\t\t\t\t\tarr = [...this.defaultIndex];\r\n\t\t\t\t} else {\r\n\t\t\t\t\t//否则默认都选中第一个\r\n\t\t\t\t\tarr = Array(this.innerColumns.length).fill(0);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tarr = deepClone(this.lastIndex)\r\n\t\t\t}\r\n\t\t\tthis.setLastIndex(arr)\r\n\t\t\tthis.setIndexs(arr)\r\n\t\t},\r\n\t\t// 点击工具栏的确定按钮\r\n\t\tconfirm() {\r\n\t\t\t// 如果用户有没有触发过change\r\n\t\t\tif (!this.currentActiveValue.length) {\r\n\t\t\t\tthis.setDefault()\r\n\t\t\t}\r\n            this.$emit('update:modelValue', this.inputValue)\r\n            if (this.hasInput) {\r\n                this.showByClickInput = false\r\n            }\r\n\t\t\tthis.setLastIndex(this.innerIndex)\r\n\t\t\tthis.$emit('update:show', false)\r\n\t\t\tthis.$emit('confirm', {\r\n\t\t\t\tindexs: this.innerIndex,\r\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[this.innerIndex[index]]),\r\n\t\t\t\tvalues: this.innerColumns\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 选择器某一列的数据发生变化时触发\r\n\t\tchangeHandler(e) {\r\n\t\t\tconst {\r\n\t\t\t\tvalue\r\n\t\t\t} = e.detail\r\n\t\t\tlet index = 0,\r\n\t\t\t\tcolumnIndex = 0\r\n\t\t\t//记录用户选中但是还没确认的值\r\n\t\t\tthis.currentActiveValue = value;\t\r\n\t\t\t// 通过对比前后两次的列索引，得出当前变化的是哪一列\r\n\t\t\tfor (let i = 0; i < value.length; i++) {\r\n\t\t\t\tlet item = value[i]\r\n\t\t\t\tif (item !== (this.lastIndex[i] || 0)) { // 把undefined转为合法假值0\r\n\t\t\t\t\t// 设置columnIndex为当前变化列的索引\r\n\t\t\t\t\tcolumnIndex = i\r\n\t\t\t\t\t// index则为变化列中的变化项的索引\r\n\t\t\t\t\tindex = item\r\n\t\t\t\t\tbreak // 终止循环，即使少一次循环，也是性能的提升\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.columnIndex = columnIndex\r\n\t\t\tconst values = this.innerColumns\r\n\t\t\t// 将当前的各项变化索引，设置为\"上一次\"的索引变化值\r\n\t\t\t// this.setLastIndex(value)\r\n\t\t\tthis.setIndexs(value)\r\n\t\t\t//如果是非自带输入框才会在change时候触发v-model绑值的变化\r\n\t\t\t//否则会非常的奇怪，用户未确认，值就变了\r\n\t\t\t// if (!this.hasInput) {\r\n\t\t\t// \tthis.$emit('update:modelValue', this.inputValue)\r\n\t\t\t// }\r\n\t\t\t\r\n\t\t\tthis.$emit('change', {\r\n\t\t\t\t// #ifndef MP-WEIXIN || MP-LARK\r\n\t\t\t\t// 微信小程序不能传递this，会因为循环引用而报错\r\n\t\t\t\t// picker: this,\r\n\t\t\t\t// #endif\r\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[value[index]]),\r\n\t\t\t\tindex,\r\n\t\t\t\tindexs: value,\r\n\t\t\t\t// values为当前变化列的数组内容\r\n\t\t\t\tvalues,\r\n\t\t\t\tcolumnIndex\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 设置index索引，此方法可被外部调用设置\r\n\t\tsetIndexs(index, setLastIndex) {\r\n\t\t\tthis.innerIndex = deepClone(index)\r\n\t\t\tif (setLastIndex) {\r\n\t\t\t\tthis.setLastIndex(index)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 记录上一次的各列索引位置\r\n\t\tsetLastIndex(index) {\r\n\t\t\t// 当能进入此方法，意味着当前设置的各列默认索引，即为“上一次”的选中值，需要记录，是因为changeHandler中\r\n\t\t\t// 需要拿前后的变化值进行对比，得出当前发生改变的是哪一列\r\n\t\t\tthis.lastIndex = deepClone(index)\r\n\t\t},\r\n\t\t// 设置对应列选项的所有值\r\n\t\tsetColumnValues(columnIndex, values) {\r\n\t\t\t// 替换innerColumns数组中columnIndex索引的值为values，使用的是数组的splice方法\r\n\t\t\tthis.innerColumns.splice(columnIndex, 1, values)\r\n            // 替换完成之后将修改列之后的已选值置空\r\n\t\t\tthis.setLastIndex(this.innerIndex.slice(0, columnIndex))\r\n\t\t\t// 拷贝一份原有的innerIndex做临时变量，将大于当前变化列的所有的列的默认索引设置为0\r\n\t\t\tlet tmpIndex = deepClone(this.innerIndex)\r\n\t\t\tfor (let i = 0; i < this.innerColumns.length; i++) {\r\n\t\t\t\tif (i > this.columnIndex) {\r\n\t\t\t\t\ttmpIndex[i] = 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 一次性赋值，不能单个修改，否则无效\r\n\t\t\tthis.setIndexs(tmpIndex)\r\n\t\t},\r\n\t\t// 获取对应列的所有选项\r\n\t\tgetColumnValues(columnIndex) {\r\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\r\n\t\t\t// 索引如果在外部change的回调中调用getColumnValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\r\n\t\t\t(async () => {\r\n\t\t\t\tawait sleep()\r\n\t\t\t})()\r\n\t\t\treturn this.innerColumns[columnIndex]\r\n\t\t},\r\n\t\t// 设置整体各列的columns的值\r\n\t\tsetColumns(columns) {\r\n\t\t\t// console.log(columns)\r\n\t\t\tthis.innerColumns = deepClone(columns)\r\n\t\t\t// 如果在设置各列数据时，没有被设置默认的各列索引defaultIndex，那么用0去填充它，数组长度为列的数量\r\n\t\t\tif (this.innerIndex.length === 0) {\r\n\t\t\t\tthis.innerIndex = new Array(columns.length).fill(0)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取各列选中值对应的索引\r\n\t\tgetIndexs() {\r\n\t\t\treturn this.innerIndex\r\n\t\t},\r\n\t\t// 获取各列选中的值\r\n\t\tgetValues() {\r\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\r\n\t\t\t// 索引如果在外部change的回调中调用getValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\r\n\t\t\t(async () => {\r\n\t\t\t\tawait sleep()\r\n\t\t\t})()\r\n\t\t\treturn this.innerColumns.map((item, index) => item[this.innerIndex[index]])\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-picker {\r\n\t\tposition: relative;\r\n\t\t&-input {\r\n\t\t\tposition: relative;\r\n\t\t\t.input-cover {\r\n\t\t\t\topacity: 0;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tz-index:1;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&__view {\r\n\r\n\t\t\t&__column {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t&__item {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tcolor: $u-main-color;\r\n\r\n\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\topacity: 0.35;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&--loading {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\t@include flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: rgba(255, 255, 255, 0.87);\r\n\t\t\tz-index: 1000;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-picker/u-picker.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "test", "deepClone", "index", "sleep"], "mappings": ";;;;;;;AAoHA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,oDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,WAAW,CAAE;AAAA;AAAA,MAEb,YAAY,CAAE;AAAA;AAAA,MAEd,cAAc,CAAE;AAAA;AAAA,MAEhB,aAAa;AAAA,MACJ,kBAAkB;AAAA,MAC3B,oBAAoB,CAAG;AAAA;AAAA,IACxB;AAAA,EACA;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,SAAS;AAAA,MACR,WAAW;AAAA,MACX,MAAK;AAAA,MACL,QAAQ,GAAG;AACV,aAAK,WAAW,CAAC;AAAA,MAClB;AAAA,IACA;AAAA;AAAA,IAED,cAAc;AAAA,MACb,WAAW;AAAA,MACX,MAAK;AAAA,MACL,QAAQ,GAAE,GAAG;AAIZ,YAAI,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,GAAG;AACrC,eAAK,UAAU,GAAG,IAAI;AAAA,QACvB;AAAA,MACD;AAAA,IACA;AAAA,IACD,YAAY;AAAA,MACX,WAAW;AAAA,MACX,MAAK;AAAA,MACL,QAAQ,GAAE,GAAG;AAIZ,YAAI,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,GAAG;AACrC,cAAI,MAAM,CAAA;AACV,cAAI,KAAK,MAAM;AACd,cAAE,QAAQ,CAAC,SAAS,UAAU;AAC7B,kBAAI,cAAc,KAAK,gBAAgB,KAAK;AAC5C,kBAAI,eAAe,OAAO,UAAU,SAAS,KAAK,WAAW,MAAM,mBAAmB;AACrF,4BAAY,QAAQ,CAAC,MAAM,WAAW;AACrC,sBAAI,KAAK,KAAK,OAAO,KAAK,SAAS;AAClC,wBAAI,KAAK,MAAM;AAAA,kBAChB;AAAA,iBACA;AAAA,qBACK;AACN,4BAAY,QAAQ,CAAC,MAAM,WAAW;AACrC,sBAAI,QAAQ,SAAS;AACpB,wBAAI,KAAK,MAAM;AAAA,kBAChB;AAAA,iBACA;AAAA,cACF;AAAA,YACD,CAAC;AAED,gBAAI,IAAI,UAAU,KAAK,KAAK;AAAc;AAAA,iBACnC;AACN,mBAAK,UAAU,KAAK,IAAI;AAAA,YACzB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO,CAAC,SAAS,UAAU,WAAW,UAAU,qBAAqB,aAAa;AAAA,EAC/E,UAAU;AAAA;AAAA,IAEZ,kBAAkB;AACjB,aAAO;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,aAAa,KAAK;AAAA,QAClB,UAAU,KAAK;AAAA,QACf,eAAe,KAAK;AAAA,QACpB,GAAG,KAAK;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAED,aAAa;AACZ,UAAI,YAAY,KAAK,aAAa,CAAC,KAAK,KAAK,aAAa,CAAC,EAAE,CAAC;AAE9D,UAAI,aAAa,OAAO,UAAU,SAAS,KAAK,SAAS,MAAM,mBAAmB;AACjF,YAAI,MAAM,KAAK,aAAa,CAAC,EAAE,OAAO,UAAQ,KAAK,WAAW,SAAS,KAAK,IAAI,CAAC,CAAC;AAClF,cAAM,IAAI,IAAI,UAAQ,KAAK,KAAK,OAAO,CAAC;AACxC,eAAO,IAAI,KAAK,GAAG;AAAA,aAEb;AAEN,eAAO,KAAK,WAAW,KAAK,GAAG;AAAA,MAChC;AAAA,IACA;AAAA;AAAA,IAED,aAAa;AACZ,UAAI,QAAQ,KAAK,aAAa,IAAI,CAAC,MAAM,UAAU,KAAK,KAAK,WAAW,KAAK,CAAC,CAAC;AAC/E,UAAI,MAAM,CAAC;AAEX,UAAI,MAAM,CAAC,KAAK,OAAO,UAAU,SAAS,KAAK,MAAM,CAAC,CAAC,MAAM,mBAAmB;AAE/E,cAAM,QAAQ,aAAW;AACxB,cAAI,KAAK,WAAW,QAAQ,KAAK,SAAS,CAAC;AAAA,QAC5C,CAAC;AAAA,aACK;AAEN,cAAM,QAAQ,CAAC,SAAS,UAAU;AACjC,cAAI,KAAK,OAAO;AAAA,QACjB,CAAC;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAAA,EACG;AAAA,EACJ,SAAS;AAAA,IACR,SAAAC,0CAAO;AAAA,IACP,WAAWC,yCAAI,KAAC;AAAA,IAChB,qBAAoB;AACnB,UAAG,CAAC,KAAK,UAAS;AACjB,aAAK,mBAAiB,CAAC,KAAK;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAED,YAAY,MAAM;AACjB,UAAIA,yCAAI,KAAC,OAAO,IAAI,GAAG;AACtB,eAAO,KAAK,KAAK,OAAO;AAAA,aAClB;AACN,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAED,eAAe;AACd,UAAI,KAAK,qBAAqB;AACjB,YAAI,KAAK,UAAU;AACf,eAAK,mBAAmB;AAAA,QAC5B;AACZ,aAAK,WAAW;AAChB,aAAK,MAAM,eAAe,KAAK;AAC/B,aAAK,MAAM,OAAO;AAAA,MACnB;AAAA,IACA;AAAA;AAAA,IAED,SAAS;AACC,UAAI,KAAK,UAAU;AACf,aAAK,mBAAmB;AAAA,MAC5B;AACT,WAAK,WAAW;AAChB,WAAK,MAAM,eAAe,KAAK;AAC/B,WAAK,MAAM,QAAQ;AAAA,IACnB;AAAA,IACD,aAAa;AACZ,UAAI,MAAM,CAAC,CAAC;AACZ,UAAI,KAAK,UAAU,UAAU,GAAG;AAE/B,YAAI,MAAM,QAAQ,KAAK,YAAY,KAAK,KAAK,aAAa,UAAU,KAAK,aAAa,QAAQ;AAC7F,gBAAM,CAAC,GAAG,KAAK,YAAY;AAAA,eACrB;AAEN,gBAAM,MAAM,KAAK,aAAa,MAAM,EAAE,KAAK,CAAC;AAAA,QAC7C;AAAA,aACM;AACN,cAAMC,0CAAAA,UAAU,KAAK,SAAS;AAAA,MAC/B;AACA,WAAK,aAAa,GAAG;AACrB,WAAK,UAAU,GAAG;AAAA,IAClB;AAAA;AAAA,IAED,UAAU;AAET,UAAI,CAAC,KAAK,mBAAmB,QAAQ;AACpC,aAAK,WAAW;AAAA,MACjB;AACS,WAAK,MAAM,qBAAqB,KAAK,UAAU;AAC/C,UAAI,KAAK,UAAU;AACf,aAAK,mBAAmB;AAAA,MAC5B;AACT,WAAK,aAAa,KAAK,UAAU;AACjC,WAAK,MAAM,eAAe,KAAK;AAC/B,WAAK,MAAM,WAAW;AAAA,QACrB,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK,aAAa,IAAI,CAAC,MAAM,UAAU,KAAK,KAAK,WAAW,KAAK,CAAC,CAAC;AAAA,QAC1E,QAAQ,KAAK;AAAA,OACb;AAAA,IACD;AAAA;AAAA,IAED,cAAc,GAAG;AAChB,YAAM;AAAA,QACL;AAAA,MACD,IAAI,EAAE;AACN,UAAI,QAAQ,GACX,cAAc;AAEf,WAAK,qBAAqB;AAE1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI;AAEtC,wBAAc;AAEd,kBAAQ;AACR;AAAA,QACD;AAAA,MACD;AACA,WAAK,cAAc;AACnB,YAAM,SAAS,KAAK;AAGpB,WAAK,UAAU,KAAK;AAOpB,WAAK,MAAM,UAAU;AAAA,QAKpB,OAAO,KAAK,aAAa,IAAI,CAAC,MAAMC,WAAU,KAAK,MAAMA,MAAK,CAAC,CAAC;AAAA,QAChE;AAAA,QACA,QAAQ;AAAA;AAAA,QAER;AAAA,QACA;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAED,UAAU,OAAO,cAAc;AAC9B,WAAK,aAAaD,0CAAS,UAAC,KAAK;AACjC,UAAI,cAAc;AACjB,aAAK,aAAa,KAAK;AAAA,MACxB;AAAA,IACA;AAAA;AAAA,IAED,aAAa,OAAO;AAGnB,WAAK,YAAYA,0CAAS,UAAC,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB,aAAa,QAAQ;AAEpC,WAAK,aAAa,OAAO,aAAa,GAAG,MAAM;AAE/C,WAAK,aAAa,KAAK,WAAW,MAAM,GAAG,WAAW,CAAC;AAEvD,UAAI,WAAWA,0CAAAA,UAAU,KAAK,UAAU;AACxC,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAClD,YAAI,IAAI,KAAK,aAAa;AACzB,mBAAS,CAAC,IAAI;AAAA,QACf;AAAA,MACD;AAEA,WAAK,UAAU,QAAQ;AAAA,IACvB;AAAA;AAAA,IAED,gBAAgB,aAAa;AAG5B,OAAC,YAAY;AACZ,cAAME,gDAAM;AAAA,MACb,GAAG;AACH,aAAO,KAAK,aAAa,WAAW;AAAA,IACpC;AAAA;AAAA,IAED,WAAW,SAAS;AAEnB,WAAK,eAAeF,0CAAS,UAAC,OAAO;AAErC,UAAI,KAAK,WAAW,WAAW,GAAG;AACjC,aAAK,aAAa,IAAI,MAAM,QAAQ,MAAM,EAAE,KAAK,CAAC;AAAA,MACnD;AAAA,IACA;AAAA;AAAA,IAED,YAAY;AACX,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAED,YAAY;AAGX,OAAC,YAAY;AACZ,cAAME,gDAAM;AAAA,MACb,GAAG;AACH,aAAO,KAAK,aAAa,IAAI,CAAC,MAAM,UAAU,KAAK,KAAK,WAAW,KAAK,CAAC,CAAC;AAAA,IAC3E;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1ZA,GAAG,gBAAgB,SAAS;"}