{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-overlay/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否显示遮罩\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.overlay.show\r\n        },\r\n        // 层级z-index\r\n        zIndex: {\r\n            type: [String, Number],\r\n            default: () => defProps.overlay.zIndex\r\n        },\r\n        // 遮罩的过渡时间，单位为ms\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.overlay.duration\r\n        },\r\n        // 不透明度值，当做rgba的第四个参数\r\n        opacity: {\r\n            type: [String, Number],\r\n            default: () => defProps.overlay.opacity\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA,EACJ;AACL,CAAC;;"}