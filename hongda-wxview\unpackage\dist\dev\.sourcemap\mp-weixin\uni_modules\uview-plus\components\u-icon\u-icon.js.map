{"version": 3, "file": "u-icon.js", "sources": ["uni_modules/uview-plus/components/u-icon/u-icon.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWljb24vdS1pY29uLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t    class=\"u-icon\"\r\n\t    @tap=\"clickHandler\"\r\n\t    :class=\"['u-icon--' + labelPos]\"\r\n\t>\r\n\t\t<image\r\n\t\t    class=\"u-icon__img\"\r\n\t\t    v-if=\"isImg\"\r\n\t\t    :src=\"name\"\r\n\t\t    :mode=\"imgMode\"\r\n\t\t    :style=\"[imgStyle, addStyle(customStyle)]\"\r\n\t\t></image>\r\n\t\t<text\r\n\t\t    v-else\r\n\t\t    class=\"u-icon__icon\"\r\n\t\t    :class=\"uClasses\"\r\n\t\t    :style=\"[iconStyle, addStyle(customStyle)]\"\r\n\t\t    :hover-class=\"hoverClass\"\r\n\t\t>{{icon}}</text>\r\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\r\n\t\t<text\r\n\t\t    v-if=\"label !== ''\" \r\n\t\t    class=\"u-icon__label\"\r\n\t\t    :style=\"{\r\n\t\t\tcolor: labelColor,\r\n\t\t\tfontSize: addUnit(labelSize),\r\n\t\t\tmarginLeft: labelPos == 'right' ? addUnit(space) : 0,\r\n\t\t\tmarginTop: labelPos == 'bottom' ? addUnit(space) : 0,\r\n\t\t\tmarginRight: labelPos == 'left' ? addUnit(space) : 0,\r\n\t\t\tmarginBottom: labelPos == 'top' ? addUnit(space) : 0,\r\n\t\t}\"\r\n\t\t>{{ label }}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 引入图标名称，已经对应的unicode\r\n\timport icons from './icons';\r\n\timport { props } from './props';\r\n\timport config from '../../libs/config/config';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle } from '../../libs/function/index';\r\n\timport fontUtil from './util';\r\n\t/**\r\n\t * icon 图标\r\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/icon.html\r\n\t * @property {String}\t\t\tname\t\t\t图标名称，见示例图标集\r\n\t * @property {String}\t\t\tcolor\t\t\t图标颜色,可接受主题色 （默认 color['u-content-color'] ）\r\n\t * @property {String | Number}\tsize\t\t\t图标字体大小，单位px （默认 '16px' ）\r\n\t * @property {Boolean}\t\t\tbold\t\t\t是否显示粗体 （默认 false ）\r\n\t * @property {String | Number}\tindex\t\t\t点击图标的时候传递事件出去的index（用于区分点击了哪一个）\r\n\t * @property {String}\t\t\thoverClass\t\t图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网\r\n\t * @property {String}\t\t\tcustomPrefix\t自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）\r\n\t * @property {String | Number}\tlabel\t\t\t图标右侧的label文字\r\n\t * @property {String}\t\t\tlabelPos\t\tlabel相对于图标的位置，只能right或bottom （默认 'right' ）\r\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px （默认 '15px' ）\r\n\t * @property {String}\t\t\tlabelColor\t\t图标右侧的label文字颜色 （ 默认 color['u-content-color'] ）\r\n\t * @property {String | Number}\tspace\t\t\tlabel与图标的距离，单位px （默认 '3px' ）\r\n\t * @property {String}\t\t\timgMode\t\t\t图片的mode\r\n\t * @property {String | Number}\twidth\t\t\t显示图片小图标时的宽度\r\n\t * @property {String | Number}\theight\t\t\t显示图片小图标时的高度\r\n\t * @property {String | Number}\ttop\t\t\t\t图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）\r\n\t * @property {Boolean}\t\t\tstop\t\t\t是否阻止事件传播 （默认 false ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\ticon的样式，对象形式\r\n\t * @pages_event {Function} click 点击图标时触发\r\n\t * @pages_event {Function} touchstart 事件触摸时触发\r\n\t * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-icon',\r\n\t\tbeforeCreate() {\r\n\t\t\tfontUtil.loadFont();\r\n    \t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: ['click'],\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tcomputed: {\r\n\t\t\tuClasses() {\r\n\t\t\t\tlet classes = []\r\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\r\n\t\t\t\t// uview-plus内置图标类名为u-iconfont\r\n\t\t\t\tif (this.customPrefix == 'uicon') {\r\n\t\t\t\t\tclasses.push('u-iconfont')\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 不能缺少这一步，否则自定义图标会无效\r\n\t\t\t\t\tclasses.push(this.customPrefix)\r\n\t\t\t\t}\r\n\t\t\t\t// 主题色，通过类配置\r\n\t\t\t\tif (this.color && config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\r\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\r\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\r\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\r\n\t\t\t\tclasses = classes.join(' ')\r\n\t\t\t\t//#endif\r\n\t\t\t\treturn classes\r\n\t\t\t},\r\n\t\t\ticonStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle = {\r\n\t\t\t\t\tfontSize: addUnit(this.size),\r\n\t\t\t\t\tlineHeight: addUnit(this.size),\r\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\r\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\r\n\t\t\t\t\ttop: addUnit(this.top)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.customPrefix !== 'uicon') {\r\n\t\t\t\t\tstyle.fontFamily = this.customPrefix\r\n\t\t\t\t}\r\n\t\t\t\t// 非主题色值时，才当作颜色值\r\n\t\t\t\tif (this.color && !config.type.includes(this.color)) style.color = this.color\r\n\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\r\n\t\t\tisImg() {\r\n\t\t\t\treturn this.name.indexOf('/') !== -1\r\n\t\t\t},\r\n\t\t\timgStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\r\n\t\t\t\tstyle.width = this.width ? addUnit(this.width) : addUnit(this.size)\r\n\t\t\t\tstyle.height = this.height ? addUnit(this.height) : addUnit(this.size)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 通过图标名，查找对应的图标\r\n\t\t\ticon() {\r\n\t\t\t\t// 使用自定义图标的时候页面上会把name属性也展示出来，所以在这里处理一下\r\n\t\t\t\tif (this.customPrefix !== \"uicon\") {\r\n\t\t\t\t\treturn config.customIcons[this.name] || this.name;\r\n\t\t\t\t}\r\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\r\n\t\t\t\treturn icons['uicon-' + this.name] || this.name\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\tclickHandler(e) {\r\n\t\t\t\tthis.$emit('click', this.index, e)\r\n\t\t\t\t// 是否阻止事件冒泡\r\n\t\t\t\tthis.stop && this.preventEvent(e)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t// 变量定义\r\n\t$u-icon-primary: $u-primary !default;\r\n\t$u-icon-success: $u-success !default;\r\n\t$u-icon-info: $u-info !default;\r\n\t$u-icon-warning: $u-warning !default;\r\n\t$u-icon-error: $u-error !default;\r\n\t$u-icon-label-line-height:1 !default;\r\n\r\n\t/* #ifdef MP-QQ || MP-TOUTIAO || MP-BAIDU || MP-KUAISHOU || MP-XHS */\r\n\t// 2025/04/09在App/微信/支付宝/鸿蒙元服务已改用uni.loadFontFace加载字体\r\n\t@font-face {\r\n\t\tfont-family: 'uicon-iconfont';\r\n\t\tsrc: url('https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf') format('truetype');\r\n\t}\r\n\t/* #endif */\r\n\r\n\t.u-icon {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\r\n\t\t&--left {\r\n\t\t\tflex-direction: row-reverse;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&--right {\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&--top {\r\n\t\t\tflex-direction: column-reverse;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t&--bottom {\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t&__icon {\r\n\t\t\tfont-family: uicon-iconfont;\r\n\t\t\tposition: relative;\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&--primary {\r\n\t\t\t\tcolor: $u-icon-primary;\r\n\t\t\t}\r\n\r\n\t\t\t&--success {\r\n\t\t\t\tcolor: $u-icon-success;\r\n\t\t\t}\r\n\r\n\t\t\t&--error {\r\n\t\t\t\tcolor: $u-icon-error;\r\n\t\t\t}\r\n\r\n\t\t\t&--warning {\r\n\t\t\t\tcolor: $u-icon-warning;\r\n\t\t\t}\r\n\r\n\t\t\t&--info {\r\n\t\t\t\tcolor: $u-icon-info;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__img {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\theight: auto;\r\n\t\t\twill-change: transform;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t&__label {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tline-height: $u-icon-label-line-height;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-icon/u-icon.vue'\nwx.createComponent(Component)"], "names": ["fontUtil", "mpMixin", "mixin", "props", "config", "addUnit", "icons", "addStyle"], "mappings": ";;;;;;;;;AAuEC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,eAAe;AACdA,gDAAQ,SAAC,SAAQ;AAAA,EACd;AAAA,EACJ,OAAO;AACN,WAAO,CACP;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,QAAQ,CAACC,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,WAAW;AACV,UAAI,UAAU,CAAC;AACf,cAAQ,KAAK,KAAK,eAAe,MAAM,KAAK,IAAI;AAEhD,UAAI,KAAK,gBAAgB,SAAS;AACjC,gBAAQ,KAAK,YAAY;AAAA,aACnB;AAEN,gBAAQ,KAAK,KAAK,YAAY;AAAA,MAC/B;AAEA,UAAI,KAAK,SAASC,gDAAO,KAAK,SAAS,KAAK,KAAK;AAAG,gBAAQ,KAAK,mBAAmB,KAAK,KAAK;AAM9F,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,UAAI,QAAQ,CAAC;AACb,cAAQ;AAAA,QACP,UAAUC,0CAAAA,QAAQ,KAAK,IAAI;AAAA,QAC3B,YAAYA,0CAAAA,QAAQ,KAAK,IAAI;AAAA,QAC7B,YAAY,KAAK,OAAO,SAAS;AAAA;AAAA,QAEjC,KAAKA,0CAAAA,QAAQ,KAAK,GAAG;AAAA,MACtB;AACA,UAAI,KAAK,iBAAiB,SAAS;AAClC,cAAM,aAAa,KAAK;AAAA,MACzB;AAEA,UAAI,KAAK,SAAS,CAACD,yCAAAA,OAAO,KAAK,SAAS,KAAK,KAAK;AAAG,cAAM,QAAQ,KAAK;AAExE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,KAAK,QAAQ,GAAG,MAAM;AAAA,IAClC;AAAA,IACD,WAAW;AACV,UAAI,QAAQ,CAAC;AAEb,YAAM,QAAQ,KAAK,QAAQC,0CAAO,QAAC,KAAK,KAAK,IAAIA,0CAAAA,QAAQ,KAAK,IAAI;AAClE,YAAM,SAAS,KAAK,SAASA,0CAAO,QAAC,KAAK,MAAM,IAAIA,0CAAAA,QAAQ,KAAK,IAAI;AACrE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,OAAO;AAEN,UAAI,KAAK,iBAAiB,SAAS;AAClC,eAAOD,yCAAAA,OAAO,YAAY,KAAK,IAAI,KAAK,KAAK;AAAA,MAC9C;AAEA,aAAOE,6CAAAA,MAAM,WAAW,KAAK,IAAI,KAAK,KAAK;AAAA,IAC5C;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAF,0CAAO;AAAA,IACP,aAAa,GAAG;AACf,WAAK,MAAM,SAAS,KAAK,OAAO,CAAC;AAEjC,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IACjC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJD,GAAG,gBAAgB,SAAS;"}