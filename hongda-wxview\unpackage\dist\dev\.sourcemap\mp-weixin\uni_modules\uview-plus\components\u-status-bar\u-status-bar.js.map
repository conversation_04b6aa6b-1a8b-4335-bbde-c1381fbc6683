{"version": 3, "file": "u-status-bar.js", "sources": ["uni_modules/uview-plus/components/u-status-bar/u-status-bar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN0YXR1cy1iYXIvdS1zdGF0dXMtYmFyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t    :style=\"[style]\"\r\n\t    class=\"u-status-bar\"\r\n\t\t:class=\"[isH5 && 'u-safe-area-inset-top']\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle, deepMerge, getWindowInfo } from '../../libs/function/index';\r\n\t/**\r\n\t * StatbusBar 状态栏占位\r\n\t * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。\r\n\t * @tutorial https://uview-plus.jiangruyi.com/components/statusBar.html\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景色 (默认 'transparent' )\r\n\t * @property {String | Object}\tcustomStyle\t\t自定义样式 \r\n\t * @example <u-status-bar></u-status-bar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-status-bar',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisH5: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.isH5 = true\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\temits: ['update:height'],\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\t// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式\r\n\t\t\t\tlet sheight = getWindowInfo().statusBarHeight\r\n\t\t\t\tthis.$emit('update:height', sheight)\r\n\t\t\t\tif (sheight == 0) {\r\n\t\t\t\t\tthis.isH5 = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstyle.height = addUnit(sheight, 'px')\r\n\t\t\t\t}\r\n\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.u-status-bar {\r\n\t\t// nvue会默认100%，如果nvue下，显式写100%的话，会导致宽度不为100%而异常\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-status-bar/u-status-bar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "getWindowInfo", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AAuBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,uDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EACD,UAAU;AAAA,EAIT;AAAA,EACD,OAAO,CAAC,eAAe;AAAA,EACvB,UAAU;AAAA,IACT,QAAQ;AACP,YAAM,QAAQ,CAAC;AAEf,UAAI,UAAUC,0CAAa,cAAA,EAAG;AAC9B,WAAK,MAAM,iBAAiB,OAAO;AACnC,UAAI,WAAW,GAAG;AACjB,aAAK,OAAO;AAAA,aACN;AACN,cAAM,SAASC,kDAAQ,SAAS,IAAI;AAAA,MACrC;AACA,YAAM,kBAAkB,KAAK;AAC7B,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AACF;;;;;;;;ACnDD,GAAG,gBAAgB,SAAS;"}