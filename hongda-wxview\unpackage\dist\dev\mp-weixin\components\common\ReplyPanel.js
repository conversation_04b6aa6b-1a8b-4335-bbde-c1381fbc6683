"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  _easycom_u_popup2();
}
const _easycom_u_popup = () => "../../uni_modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  _easycom_u_popup();
}
const _sfc_main = {
  __name: "ReplyPanel",
  props: {
    // 控制弹窗显示
    show: {
      type: Boolean,
      default: false
    },
    // 被回复的目标评论对象
    replyTarget: {
      type: Object,
      default: null
    },
    // 外部控制的提交状态
    isSubmitting: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:show", "close", "submit"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const content = common_vendor.ref("");
    common_vendor.watch(() => props.show, (newVal) => {
      if (!newVal) {
        content.value = "";
      }
    });
    const canSubmit = common_vendor.computed(() => content.value.trim().length > 0);
    const handleClose = () => {
      emit("close");
    };
    const handleSubmit = () => {
      if (!canSubmit.value || props.isSubmitting)
        return;
      emit("submit", content.value);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.replyTarget
      }, __props.replyTarget ? {
        b: common_vendor.t(__props.replyTarget.nickname)
      } : {}, {
        c: common_vendor.o(handleClose),
        d: __props.show,
        e: content.value,
        f: common_vendor.o(($event) => content.value = $event.detail.value),
        g: common_vendor.t(content.value.length),
        h: common_vendor.t(__props.isSubmitting ? "发送中" : "发送"),
        i: canSubmit.value ? 1 : "",
        j: !canSubmit.value || __props.isSubmitting,
        k: __props.isSubmitting,
        l: common_vendor.o(handleSubmit),
        m: common_vendor.o(handleClose),
        n: common_vendor.p({
          show: __props.show,
          mode: "bottom",
          round: "20",
          ["safe-area-inset-bottom"]: true
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0a64a93b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/ReplyPanel.js.map
