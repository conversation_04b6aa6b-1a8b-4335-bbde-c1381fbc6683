{"version": 3, "file": "EventCard.js", "sources": ["components/event/EventCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"event-card\" @click=\"$emit('click', event)\">\n    <view class=\"card-left\">\n      <image :src=\"getFullImageUrl(event.coverImageUrl)\" mode=\"aspectFill\" class=\"event-image\" :lazy-load=\"true\"></image>\n      <view v-if=\"Number(event.status) === 1 || Number(event.status) === 2\" :class=\"['status-tag', getStatusClass(event.status)]\">\n        {{ formatEventStatus(event.status) }}\n      </view>\n    </view>\n\n    <view class=\"card-right\">\n      <text class=\"event-title\">{{ event.title }}</text>\n\n      <view class=\"event-info-row\">\n        <view class=\"time-location-item\">\n          <image class=\"event-info-icon\" :src=\"listTimeIconUrl\" mode=\"aspectFit\"></image>\n          <text class=\"info-text\">{{ formatEventDate(event.startTime) }}</text>\n        </view>\n        <view class=\"time-location-item\">\n          <image class=\"event-info-icon\" :src=\"listLocationIconUrl\" mode=\"aspectFit\"></image>\n          <text class=\"info-text\">{{ formatEventLocation(event) }}</text>\n        </view>\n      </view>\n\n      <view class=\"event-info remaining-spots\">\n        <text class=\"spots-count\">\n          剩余名额: {{ calculateRemainingSpots(event.maxParticipants, event.registeredCount) }}\n        </text>\n      </view>\n    </view>\n  </view>\n  \n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js'\nimport { formatEventDate } from '@/utils/date.js'\nimport { getFullImageUrl } from '@/utils/image.js'\n\nconst props = defineProps({\n  event: { type: Object, required: true }\n})\n\n// 静态资源 URL（不再使用本地兜底）\nconst listTimeIconUrl = ref('')\nconst listLocationIconUrl = ref('')\n\n// 组件挂载时读取静态资源配置\nonMounted(() => {\n  const assets = uni.getStorageSync('staticAssets')\n  \n  listTimeIconUrl.value = assets?.list_time || ''\n  listLocationIconUrl.value = assets?.list_location || ''\n})\n\nconst formatEventLocation = (event) => {\n  if (event.city && event.city.trim()) {\n    return event.city.trim().replace(/市$/, '')\n  }\n  return '待定'\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.event-card {\n  width: 100%;\n  height: 272rpx;\n  background: #FFFFFF;\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\n  border: none;\n  border-top: 2rpx solid #EEEEEE;\n  border-bottom: 2rpx solid #EEEEEE;\n  margin-bottom: 0rpx;\n  padding: 24rpx 24rpx;\n  display: flex;\n  overflow: hidden;\n  box-sizing: border-box;\n}\n\n.card-left {\n  position: relative;\n  width: 336rpx;\n  height: 192rpx;\n  flex-shrink: 0;\n  margin-top: 16rpx;\n  margin-bottom: 16rpx; \n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.event-image {\n  width: 100%;\n  height: 100%;\n  display: block;\n  border-radius: 16rpx;\n}\n\n.status-tag {\n  position: absolute;\n  top: 12rpx;\n  left: 12rpx;\n  width: 90rpx;\n  height: 40rpx;\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n  border-radius: 20rpx 20rpx 20rpx 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n\n  color: #23232A;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 22rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n\n  &.ended {\n    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\n  }\n}\n\n.card-right {\n  flex: 1;\n  padding: 16rpx 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.event-title {\n  width: 346rpx;\n  height: 80rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #23232A;\n  text-align: justify;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1.4;\n  margin-bottom: 24rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.event-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.event-info-row {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: flex-start !important;\n  gap: 24rpx !important;\n  margin-bottom: 18rpx !important;\n  flex-wrap: nowrap !important;\n}\n\n.time-location-item {\n  display: flex !important;\n  align-items: center !important;\n  gap: 8rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.event-info-icon {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.info-text {\n  width: 176rpx !important;\n  height: 32rpx !important;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\n  font-weight: normal !important;\n  font-size: 22rpx !important;\n  color: #9B9A9A !important;\n  text-align: left !important;\n  font-style: normal !important;\n  text-transform: none !important;\n  line-height: 32rpx !important;\n  overflow: hidden !important;\n  text-overflow: ellipsis !important;\n  white-space: nowrap !important;\n}\n\n.remaining-spots {\n  width: 154rpx;\n  height: 40rpx;\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\n  border: 1rpx solid #FB8620;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n  box-sizing: border-box;\n  overflow: hidden;\n  flex-shrink: 0;\n\n  .spots-count {\n    width: 100%;\n    height: 36rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 20rpx;\n    color: #FB8620;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n    line-height: 36rpx;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n}\n</style>\n\n\n\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventCard.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;AA4CA,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAC9B,UAAM,sBAAsBA,cAAG,IAAC,EAAE;AAGlCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,sBAAgB,SAAQ,iCAAQ,cAAa;AAC7C,0BAAoB,SAAQ,iCAAQ,kBAAiB;AAAA,IACvD,CAAC;AAED,UAAM,sBAAsB,CAAC,UAAU;AACrC,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAI,GAAI;AACnC,eAAO,MAAM,KAAK,KAAM,EAAC,QAAQ,MAAM,EAAE;AAAA,MAC1C;AACD,aAAO;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;AC3DA,GAAG,gBAAgB,SAAS;"}