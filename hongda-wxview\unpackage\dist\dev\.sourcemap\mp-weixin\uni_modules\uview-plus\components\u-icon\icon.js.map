{"version": 3, "file": "icon.js", "sources": ["uni_modules/uview-plus/components/u-icon/icon.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 18:00:14\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/icon.js\r\n */\r\nimport config from '../../libs/config/config'\r\n\r\nconst {\r\n    color\r\n} = config\r\nexport default {\r\n    // icon组件\r\n    icon: {\r\n        name: '',\r\n        color: color['u-content-color'],\r\n        size: '16px',\r\n        bold: false,\r\n        index: '',\r\n        hoverClass: '',\r\n        customPrefix: 'uicon',\r\n        label: '',\r\n        labelPos: 'right',\r\n        labelSize: '15px',\r\n        labelColor: color['u-content-color'],\r\n        space: '3px',\r\n        imgMode: '',\r\n        width: '',\r\n        height: '',\r\n        top: 0,\r\n        stop: false\r\n    }\r\n}\r\n"], "names": ["config"], "mappings": ";;AAWA,MAAM;AAAA,EACF;AACJ,IAAIA,yCAAM;AACV,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,MAAM;AAAA,IACN,OAAO,MAAM,iBAAiB;AAAA,IAC9B,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY,MAAM,iBAAiB;AAAA,IACnC,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,EACT;AACL;;"}