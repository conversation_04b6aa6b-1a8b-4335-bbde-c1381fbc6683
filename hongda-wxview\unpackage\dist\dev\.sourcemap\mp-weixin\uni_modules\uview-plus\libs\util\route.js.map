{"version": 3, "file": "route.js", "sources": ["uni_modules/uview-plus/libs/util/route.js"], "sourcesContent": ["/**\r\n * 路由跳转方法，该方法相对于直接使用uni.xxx的好处是使用更加简单快捷\r\n * 并且带有路由拦截功能\r\n */\r\nimport { queryParams, deepMerge, page } from '../function/index';\r\nclass Router {\r\n    constructor() {\r\n        // 原始属性定义\r\n        this.config = {\r\n            type: 'navigateTo',\r\n            url: '',\r\n            delta: 1, // navigateBack页面后退时,回退的层数\r\n            params: {}, // 传递的参数\r\n            animationType: 'pop-in', // 窗口动画,只在APP有效\r\n            animationDuration: 300, // 窗口动画持续时间,单位毫秒,只在APP有效\r\n            intercept: false // 是否需要拦截\r\n        }\r\n        // 因为route方法是需要对外赋值给另外的对象使用，同时route内部有使用this，会导致route失去上下文\r\n        // 这里在构造函数中进行this绑定\r\n        this.route = this.route.bind(this)\r\n    }\r\n\r\n    // 判断url前面是否有\"/\"，如果没有则加上，否则无法跳转\r\n    addRootPath(url) {\r\n        return url[0] === '/' ? url : `/${url}`\r\n    }\r\n\r\n    // 整合路由参数\r\n    mixinParam(url, params) {\r\n        url = url && this.addRootPath(url)\r\n\r\n        // 使用正则匹配，主要依据是判断是否有\"/\",\"?\",\"=\"等，如“/page/index/index?name=mary\"\r\n        // 如果有url中有get参数，转换后无需带上\"?\"\r\n        let query = ''\r\n        if (/.*\\/.*\\?.*=.*/.test(url)) {\r\n            // object对象转为get类型的参数\r\n            query = queryParams(params, false)\r\n            // 因为已有get参数,所以后面拼接的参数需要带上\"&\"隔开\r\n            return url += `&${query}`\r\n        }\r\n        // 直接拼接参数，因为此处url中没有后面的query参数，也就没有\"?/&\"之类的符号\r\n        query = queryParams(params)\r\n        return url += query\r\n    }\r\n\r\n    // 对外的方法名称\r\n    async route(options = {}, params = {}) {\r\n        // 合并用户的配置和内部的默认配置\r\n        let mergeConfig = {}\r\n\r\n        if (typeof options === 'string') {\r\n            // 如果options为字符串，则为route(url, params)的形式\r\n            mergeConfig.url = this.mixinParam(options, params)\r\n            mergeConfig.type = 'navigateTo'\r\n        } else {\r\n            mergeConfig = deepMerge(this.config, options)\r\n            // 否则正常使用mergeConfig中的url和params进行拼接\r\n            mergeConfig.url = this.mixinParam(options.url, options.params)\r\n        }\r\n\r\n        // 如果本次跳转的路径和本页面路径一致，不执行跳转，防止用户快速点击跳转按钮，造成多次跳转同一个页面的问题\r\n        if (mergeConfig.url === page()) return\r\n\r\n        if (params.intercept) {\r\n            this.config.intercept = params.intercept\r\n        }\r\n        // params参数也带给拦截器\r\n        mergeConfig.params = params\r\n        // 合并内外部参数\r\n        mergeConfig = deepMerge(this.config, mergeConfig)\r\n        // 判断用户是否定义了拦截器\r\n        if (typeof uni.$u.routeIntercept === 'function') {\r\n            // 定一个promise，根据用户执行resolve(true)或者resolve(false)来决定是否进行路由跳转\r\n            const isNext = await new Promise((resolve, reject) => {\r\n                uni.$u.routeIntercept(mergeConfig, resolve)\r\n            })\r\n            // 如果isNext为true，则执行路由跳转\r\n            isNext && this.openPage(mergeConfig)\r\n        } else {\r\n            this.openPage(mergeConfig)\r\n        }\r\n    }\r\n\r\n    // 执行路由跳转\r\n    openPage(config) {\r\n        // 解构参数\r\n        const {\r\n            url,\r\n            type,\r\n            delta,\r\n            animationType,\r\n            animationDuration\r\n        } = config\r\n        if (config.type == 'navigateTo' || config.type == 'to') {\r\n            uni.navigateTo({\r\n                url,\r\n                animationType,\r\n                animationDuration\r\n            })\r\n        }\r\n        if (config.type == 'redirectTo' || config.type == 'redirect') {\r\n            uni.redirectTo({\r\n                url\r\n            })\r\n        }\r\n        if (config.type == 'switchTab' || config.type == 'tab') {\r\n            uni.switchTab({\r\n                url\r\n            })\r\n        }\r\n        if (config.type == 'reLaunch' || config.type == 'launch') {\r\n            uni.reLaunch({\r\n                url\r\n            })\r\n        }\r\n        if (config.type == 'navigateBack' || config.type == 'back') {\r\n            uni.navigateBack({\r\n                delta\r\n            })\r\n        }\r\n    }\r\n}\r\n\r\nexport default (new Router()).route\r\n"], "names": ["queryParams", "deepMerge", "page", "uni"], "mappings": ";;;AAKA,MAAM,OAAO;AAAA,EACT,cAAc;AAEV,SAAK,SAAS;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA;AAAA,MACP,QAAQ,CAAE;AAAA;AAAA,MACV,eAAe;AAAA;AAAA,MACf,mBAAmB;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,IACd;AAGD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACpC;AAAA;AAAA,EAGD,YAAY,KAAK;AACb,WAAO,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG;AAAA,EACxC;AAAA;AAAA,EAGD,WAAW,KAAK,QAAQ;AACpB,UAAM,OAAO,KAAK,YAAY,GAAG;AAIjC,QAAI,QAAQ;AACZ,QAAI,gBAAgB,KAAK,GAAG,GAAG;AAE3B,cAAQA,0CAAAA,YAAY,QAAQ,KAAK;AAEjC,aAAO,OAAO,IAAI,KAAK;AAAA,IAC1B;AAED,YAAQA,0CAAW,YAAC,MAAM;AAC1B,WAAO,OAAO;AAAA,EACjB;AAAA;AAAA,EAGD,MAAM,MAAM,UAAU,IAAI,SAAS,CAAA,GAAI;AAEnC,QAAI,cAAc,CAAE;AAEpB,QAAI,OAAO,YAAY,UAAU;AAE7B,kBAAY,MAAM,KAAK,WAAW,SAAS,MAAM;AACjD,kBAAY,OAAO;AAAA,IAC/B,OAAe;AACH,oBAAcC,0CAAS,UAAC,KAAK,QAAQ,OAAO;AAE5C,kBAAY,MAAM,KAAK,WAAW,QAAQ,KAAK,QAAQ,MAAM;AAAA,IAChE;AAGD,QAAI,YAAY,QAAQC,0CAAI,KAAA;AAAI;AAEhC,QAAI,OAAO,WAAW;AAClB,WAAK,OAAO,YAAY,OAAO;AAAA,IAClC;AAED,gBAAY,SAAS;AAErB,kBAAcD,0CAAS,UAAC,KAAK,QAAQ,WAAW;AAEhD,QAAI,OAAOE,cAAG,MAAC,GAAG,mBAAmB,YAAY;AAE7C,YAAM,SAAS,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAClDA,sBAAAA,MAAI,GAAG,eAAe,aAAa,OAAO;AAAA,MAC1D,CAAa;AAED,gBAAU,KAAK,SAAS,WAAW;AAAA,IAC/C,OAAe;AACH,WAAK,SAAS,WAAW;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA,EAGD,SAAS,QAAQ;AAEb,UAAM;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACZ,IAAY;AACJ,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,MAAM;AACpDA,oBAAAA,MAAI,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MAChB,CAAa;AAAA,IACJ;AACD,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,YAAY;AAC1DA,oBAAAA,MAAI,WAAW;AAAA,QACX;AAAA,MAChB,CAAa;AAAA,IACJ;AACD,QAAI,OAAO,QAAQ,eAAe,OAAO,QAAQ,OAAO;AACpDA,oBAAAA,MAAI,UAAU;AAAA,QACV;AAAA,MAChB,CAAa;AAAA,IACJ;AACD,QAAI,OAAO,QAAQ,cAAc,OAAO,QAAQ,UAAU;AACtDA,oBAAAA,MAAI,SAAS;AAAA,QACT;AAAA,MAChB,CAAa;AAAA,IACJ;AACD,QAAI,OAAO,QAAQ,kBAAkB,OAAO,QAAQ,QAAQ;AACxDA,oBAAAA,MAAI,aAAa;AAAA,QACb;AAAA,MAChB,CAAa;AAAA,IACJ;AAAA,EACJ;AACL;AAEA,MAAA,QAAgB,IAAI,OAAM,EAAI;;"}