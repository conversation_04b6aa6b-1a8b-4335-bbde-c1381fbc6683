{"version": 3, "file": "async-validator.js", "sources": ["uni_modules/uview-plus/libs/util/async-validator.js"], "sourcesContent": ["function _extends() {\r\n    _extends = Object.assign || function (target) {\r\n        for (let i = 1; i < arguments.length; i++) {\r\n            const source = arguments[i]\r\n\r\n            for (const key in source) {\r\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\r\n                    target[key] = source[key]\r\n                }\r\n            }\r\n        }\r\n\r\n        return target\r\n    }\r\n\r\n    return _extends.apply(this, arguments)\r\n}\r\n\r\n/* eslint no-console:0 */\r\nconst formatRegExp = /%[sdj%]/g\r\nlet warning = function warning() {} // don't print warning message when in production env or node runtime\r\n\r\nif (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production' && typeof window\r\n\t!== 'undefined' && typeof document !== 'undefined') {\r\n    warning = function warning(type, errors) {\r\n        if (typeof console !== 'undefined' && console.warn) {\r\n            if (errors.every((e) => typeof e === 'string')) {\r\n                console.warn(type, errors)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction convertFieldsError(errors) {\r\n    if (!errors || !errors.length) return null\r\n    const fields = {}\r\n    errors.forEach((error) => {\r\n        const { field } = error\r\n        fields[field] = fields[field] || []\r\n        fields[field].push(error)\r\n    })\r\n    return fields\r\n}\r\n\r\nfunction format() {\r\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n        args[_key] = arguments[_key]\r\n    }\r\n\r\n    let i = 1\r\n    const f = args[0]\r\n    const len = args.length\r\n\r\n    if (typeof f === 'function') {\r\n        return f.apply(null, args.slice(1))\r\n    }\r\n\r\n    if (typeof f === 'string') {\r\n        let str = String(f).replace(formatRegExp, (x) => {\r\n            if (x === '%%') {\r\n                return '%'\r\n            }\r\n\r\n            if (i >= len) {\r\n                return x\r\n            }\r\n\r\n            switch (x) {\r\n            case '%s':\r\n                return String(args[i++])\r\n\r\n            case '%d':\r\n                return Number(args[i++])\r\n\r\n            case '%j':\r\n                try {\r\n                    return JSON.stringify(args[i++])\r\n                } catch (_) {\r\n                    return '[Circular]'\r\n                }\r\n\r\n                break\r\n\r\n            default:\r\n                return x\r\n            }\r\n        })\r\n\r\n        for (let arg = args[i]; i < len; arg = args[++i]) {\r\n            str += ` ${arg}`\r\n        }\r\n\r\n        return str\r\n    }\r\n\r\n    return f\r\n}\r\n\r\nfunction isNativeStringType(type) {\r\n    return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'pattern'\r\n}\r\n\r\nfunction isEmptyValue(value, type) {\r\n    if (value === undefined || value === null) {\r\n        return true\r\n    }\r\n\r\n    if (type === 'array' && Array.isArray(value) && !value.length) {\r\n        return true\r\n    }\r\n\r\n    if (isNativeStringType(type) && typeof value === 'string' && !value) {\r\n        return true\r\n    }\r\n\r\n    return false\r\n}\r\n\r\nfunction asyncParallelArray(arr, func, callback) {\r\n    const results = []\r\n    let total = 0\r\n    const arrLength = arr.length\r\n\r\n    function count(errors) {\r\n        results.push.apply(results, errors)\r\n        total++\r\n\r\n        if (total === arrLength) {\r\n            callback(results)\r\n        }\r\n    }\r\n\r\n    arr.forEach((a) => {\r\n        func(a, count)\r\n    })\r\n}\r\n\r\nfunction asyncSerialArray(arr, func, callback) {\r\n    let index = 0\r\n    const arrLength = arr.length\r\n\r\n    function next(errors) {\r\n        if (errors && errors.length) {\r\n            callback(errors)\r\n            return\r\n        }\r\n\r\n        const original = index\r\n        index += 1\r\n\r\n        if (original < arrLength) {\r\n            func(arr[original], next)\r\n        } else {\r\n            callback([])\r\n        }\r\n    }\r\n\r\n    next([])\r\n}\r\n\r\nfunction flattenObjArr(objArr) {\r\n    const ret = []\r\n    Object.keys(objArr).forEach((k) => {\r\n        ret.push.apply(ret, objArr[k])\r\n    })\r\n    return ret\r\n}\r\n\r\nfunction asyncMap(objArr, option, func, callback) {\r\n    if (option.first) {\r\n        const _pending = new Promise((resolve, reject) => {\r\n            const next = function next(errors) {\r\n                callback(errors)\r\n                return errors.length ? reject({\r\n                    errors,\r\n                    fields: convertFieldsError(errors)\r\n                }) : resolve()\r\n            }\r\n\r\n            const flattenArr = flattenObjArr(objArr)\r\n            asyncSerialArray(flattenArr, func, next)\r\n        })\r\n\r\n        _pending.catch((e) => e)\r\n\r\n        return _pending\r\n    }\r\n\r\n    let firstFields = option.firstFields || []\r\n\r\n    if (firstFields === true) {\r\n        firstFields = Object.keys(objArr)\r\n    }\r\n\r\n    const objArrKeys = Object.keys(objArr)\r\n    const objArrLength = objArrKeys.length\r\n    let total = 0\r\n    const results = []\r\n    const pending = new Promise((resolve, reject) => {\r\n        const next = function next(errors) {\r\n            results.push.apply(results, errors)\r\n            total++\r\n\r\n            if (total === objArrLength) {\r\n                callback(results)\r\n                return results.length ? reject({\r\n                    errors: results,\r\n                    fields: convertFieldsError(results)\r\n                }) : resolve()\r\n            }\r\n        }\r\n\r\n        if (!objArrKeys.length) {\r\n            callback(results)\r\n            resolve()\r\n        }\r\n\r\n        objArrKeys.forEach((key) => {\r\n            const arr = objArr[key]\r\n\r\n            if (firstFields.indexOf(key) !== -1) {\r\n                asyncSerialArray(arr, func, next)\r\n            } else {\r\n                asyncParallelArray(arr, func, next)\r\n            }\r\n        })\r\n    })\r\n    pending.catch((e) => e)\r\n    return pending\r\n}\r\n\r\nfunction complementError(rule) {\r\n    return function (oe) {\r\n        if (oe && oe.message) {\r\n            oe.field = oe.field || rule.fullField\r\n            return oe\r\n        }\r\n\r\n        return {\r\n            message: typeof oe === 'function' ? oe() : oe,\r\n            field: oe.field || rule.fullField\r\n        }\r\n    }\r\n}\r\n\r\nfunction deepMerge(target, source) {\r\n    if (source) {\r\n        for (const s in source) {\r\n            if (source.hasOwnProperty(s)) {\r\n                const value = source[s]\r\n\r\n                if (typeof value === 'object' && typeof target[s] === 'object') {\r\n                    target[s] = { ...target[s], ...value }\r\n                } else {\r\n                    target[s] = value\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return target\r\n}\r\n\r\n/**\r\n *  Rule for validating required fields.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction required(rule, value, source, errors, options, type) {\r\n    if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\r\n        errors.push(format(options.messages.required, rule.fullField))\r\n    }\r\n}\r\n\r\n/**\r\n *  Rule for validating whitespace.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction whitespace(rule, value, source, errors, options) {\r\n    if (/^\\s+$/.test(value) || value === '') {\r\n        errors.push(format(options.messages.whitespace, rule.fullField))\r\n    }\r\n}\r\n\r\n/* eslint max-len:0 */\r\n\r\nconst pattern = {\r\n    // http://emailregex.com/\r\n    email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/,\r\n    url: new RegExp(\r\n        '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\r\n        'i'\r\n    ),\r\n    hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\r\n}\r\nvar types = {\r\n    integer: function integer(value) {\r\n        return /^(-)?\\d+$/.test(value);\r\n    },\r\n    float: function float(value) {\r\n        return /^(-)?\\d+(\\.\\d+)?$/.test(value);\r\n    },\r\n    array: function array(value) {\r\n        return Array.isArray(value)\r\n    },\r\n    regexp: function regexp(value) {\r\n        if (value instanceof RegExp) {\r\n            return true\r\n        }\r\n\r\n        try {\r\n            return !!new RegExp(value)\r\n        } catch (e) {\r\n            return false\r\n        }\r\n    },\r\n    date: function date(value) {\r\n        return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear\r\n\t\t\t=== 'function'\r\n    },\r\n    number: function number(value) {\r\n        if (isNaN(value)) {\r\n            return false\r\n        }\r\n\r\n        // 修改源码，将字符串数值先转为数值\r\n        return typeof +value === 'number'\r\n    },\r\n    object: function object(value) {\r\n        return typeof value === 'object' && !types.array(value)\r\n    },\r\n    method: function method(value) {\r\n        return typeof value === 'function'\r\n    },\r\n    email: function email(value) {\r\n        return typeof value === 'string' && !!value.match(pattern.email) && value.length < 255\r\n    },\r\n    url: function url(value) {\r\n        return typeof value === 'string' && !!value.match(pattern.url)\r\n    },\r\n    hex: function hex(value) {\r\n        return typeof value === 'string' && !!value.match(pattern.hex)\r\n    }\r\n}\r\n/**\r\n *  Rule for validating the type of a value.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction type(rule, value, source, errors, options) {\r\n    if (rule.required && value === undefined) {\r\n        required(rule, value, source, errors, options)\r\n        return\r\n    }\r\n\r\n    const custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex']\r\n    const ruleType = rule.type\r\n\r\n    if (custom.indexOf(ruleType) > -1) {\r\n        if (!types[ruleType](value)) {\r\n            errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type))\r\n        } // straight typeof check\r\n    } else if (ruleType && typeof value !== rule.type) {\r\n        errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type))\r\n    }\r\n}\r\n\r\n/**\r\n *  Rule for validating minimum and maximum allowed values.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction range(rule, value, source, errors, options) {\r\n    const len = typeof rule.len === 'number'\r\n    const min = typeof rule.min === 'number'\r\n    const max = typeof rule.max === 'number' // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\r\n\r\n    const spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\r\n    let val = value\r\n    let key = null\r\n    const num = typeof value === 'number'\r\n    const str = typeof value === 'string'\r\n    const arr = Array.isArray(value)\r\n\r\n    if (num) {\r\n        key = 'number'\r\n    } else if (str) {\r\n        key = 'string'\r\n    } else if (arr) {\r\n        key = 'array'\r\n    } // if the value is not of a supported type for range validation\r\n    // the validation rule rule should use the\r\n    // type property to also test for a particular type\r\n\r\n    if (!key) {\r\n        return false\r\n    }\r\n\r\n    if (arr) {\r\n        val = value.length\r\n    }\r\n\r\n    if (str) {\r\n        // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\r\n        val = value.replace(spRegexp, '_').length\r\n    }\r\n\r\n    if (len) {\r\n        if (val !== rule.len) {\r\n            errors.push(format(options.messages[key].len, rule.fullField, rule.len))\r\n        }\r\n    } else if (min && !max && val < rule.min) {\r\n        errors.push(format(options.messages[key].min, rule.fullField, rule.min))\r\n    } else if (max && !min && val > rule.max) {\r\n        errors.push(format(options.messages[key].max, rule.fullField, rule.max))\r\n    } else if (min && max && (val < rule.min || val > rule.max)) {\r\n        errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max))\r\n    }\r\n}\r\n\r\nconst ENUM = 'enum'\r\n/**\r\n *  Rule for validating a value exists in an enumerable list.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction enumerable(rule, value, source, errors, options) {\r\n    rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : []\r\n\r\n    if (rule[ENUM].indexOf(value) === -1) {\r\n        errors.push(format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')))\r\n    }\r\n}\r\n\r\n/**\r\n *  Rule for validating a regular expression pattern.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param source The source object being validated.\r\n *  @param errors An array of errors that this rule may add\r\n *  validation errors to.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction pattern$1(rule, value, source, errors, options) {\r\n    if (rule.pattern) {\r\n        if (rule.pattern instanceof RegExp) {\r\n            // if a RegExp instance is passed, reset `lastIndex` in case its `global`\r\n            // flag is accidentally set to `true`, which in a validation scenario\r\n            // is not necessary and the result might be misleading\r\n            rule.pattern.lastIndex = 0\r\n\r\n            if (!rule.pattern.test(value)) {\r\n                errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern))\r\n            }\r\n        } else if (typeof rule.pattern === 'string') {\r\n            const _pattern = new RegExp(rule.pattern)\r\n\r\n            if (!_pattern.test(value)) {\r\n                errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern))\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nconst rules = {\r\n    required,\r\n    whitespace,\r\n    type,\r\n    range,\r\n    enum: enumerable,\r\n    pattern: pattern$1\r\n}\r\n\r\n/**\r\n *  Performs validation for string types.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction string(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value, 'string') && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options, 'string')\r\n\r\n        if (!isEmptyValue(value, 'string')) {\r\n            rules.type(rule, value, source, errors, options)\r\n            rules.range(rule, value, source, errors, options)\r\n            rules.pattern(rule, value, source, errors, options)\r\n\r\n            if (rule.whitespace === true) {\r\n                rules.whitespace(rule, value, source, errors, options)\r\n            }\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a function.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction method(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a number.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction number(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (value === '') {\r\n            value = undefined\r\n        }\r\n\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n            rules.range(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a boolean.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction _boolean(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates the regular expression type.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction regexp(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (!isEmptyValue(value)) {\r\n            rules.type(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a number is an integer.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction integer(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n            rules.range(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a number is a floating point number.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction floatFn(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n            rules.range(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates an array.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction array(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value, 'array') && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options, 'array')\r\n\r\n        if (!isEmptyValue(value, 'array')) {\r\n            rules.type(rule, value, source, errors, options)\r\n            rules.range(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates an object.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction object(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules.type(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\nconst ENUM$1 = 'enum'\r\n/**\r\n *  Validates an enumerable list.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction enumerable$1(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (value !== undefined) {\r\n            rules[ENUM$1](rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Validates a regular expression pattern.\r\n *\r\n *  Performs validation when a rule only contains\r\n *  a pattern property but is not declared as a string type.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction pattern$2(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value, 'string') && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (!isEmptyValue(value, 'string')) {\r\n            rules.pattern(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\nfunction date(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n\r\n        if (!isEmptyValue(value)) {\r\n            let dateObject\r\n\r\n            if (typeof value === 'number') {\r\n                dateObject = new Date(value)\r\n            } else {\r\n                dateObject = value\r\n            }\r\n\r\n            rules.type(rule, dateObject, source, errors, options)\r\n\r\n            if (dateObject) {\r\n                rules.range(rule, dateObject.getTime(), source, errors, options)\r\n            }\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\nfunction required$1(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const type = Array.isArray(value) ? 'array' : typeof value\r\n    rules.required(rule, value, source, errors, options, type)\r\n    callback(errors)\r\n}\r\n\r\nfunction type$1(rule, value, callback, source, options) {\r\n    const ruleType = rule.type\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value, ruleType) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options, ruleType)\r\n\r\n        if (!isEmptyValue(value, ruleType)) {\r\n            rules.type(rule, value, source, errors, options)\r\n        }\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\n/**\r\n *  Performs validation for any type.\r\n *\r\n *  @param rule The validation rule.\r\n *  @param value The value of the field on the source object.\r\n *  @param callback The callback function.\r\n *  @param source The source object being validated.\r\n *  @param options The validation options.\r\n *  @param options.messages The validation messages.\r\n */\r\n\r\nfunction any(rule, value, callback, source, options) {\r\n    const errors = []\r\n    const validate = rule.required || !rule.required && source.hasOwnProperty(rule.field)\r\n\r\n    if (validate) {\r\n        if (isEmptyValue(value) && !rule.required) {\r\n            return callback()\r\n        }\r\n\r\n        rules.required(rule, value, source, errors, options)\r\n    }\r\n\r\n    callback(errors)\r\n}\r\n\r\nconst validators = {\r\n    string,\r\n    method,\r\n    number,\r\n    boolean: _boolean,\r\n    regexp,\r\n    integer,\r\n    float: floatFn,\r\n    array,\r\n    object,\r\n    enum: enumerable$1,\r\n    pattern: pattern$2,\r\n    date,\r\n    url: type$1,\r\n    hex: type$1,\r\n    email: type$1,\r\n    required: required$1,\r\n    any\r\n}\r\n\r\nfunction newMessages() {\r\n    return {\r\n        default: 'Validation error on field %s',\r\n        required: '%s is required',\r\n        enum: '%s must be one of %s',\r\n        whitespace: '%s cannot be empty',\r\n        date: {\r\n            format: '%s date %s is invalid for format %s',\r\n            parse: '%s date could not be parsed, %s is invalid ',\r\n            invalid: '%s date %s is invalid'\r\n        },\r\n        types: {\r\n            string: '%s is not a %s',\r\n            method: '%s is not a %s (function)',\r\n            array: '%s is not an %s',\r\n            object: '%s is not an %s',\r\n            number: '%s is not a %s',\r\n            date: '%s is not a %s',\r\n            boolean: '%s is not a %s',\r\n            integer: '%s is not an %s',\r\n            float: '%s is not a %s',\r\n            regexp: '%s is not a valid %s',\r\n            email: '%s is not a valid %s',\r\n            url: '%s is not a valid %s',\r\n            hex: '%s is not a valid %s'\r\n        },\r\n        string: {\r\n            len: '%s must be exactly %s characters',\r\n            min: '%s must be at least %s characters',\r\n            max: '%s cannot be longer than %s characters',\r\n            range: '%s must be between %s and %s characters'\r\n        },\r\n        number: {\r\n            len: '%s must equal %s',\r\n            min: '%s cannot be less than %s',\r\n            max: '%s cannot be greater than %s',\r\n            range: '%s must be between %s and %s'\r\n        },\r\n        array: {\r\n            len: '%s must be exactly %s in length',\r\n            min: '%s cannot be less than %s in length',\r\n            max: '%s cannot be greater than %s in length',\r\n            range: '%s must be between %s and %s in length'\r\n        },\r\n        pattern: {\r\n            mismatch: '%s value %s does not match pattern %s'\r\n        },\r\n        clone: function clone() {\r\n            const cloned = JSON.parse(JSON.stringify(this))\r\n            cloned.clone = this.clone\r\n            return cloned\r\n        }\r\n    }\r\n}\r\nconst messages = newMessages()\r\n\r\n/**\r\n *  Encapsulates a validation schema.\r\n *\r\n *  @param descriptor An object declaring validation rules\r\n *  for this schema.\r\n */\r\n\r\nfunction Schema(descriptor) {\r\n    this.rules = null\r\n    this._messages = messages\r\n    this.define(descriptor)\r\n}\r\n\r\nSchema.prototype = {\r\n    messages: function messages(_messages) {\r\n        if (_messages) {\r\n            this._messages = deepMerge(newMessages(), _messages)\r\n        }\r\n\r\n        return this._messages\r\n    },\r\n    define: function define(rules) {\r\n        if (!rules) {\r\n            throw new Error('Cannot configure a schema with no rules')\r\n        }\r\n\r\n        if (typeof rules !== 'object' || Array.isArray(rules)) {\r\n            throw new Error('Rules must be an object')\r\n        }\r\n\r\n        this.rules = {}\r\n        let z\r\n        let item\r\n\r\n        for (z in rules) {\r\n            if (rules.hasOwnProperty(z)) {\r\n                item = rules[z]\r\n                this.rules[z] = Array.isArray(item) ? item : [item]\r\n            }\r\n        }\r\n    },\r\n    validate: function validate(source_, o, oc) {\r\n        const _this = this\r\n\r\n        if (o === void 0) {\r\n            o = {}\r\n        }\r\n\r\n        if (oc === void 0) {\r\n            oc = function oc() {}\r\n        }\r\n\r\n        let source = source_\r\n        let options = o\r\n        let callback = oc\r\n\r\n        if (typeof options === 'function') {\r\n            callback = options\r\n            options = {}\r\n        }\r\n\r\n        if (!this.rules || Object.keys(this.rules).length === 0) {\r\n            if (callback) {\r\n                callback()\r\n            }\r\n\r\n            return Promise.resolve()\r\n        }\r\n\r\n        function complete(results) {\r\n            let i\r\n            let errors = []\r\n            let fields = {}\r\n\r\n            function add(e) {\r\n                if (Array.isArray(e)) {\r\n                    let _errors\r\n\r\n                    errors = (_errors = errors).concat.apply(_errors, e)\r\n                } else {\r\n                    errors.push(e)\r\n                }\r\n            }\r\n\r\n            for (i = 0; i < results.length; i++) {\r\n                add(results[i])\r\n            }\r\n\r\n            if (!errors.length) {\r\n                errors = null\r\n                fields = null\r\n            } else {\r\n                fields = convertFieldsError(errors)\r\n            }\r\n\r\n            callback(errors, fields)\r\n        }\r\n\r\n        if (options.messages) {\r\n            let messages$1 = this.messages()\r\n\r\n            if (messages$1 === messages) {\r\n                messages$1 = newMessages()\r\n            }\r\n\r\n            deepMerge(messages$1, options.messages)\r\n            options.messages = messages$1\r\n        } else {\r\n            options.messages = this.messages()\r\n        }\r\n\r\n        let arr\r\n        let value\r\n        const series = {}\r\n        const keys = options.keys || Object.keys(this.rules)\r\n        keys.forEach((z) => {\r\n            arr = _this.rules[z]\r\n            value = source[z]\r\n            arr.forEach((r) => {\r\n                let rule = r\r\n\r\n                if (typeof rule.transform === 'function') {\r\n                    if (source === source_) {\r\n                        source = { ...source }\r\n                    }\r\n\r\n                    value = source[z] = rule.transform(value)\r\n                }\r\n\r\n                if (typeof rule === 'function') {\r\n                    rule = {\r\n                        validator: rule\r\n                    }\r\n                } else {\r\n                    rule = { ...rule }\r\n                }\r\n\r\n                rule.validator = _this.getValidationMethod(rule)\r\n                rule.field = z\r\n                rule.fullField = rule.fullField || z\r\n                rule.type = _this.getType(rule)\r\n\r\n                if (!rule.validator) {\r\n                    return\r\n                }\r\n\r\n                series[z] = series[z] || []\r\n                series[z].push({\r\n                    rule,\r\n                    value,\r\n                    source,\r\n                    field: z\r\n                })\r\n            })\r\n        })\r\n        const errorFields = {}\r\n        return asyncMap(series, options, (data, doIt) => {\r\n            const { rule } = data\r\n            let deep = (rule.type === 'object' || rule.type === 'array') && (typeof rule.fields === 'object' || typeof rule.defaultField\r\n\t\t\t\t=== 'object')\r\n            deep = deep && (rule.required || !rule.required && data.value)\r\n            rule.field = data.field\r\n\r\n            function addFullfield(key, schema) {\r\n                return { ...schema, fullField: `${rule.fullField}.${key}` }\r\n            }\r\n\r\n            function cb(e) {\r\n                if (e === void 0) {\r\n                    e = []\r\n                }\r\n\r\n                let errors = e\r\n\r\n                if (!Array.isArray(errors)) {\r\n                    errors = [errors]\r\n                }\r\n\r\n                if (!options.suppressWarning && errors.length) {\r\n                    Schema.warning('async-validator:', errors)\r\n                }\r\n\r\n                if (errors.length && rule.message) {\r\n                    errors = [].concat(rule.message)\r\n                }\r\n\r\n                errors = errors.map(complementError(rule))\r\n\r\n                if (options.first && errors.length) {\r\n                    errorFields[rule.field] = 1\r\n                    return doIt(errors)\r\n                }\r\n\r\n                if (!deep) {\r\n                    doIt(errors)\r\n                } else {\r\n                    // if rule is required but the target object\r\n                    // does not exist fail at the rule level and don't\r\n                    // go deeper\r\n                    if (rule.required && !data.value) {\r\n                        if (rule.message) {\r\n                            errors = [].concat(rule.message).map(complementError(rule))\r\n                        } else if (options.error) {\r\n                            errors = [options.error(rule, format(options.messages.required, rule.field))]\r\n                        } else {\r\n                            errors = []\r\n                        }\r\n\r\n                        return doIt(errors)\r\n                    }\r\n\r\n                    let fieldsSchema = {}\r\n\r\n                    if (rule.defaultField) {\r\n                        for (const k in data.value) {\r\n                            if (data.value.hasOwnProperty(k)) {\r\n                                fieldsSchema[k] = rule.defaultField\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    fieldsSchema = { ...fieldsSchema, ...data.rule.fields }\r\n\r\n                    for (const f in fieldsSchema) {\r\n                        if (fieldsSchema.hasOwnProperty(f)) {\r\n                            const fieldSchema = Array.isArray(fieldsSchema[f]) ? fieldsSchema[f] : [fieldsSchema[f]]\r\n                            fieldsSchema[f] = fieldSchema.map(addFullfield.bind(null, f))\r\n                        }\r\n                    }\r\n\r\n                    const schema = new Schema(fieldsSchema)\r\n                    schema.messages(options.messages)\r\n\r\n                    if (data.rule.options) {\r\n                        data.rule.options.messages = options.messages\r\n                        data.rule.options.error = options.error\r\n                    }\r\n\r\n                    schema.validate(data.value, data.rule.options || options, (errs) => {\r\n                        const finalErrors = []\r\n\r\n                        if (errors && errors.length) {\r\n                            finalErrors.push.apply(finalErrors, errors)\r\n                        }\r\n\r\n                        if (errs && errs.length) {\r\n                            finalErrors.push.apply(finalErrors, errs)\r\n                        }\r\n\r\n                        doIt(finalErrors.length ? finalErrors : null)\r\n                    })\r\n                }\r\n            }\r\n\r\n            let res\r\n\r\n            if (rule.asyncValidator) {\r\n                res = rule.asyncValidator(rule, data.value, cb, data.source, options)\r\n            } else if (rule.validator) {\r\n                res = rule.validator(rule, data.value, cb, data.source, options)\r\n\r\n                if (res === true) {\r\n                    cb()\r\n                } else if (res === false) {\r\n                    cb(rule.message || `${rule.field} fails`)\r\n                } else if (res instanceof Array) {\r\n                    cb(res)\r\n                } else if (res instanceof Error) {\r\n                    cb(res.message)\r\n                }\r\n            }\r\n\r\n            if (res && res.then) {\r\n                res.then(() => cb(), (e) => cb(e))\r\n            }\r\n        }, (results) => {\r\n            complete(results)\r\n        })\r\n    },\r\n    getType: function getType(rule) {\r\n        if (rule.type === undefined && rule.pattern instanceof RegExp) {\r\n            rule.type = 'pattern'\r\n        }\r\n\r\n        if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\r\n            throw new Error(format('Unknown rule type %s', rule.type))\r\n        }\r\n\r\n        return rule.type || 'string'\r\n    },\r\n    getValidationMethod: function getValidationMethod(rule) {\r\n        if (typeof rule.validator === 'function') {\r\n            return rule.validator\r\n        }\r\n\r\n        const keys = Object.keys(rule)\r\n        const messageIndex = keys.indexOf('message')\r\n\r\n        if (messageIndex !== -1) {\r\n            keys.splice(messageIndex, 1)\r\n        }\r\n\r\n        if (keys.length === 1 && keys[0] === 'required') {\r\n            return validators.required\r\n        }\r\n\r\n        return validators[this.getType(rule)] || false\r\n    }\r\n}\r\n\r\nSchema.register = function register(type, validator) {\r\n    if (typeof validator !== 'function') {\r\n        throw new Error('Cannot register a validator by type, validator is not a function')\r\n    }\r\n\r\n    validators[type] = validator\r\n}\r\n\r\nSchema.warning = warning\r\nSchema.messages = messages\r\n\r\nexport default Schema\r\n// # sourceMappingURL=index.js.map\r\n"], "names": ["warning", "type", "uni", "next", "validate", "method", "number", "regexp", "integer", "array", "object", "date", "messages", "rules", "oc"], "mappings": ";;AAmBA,MAAM,eAAe;AACrB,IAAI,UAAU,SAASA,WAAU;AAAC;AAElC,IAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAyC,OAAO,WAChG,eAAe,OAAO,aAAa,aAAa;AACvC,YAAA,SAASA,SAAQC,OAAM,QAAQ;AACrC,QAAI,OAAO,YAAY,eAAe,QAAQ,MAAM;AAChD,UAAI,OAAO,MAAM,CAAC,MAAM,OAAO,MAAM,QAAQ,GAAG;AAC5CC,sBAAA,MAAA,MAAA,QAAA,6DAAaD,OAAM,MAAM;AAAA,MAC7B;AAAA,IACJ;AAAA,EAAA;AAER;AAEA,SAAS,mBAAmB,QAAQ;AAC5B,MAAA,CAAC,UAAU,CAAC,OAAO;AAAe,WAAA;AACtC,QAAM,SAAS,CAAA;AACR,SAAA,QAAQ,CAAC,UAAU;AAChB,UAAA,EAAE,MAAU,IAAA;AAClB,WAAO,KAAK,IAAI,OAAO,KAAK,KAAK,CAAA;AAC1B,WAAA,KAAK,EAAE,KAAK,KAAK;AAAA,EAAA,CAC3B;AACM,SAAA;AACX;AAEA,SAAS,SAAS;AACd,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAChF,SAAA,IAAI,IAAI,UAAU,IAAI;AAAA,EAC/B;AAEA,MAAI,IAAI;AACF,QAAA,IAAI,KAAK,CAAC;AAChB,QAAM,MAAM,KAAK;AAEb,MAAA,OAAO,MAAM,YAAY;AACzB,WAAO,EAAE,MAAM,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACtC;AAEI,MAAA,OAAO,MAAM,UAAU;AACvB,QAAI,MAAM,OAAO,CAAC,EAAE,QAAQ,cAAc,CAAC,MAAM;AAC7C,UAAI,MAAM,MAAM;AACL,eAAA;AAAA,MACX;AAEA,UAAI,KAAK,KAAK;AACH,eAAA;AAAA,MACX;AAEA,cAAQ,GAAG;AAAA,QACX,KAAK;AACM,iBAAA,OAAO,KAAK,GAAG,CAAC;AAAA,QAE3B,KAAK;AACM,iBAAA,OAAO,KAAK,GAAG,CAAC;AAAA,QAE3B,KAAK;AACG,cAAA;AACA,mBAAO,KAAK,UAAU,KAAK,GAAG,CAAC;AAAA,mBAC1B,GAAG;AACD,mBAAA;AAAA,UACX;AAEA;AAAA,QAEJ;AACW,iBAAA;AAAA,MACX;AAAA,IAAA,CACH;AAEQ,aAAA,MAAM,KAAK,CAAC,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,GAAG;AAC9C,aAAO,IAAI,GAAG;AAAA,IAClB;AAEO,WAAA;AAAA,EACX;AAEO,SAAA;AACX;AAEA,SAAS,mBAAmBA,OAAM;AACvBA,SAAAA,UAAS,YAAYA,UAAS,SAASA,UAAS,SAASA,UAAS,WAAWA,UAAS;AACjG;AAEA,SAAS,aAAa,OAAOA,OAAM;AAC3B,MAAA,UAAU,UAAa,UAAU,MAAM;AAChC,WAAA;AAAA,EACX;AAEIA,MAAAA,UAAS,WAAW,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,QAAQ;AACpD,WAAA;AAAA,EACX;AAEA,MAAI,mBAAmBA,KAAI,KAAK,OAAO,UAAU,YAAY,CAAC,OAAO;AAC1D,WAAA;AAAA,EACX;AAEO,SAAA;AACX;AAEA,SAAS,mBAAmB,KAAK,MAAM,UAAU;AAC7C,QAAM,UAAU,CAAA;AAChB,MAAI,QAAQ;AACZ,QAAM,YAAY,IAAI;AAEtB,WAAS,MAAM,QAAQ;AACX,YAAA,KAAK,MAAM,SAAS,MAAM;AAClC;AAEA,QAAI,UAAU,WAAW;AACrB,eAAS,OAAO;AAAA,IACpB;AAAA,EACJ;AAEI,MAAA,QAAQ,CAAC,MAAM;AACf,SAAK,GAAG,KAAK;AAAA,EAAA,CAChB;AACL;AAEA,SAAS,iBAAiB,KAAK,MAAM,UAAU;AAC3C,MAAI,QAAQ;AACZ,QAAM,YAAY,IAAI;AAEtB,WAAS,KAAK,QAAQ;AACd,QAAA,UAAU,OAAO,QAAQ;AACzB,eAAS,MAAM;AACf;AAAA,IACJ;AAEA,UAAM,WAAW;AACR,aAAA;AAET,QAAI,WAAW,WAAW;AACjB,WAAA,IAAI,QAAQ,GAAG,IAAI;AAAA,IAAA,OACrB;AACH,eAAS,CAAE,CAAA;AAAA,IACf;AAAA,EACJ;AAEA,OAAK,CAAE,CAAA;AACX;AAEA,SAAS,cAAc,QAAQ;AAC3B,QAAM,MAAM,CAAA;AACZ,SAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AAC/B,QAAI,KAAK,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,EAAA,CAChC;AACM,SAAA;AACX;AAEA,SAAS,SAAS,QAAQ,QAAQ,MAAM,UAAU;AAC9C,MAAI,OAAO,OAAO;AACd,UAAM,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxC,YAAA,OAAO,SAASE,MAAK,QAAQ;AAC/B,iBAAS,MAAM;AACR,eAAA,OAAO,SAAS,OAAO;AAAA,UAC1B;AAAA,UACA,QAAQ,mBAAmB,MAAM;AAAA,QAAA,CACpC,IAAI,QAAQ;AAAA,MAAA;AAGX,YAAA,aAAa,cAAc,MAAM;AACtB,uBAAA,YAAY,MAAM,IAAI;AAAA,IAAA,CAC1C;AAEQ,aAAA,MAAM,CAAC,MAAM,CAAC;AAEhB,WAAA;AAAA,EACX;AAEI,MAAA,cAAc,OAAO,eAAe;AAExC,MAAI,gBAAgB,MAAM;AACR,kBAAA,OAAO,KAAK,MAAM;AAAA,EACpC;AAEM,QAAA,aAAa,OAAO,KAAK,MAAM;AACrC,QAAM,eAAe,WAAW;AAChC,MAAI,QAAQ;AACZ,QAAM,UAAU,CAAA;AAChB,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAA,OAAO,SAASA,MAAK,QAAQ;AACvB,cAAA,KAAK,MAAM,SAAS,MAAM;AAClC;AAEA,UAAI,UAAU,cAAc;AACxB,iBAAS,OAAO;AACT,eAAA,QAAQ,SAAS,OAAO;AAAA,UAC3B,QAAQ;AAAA,UACR,QAAQ,mBAAmB,OAAO;AAAA,QAAA,CACrC,IAAI,QAAQ;AAAA,MACjB;AAAA,IAAA;AAGA,QAAA,CAAC,WAAW,QAAQ;AACpB,eAAS,OAAO;AACR;IACZ;AAEW,eAAA,QAAQ,CAAC,QAAQ;AAClB,YAAA,MAAM,OAAO,GAAG;AAEtB,UAAI,YAAY,QAAQ,GAAG,MAAM,IAAI;AAChB,yBAAA,KAAK,MAAM,IAAI;AAAA,MAAA,OAC7B;AACgB,2BAAA,KAAK,MAAM,IAAI;AAAA,MACtC;AAAA,IAAA,CACH;AAAA,EAAA,CACJ;AACO,UAAA,MAAM,CAAC,MAAM,CAAC;AACf,SAAA;AACX;AAEA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,SAAU,IAAI;AACb,QAAA,MAAM,GAAG,SAAS;AACf,SAAA,QAAQ,GAAG,SAAS,KAAK;AACrB,aAAA;AAAA,IACX;AAEO,WAAA;AAAA,MACH,SAAS,OAAO,OAAO,aAAa,GAAO,IAAA;AAAA,MAC3C,OAAO,GAAG,SAAS,KAAK;AAAA,IAAA;AAAA,EAC5B;AAER;AAEA,SAAS,UAAU,QAAQ,QAAQ;AAC/B,MAAI,QAAQ;AACR,eAAW,KAAK,QAAQ;AAChB,UAAA,OAAO,eAAe,CAAC,GAAG;AACpB,cAAA,QAAQ,OAAO,CAAC;AAEtB,YAAI,OAAO,UAAU,YAAY,OAAO,OAAO,CAAC,MAAM,UAAU;AACrD,iBAAA,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,GAAG,GAAG;QAAM,OAClC;AACH,iBAAO,CAAC,IAAI;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEO,SAAA;AACX;AAcA,SAAS,SAAS,MAAM,OAAO,QAAQ,QAAQ,SAASF,OAAM;AAC1D,MAAI,KAAK,aAAa,CAAC,OAAO,eAAe,KAAK,KAAK,KAAK,aAAa,OAAOA,SAAQ,KAAK,IAAI,IAAI;AACjG,WAAO,KAAK,OAAO,QAAQ,SAAS,UAAU,KAAK,SAAS,CAAC;AAAA,EACjE;AACJ;AAcA,SAAS,WAAW,MAAM,OAAO,QAAQ,QAAQ,SAAS;AACtD,MAAI,QAAQ,KAAK,KAAK,KAAK,UAAU,IAAI;AACrC,WAAO,KAAK,OAAO,QAAQ,SAAS,YAAY,KAAK,SAAS,CAAC;AAAA,EACnE;AACJ;AAIA,MAAM,UAAU;AAAA;AAAA,EAEZ,OAAO;AAAA,EACP,KAAK,IAAI;AAAA,IACL;AAAA,IACA;AAAA,EACJ;AAAA,EACA,KAAK;AACT;AACA,IAAI,QAAQ;AAAA,EACR,SAAS,SAAS,QAAQ,OAAO;AACtB,WAAA,YAAY,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,OAAO,SAAS,MAAM,OAAO;AAClB,WAAA,oBAAoB,KAAK,KAAK;AAAA,EACzC;AAAA,EACA,OAAO,SAAS,MAAM,OAAO;AAClB,WAAA,MAAM,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO;AAC3B,QAAI,iBAAiB,QAAQ;AAClB,aAAA;AAAA,IACX;AAEI,QAAA;AACA,aAAO,CAAC,CAAC,IAAI,OAAO,KAAK;AAAA,aACpB,GAAG;AACD,aAAA;AAAA,IACX;AAAA,EACJ;AAAA,EACA,MAAM,SAAS,KAAK,OAAO;AAChB,WAAA,OAAO,MAAM,YAAY,cAAc,OAAO,MAAM,aAAa,cAAc,OAAO,MAAM,YACpG;AAAA,EACH;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO;AACvB,QAAA,MAAM,KAAK,GAAG;AACP,aAAA;AAAA,IACX;AAGO,WAAA,OAAO,CAAC,UAAU;AAAA,EAC7B;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO;AAC3B,WAAO,OAAO,UAAU,YAAY,CAAC,MAAM,MAAM,KAAK;AAAA,EAC1D;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO;AAC3B,WAAO,OAAO,UAAU;AAAA,EAC5B;AAAA,EACA,OAAO,SAAS,MAAM,OAAO;AAClB,WAAA,OAAO,UAAU,YAAY,CAAC,CAAC,MAAM,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AAAA,EACvF;AAAA,EACA,KAAK,SAAS,IAAI,OAAO;AACd,WAAA,OAAO,UAAU,YAAY,CAAC,CAAC,MAAM,MAAM,QAAQ,GAAG;AAAA,EACjE;AAAA,EACA,KAAK,SAAS,IAAI,OAAO;AACd,WAAA,OAAO,UAAU,YAAY,CAAC,CAAC,MAAM,MAAM,QAAQ,GAAG;AAAA,EACjE;AACJ;AAaA,SAAS,KAAK,MAAM,OAAO,QAAQ,QAAQ,SAAS;AAC5C,MAAA,KAAK,YAAY,UAAU,QAAW;AACtC,aAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC7C;AAAA,EACJ;AAEA,QAAM,SAAS,CAAC,WAAW,SAAS,SAAS,UAAU,UAAU,UAAU,SAAS,UAAU,QAAQ,OAAO,KAAK;AAClH,QAAM,WAAW,KAAK;AAEtB,MAAI,OAAO,QAAQ,QAAQ,IAAI,IAAI;AAC/B,QAAI,CAAC,MAAM,QAAQ,EAAE,KAAK,GAAG;AAClB,aAAA,KAAK,OAAO,QAAQ,SAAS,MAAM,QAAQ,GAAG,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,IACnF;AAAA,EACO,WAAA,YAAY,OAAO,UAAU,KAAK,MAAM;AACxC,WAAA,KAAK,OAAO,QAAQ,SAAS,MAAM,QAAQ,GAAG,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,EACnF;AACJ;AAcA,SAAS,MAAM,MAAM,OAAO,QAAQ,QAAQ,SAAS;AAC3C,QAAA,MAAM,OAAO,KAAK,QAAQ;AAC1B,QAAA,MAAM,OAAO,KAAK,QAAQ;AAC1B,QAAA,MAAM,OAAO,KAAK,QAAQ;AAEhC,QAAM,WAAW;AACjB,MAAI,MAAM;AACV,MAAI,MAAM;AACJ,QAAA,MAAM,OAAO,UAAU;AACvB,QAAA,MAAM,OAAO,UAAU;AACvB,QAAA,MAAM,MAAM,QAAQ,KAAK;AAE/B,MAAI,KAAK;AACC,UAAA;AAAA,aACC,KAAK;AACN,UAAA;AAAA,aACC,KAAK;AACN,UAAA;AAAA,EACV;AAIA,MAAI,CAAC,KAAK;AACC,WAAA;AAAA,EACX;AAEA,MAAI,KAAK;AACL,UAAM,MAAM;AAAA,EAChB;AAEA,MAAI,KAAK;AAEL,UAAM,MAAM,QAAQ,UAAU,GAAG,EAAE;AAAA,EACvC;AAEA,MAAI,KAAK;AACD,QAAA,QAAQ,KAAK,KAAK;AACX,aAAA,KAAK,OAAO,QAAQ,SAAS,GAAG,EAAE,KAAK,KAAK,WAAW,KAAK,GAAG,CAAC;AAAA,IAC3E;AAAA,EAAA,WACO,OAAO,CAAC,OAAO,MAAM,KAAK,KAAK;AAC/B,WAAA,KAAK,OAAO,QAAQ,SAAS,GAAG,EAAE,KAAK,KAAK,WAAW,KAAK,GAAG,CAAC;AAAA,EAAA,WAChE,OAAO,CAAC,OAAO,MAAM,KAAK,KAAK;AAC/B,WAAA,KAAK,OAAO,QAAQ,SAAS,GAAG,EAAE,KAAK,KAAK,WAAW,KAAK,GAAG,CAAC;AAAA,EAAA,WAChE,OAAO,QAAQ,MAAM,KAAK,OAAO,MAAM,KAAK,MAAM;AACzD,WAAO,KAAK,OAAO,QAAQ,SAAS,GAAG,EAAE,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA,EACvF;AACJ;AAEA,MAAM,OAAO;AAab,SAAS,WAAW,MAAM,OAAO,QAAQ,QAAQ,SAAS;AACjD,OAAA,IAAI,IAAI,MAAM,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAA;AAEtD,MAAI,KAAK,IAAI,EAAE,QAAQ,KAAK,MAAM,IAAI;AAClC,WAAO,KAAK,OAAO,QAAQ,SAAS,IAAI,GAAG,KAAK,WAAW,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC;AAAA,EACrF;AACJ;AAcA,SAAS,UAAU,MAAM,OAAO,QAAQ,QAAQ,SAAS;AACrD,MAAI,KAAK,SAAS;AACV,QAAA,KAAK,mBAAmB,QAAQ;AAIhC,WAAK,QAAQ,YAAY;AAEzB,UAAI,CAAC,KAAK,QAAQ,KAAK,KAAK,GAAG;AACpB,eAAA,KAAK,OAAO,QAAQ,SAAS,QAAQ,UAAU,KAAK,WAAW,OAAO,KAAK,OAAO,CAAC;AAAA,MAC9F;AAAA,IACO,WAAA,OAAO,KAAK,YAAY,UAAU;AACzC,YAAM,WAAW,IAAI,OAAO,KAAK,OAAO;AAExC,UAAI,CAAC,SAAS,KAAK,KAAK,GAAG;AAChB,eAAA,KAAK,OAAO,QAAQ,SAAS,QAAQ,UAAU,KAAK,WAAW,OAAO,KAAK,OAAO,CAAC;AAAA,MAC9F;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,MAAM,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,SAAS;AACb;AAaA,SAAS,OAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,SAAS,CAAA;AACTG,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,OAAO,QAAQ,KAAK,CAAC,KAAK,UAAU;AACjD,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,SAAS,QAAQ;AAE7D,QAAI,CAAC,aAAa,OAAO,QAAQ,GAAG;AAChC,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC/C,YAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAChD,YAAM,QAAQ,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAE9C,UAAA,KAAK,eAAe,MAAM;AAC1B,cAAM,WAAW,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,MACzD;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASC,QAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,SAAS,CAAA;AACTD,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACnD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASE,QAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,SAAS,CAAA;AACTF,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,UAAU,IAAI;AACN,cAAA;AAAA,IACZ;AAEA,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC/C,YAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACpD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAAS,SAAS,MAAM,OAAO,UAAU,QAAQ,SAAS;AACtD,QAAM,SAAS,CAAA;AACTA,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACnD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASG,QAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,SAAS,CAAA;AACTH,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAE/C,QAAA,CAAC,aAAa,KAAK,GAAG;AACtB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACnD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASI,SAAQ,MAAM,OAAO,UAAU,QAAQ,SAAS;AACrD,QAAM,SAAS,CAAA;AACTJ,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC/C,YAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACpD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAAS,QAAQ,MAAM,OAAO,UAAU,QAAQ,SAAS;AACrD,QAAM,SAAS,CAAA;AACTA,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC/C,YAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACpD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASK,OAAM,MAAM,OAAO,UAAU,QAAQ,SAAS;AACnD,QAAM,SAAS,CAAA;AACTL,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,OAAO,OAAO,KAAK,CAAC,KAAK,UAAU;AAChD,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,SAAS,OAAO;AAE5D,QAAI,CAAC,aAAa,OAAO,OAAO,GAAG;AAC/B,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAC/C,YAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACpD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAASM,QAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,SAAS,CAAA;AACTN,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACnD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAEA,MAAM,SAAS;AAYf,SAAS,aAAa,MAAM,OAAO,UAAU,QAAQ,SAAS;AAC1D,QAAM,SAAS,CAAA;AACTA,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,UAAU,QAAW;AACrB,YAAM,MAAM,EAAE,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACtD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAgBA,SAAS,UAAU,MAAM,OAAO,UAAU,QAAQ,SAAS;AACvD,QAAM,SAAS,CAAA;AACTA,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,OAAO,QAAQ,KAAK,CAAC,KAAK,UAAU;AACjD,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAEnD,QAAI,CAAC,aAAa,OAAO,QAAQ,GAAG;AAChC,YAAM,QAAQ,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACtD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAEA,SAASO,MAAK,MAAM,OAAO,UAAU,QAAQ,SAAS;AAClD,QAAM,SAAS,CAAA;AACTP,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAE/C,QAAA,CAAC,aAAa,KAAK,GAAG;AAClB,UAAA;AAEA,UAAA,OAAO,UAAU,UAAU;AACd,qBAAA,IAAI,KAAK,KAAK;AAAA,MAAA,OACxB;AACU,qBAAA;AAAA,MACjB;AAEA,YAAM,KAAK,MAAM,YAAY,QAAQ,QAAQ,OAAO;AAEpD,UAAI,YAAY;AACZ,cAAM,MAAM,MAAM,WAAW,WAAW,QAAQ,QAAQ,OAAO;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAEA,SAAS,WAAW,MAAM,OAAO,UAAU,QAAQ,SAAS;AACxD,QAAM,SAAS,CAAA;AACf,QAAMH,QAAO,MAAM,QAAQ,KAAK,IAAI,UAAU,OAAO;AACrD,QAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,SAASA,KAAI;AACzD,WAAS,MAAM;AACnB;AAEA,SAAS,OAAO,MAAM,OAAO,UAAU,QAAQ,SAAS;AACpD,QAAM,WAAW,KAAK;AACtB,QAAM,SAAS,CAAA;AACTG,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,OAAO,QAAQ,KAAK,CAAC,KAAK,UAAU;AACjD,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,SAAS,QAAQ;AAE7D,QAAI,CAAC,aAAa,OAAO,QAAQ,GAAG;AAChC,YAAM,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,IACnD;AAAA,EACJ;AAEA,WAAS,MAAM;AACnB;AAaA,SAAS,IAAI,MAAM,OAAO,UAAU,QAAQ,SAAS;AACjD,QAAM,SAAS,CAAA;AACTA,QAAAA,YAAW,KAAK,YAAY,CAAC,KAAK,YAAY,OAAO,eAAe,KAAK,KAAK;AAEpF,MAAIA,WAAU;AACV,QAAI,aAAa,KAAK,KAAK,CAAC,KAAK,UAAU;AACvC,aAAO,SAAS;AAAA,IACpB;AAEA,UAAM,SAAS,MAAM,OAAO,QAAQ,QAAQ,OAAO;AAAA,EACvD;AAEA,WAAS,MAAM;AACnB;AAEA,MAAM,aAAa;AAAA,EACf;AAAA,EACA,QAAAC;AAAAA,EACA,QAAAC;AAAAA,EACA,SAAS;AAAA,EACT,QAAAC;AAAAA,EACA,SAAAC;AAAAA,EACA,OAAO;AAAA,EACP,OAAAC;AAAAA,EACA,QAAAC;AAAAA,EACA,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAAC;AAAAA,EACA,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AAAA,EACV;AACJ;AAEA,SAAS,cAAc;AACZ,SAAA;AAAA,IACH,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,MACF,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACH,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACH,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACL,UAAU;AAAA,IACd;AAAA,IACA,OAAO,SAAS,QAAQ;AACpB,YAAM,SAAS,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAC9C,aAAO,QAAQ,KAAK;AACb,aAAA;AAAA,IACX;AAAA,EAAA;AAER;AACA,MAAM,WAAW,YAAY;AAS7B,SAAS,OAAO,YAAY;AACxB,OAAK,QAAQ;AACb,OAAK,YAAY;AACjB,OAAK,OAAO,UAAU;AAC1B;AAEA,OAAO,YAAY;AAAA,EACf,UAAU,SAASC,UAAS,WAAW;AACnC,QAAI,WAAW;AACX,WAAK,YAAY,UAAU,YAAY,GAAG,SAAS;AAAA,IACvD;AAEA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ,SAAS,OAAOC,QAAO;AAC3B,QAAI,CAACA,QAAO;AACF,YAAA,IAAI,MAAM,yCAAyC;AAAA,IAC7D;AAEA,QAAI,OAAOA,WAAU,YAAY,MAAM,QAAQA,MAAK,GAAG;AAC7C,YAAA,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AAEA,SAAK,QAAQ;AACT,QAAA;AACA,QAAA;AAEJ,SAAK,KAAKA,QAAO;AACTA,UAAAA,OAAM,eAAe,CAAC,GAAG;AACzB,eAAOA,OAAM,CAAC;AACT,aAAA,MAAM,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,SAAS,SAAS,SAAS,GAAG,IAAI;AACxC,UAAM,QAAQ;AAEd,QAAI,MAAM,QAAQ;AACd,UAAI,CAAA;AAAA,IACR;AAEA,QAAI,OAAO,QAAQ;AACf,WAAK,SAASC,MAAK;AAAA,MAAA;AAAA,IACvB;AAEA,QAAI,SAAS;AACb,QAAI,UAAU;AACd,QAAI,WAAW;AAEX,QAAA,OAAO,YAAY,YAAY;AACpB,iBAAA;AACX,gBAAU,CAAA;AAAA,IACd;AAEI,QAAA,CAAC,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,EAAE,WAAW,GAAG;AACrD,UAAI,UAAU;AACD;MACb;AAEA,aAAO,QAAQ;IACnB;AAEA,aAAS,SAAS,SAAS;AACnB,UAAA;AACJ,UAAI,SAAS,CAAA;AACb,UAAI,SAAS,CAAA;AAEb,eAAS,IAAI,GAAG;AACR,YAAA,MAAM,QAAQ,CAAC,GAAG;AACd,cAAA;AAEJ,oBAAU,UAAU,QAAQ,OAAO,MAAM,SAAS,CAAC;AAAA,QAAA,OAChD;AACH,iBAAO,KAAK,CAAC;AAAA,QACjB;AAAA,MACJ;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAC7B,YAAA,QAAQ,CAAC,CAAC;AAAA,MAClB;AAEI,UAAA,CAAC,OAAO,QAAQ;AACP,iBAAA;AACA,iBAAA;AAAA,MAAA,OACN;AACH,iBAAS,mBAAmB,MAAM;AAAA,MACtC;AAEA,eAAS,QAAQ,MAAM;AAAA,IAC3B;AAEA,QAAI,QAAQ,UAAU;AACd,UAAA,aAAa,KAAK;AAEtB,UAAI,eAAe,UAAU;AACzB,qBAAa,YAAY;AAAA,MAC7B;AAEU,gBAAA,YAAY,QAAQ,QAAQ;AACtC,cAAQ,WAAW;AAAA,IAAA,OAChB;AACK,cAAA,WAAW,KAAK;IAC5B;AAEI,QAAA;AACA,QAAA;AACJ,UAAM,SAAS,CAAA;AACf,UAAM,OAAO,QAAQ,QAAQ,OAAO,KAAK,KAAK,KAAK;AAC9C,SAAA,QAAQ,CAAC,MAAM;AACV,YAAA,MAAM,MAAM,CAAC;AACnB,cAAQ,OAAO,CAAC;AACZ,UAAA,QAAQ,CAAC,MAAM;AACf,YAAI,OAAO;AAEP,YAAA,OAAO,KAAK,cAAc,YAAY;AACtC,cAAI,WAAW,SAAS;AACX,qBAAA,EAAE,GAAG;UAClB;AAEA,kBAAQ,OAAO,CAAC,IAAI,KAAK,UAAU,KAAK;AAAA,QAC5C;AAEI,YAAA,OAAO,SAAS,YAAY;AACrB,iBAAA;AAAA,YACH,WAAW;AAAA,UAAA;AAAA,QACf,OACG;AACI,iBAAA,EAAE,GAAG;QAChB;AAEK,aAAA,YAAY,MAAM,oBAAoB,IAAI;AAC/C,aAAK,QAAQ;AACR,aAAA,YAAY,KAAK,aAAa;AAC9B,aAAA,OAAO,MAAM,QAAQ,IAAI;AAE1B,YAAA,CAAC,KAAK,WAAW;AACjB;AAAA,QACJ;AAEA,eAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAA;AAClB,eAAA,CAAC,EAAE,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QAAA,CACV;AAAA,MAAA,CACJ;AAAA,IAAA,CACJ;AACD,UAAM,cAAc,CAAA;AACpB,WAAO,SAAS,QAAQ,SAAS,CAAC,MAAM,SAAS;AACvC,YAAA,EAAE,KAAS,IAAA;AACjB,UAAI,QAAQ,KAAK,SAAS,YAAY,KAAK,SAAS,aAAa,OAAO,KAAK,WAAW,YAAY,OAAO,KAAK,iBACpH;AACI,aAAO,SAAS,KAAK,YAAY,CAAC,KAAK,YAAY,KAAK;AACxD,WAAK,QAAQ,KAAK;AAET,eAAA,aAAa,KAAK,QAAQ;AACxB,eAAA,EAAE,GAAG,QAAQ,WAAW,GAAG,KAAK,SAAS,IAAI,GAAG;MAC3D;AAEA,eAAS,GAAG,GAAG;AACX,YAAI,MAAM,QAAQ;AACd,cAAI,CAAA;AAAA,QACR;AAEA,YAAI,SAAS;AAEb,YAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,mBAAS,CAAC,MAAM;AAAA,QACpB;AAEA,YAAI,CAAC,QAAQ,mBAAmB,OAAO,QAAQ;AACpC,iBAAA,QAAQ,oBAAoB,MAAM;AAAA,QAC7C;AAEI,YAAA,OAAO,UAAU,KAAK,SAAS;AAC/B,mBAAS,CAAA,EAAG,OAAO,KAAK,OAAO;AAAA,QACnC;AAEA,iBAAS,OAAO,IAAI,gBAAgB,IAAI,CAAC;AAErC,YAAA,QAAQ,SAAS,OAAO,QAAQ;AACpB,sBAAA,KAAK,KAAK,IAAI;AAC1B,iBAAO,KAAK,MAAM;AAAA,QACtB;AAEA,YAAI,CAAC,MAAM;AACP,eAAK,MAAM;AAAA,QAAA,OACR;AAIH,cAAI,KAAK,YAAY,CAAC,KAAK,OAAO;AAC9B,gBAAI,KAAK,SAAS;AACL,uBAAA,CAAA,EAAG,OAAO,KAAK,OAAO,EAAE,IAAI,gBAAgB,IAAI,CAAC;AAAA,YAAA,WACnD,QAAQ,OAAO;AACb,uBAAA,CAAC,QAAQ,MAAM,MAAM,OAAO,QAAQ,SAAS,UAAU,KAAK,KAAK,CAAC,CAAC;AAAA,YAAA,OACzE;AACH,uBAAS,CAAA;AAAA,YACb;AAEA,mBAAO,KAAK,MAAM;AAAA,UACtB;AAEA,cAAI,eAAe,CAAA;AAEnB,cAAI,KAAK,cAAc;AACR,uBAAA,KAAK,KAAK,OAAO;AACxB,kBAAI,KAAK,MAAM,eAAe,CAAC,GAAG;AACjB,6BAAA,CAAC,IAAI,KAAK;AAAA,cAC3B;AAAA,YACJ;AAAA,UACJ;AAEA,yBAAe,EAAE,GAAG,cAAc,GAAG,KAAK,KAAK;AAE/C,qBAAW,KAAK,cAAc;AACtB,gBAAA,aAAa,eAAe,CAAC,GAAG;AAChC,oBAAM,cAAc,MAAM,QAAQ,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1E,2BAAA,CAAC,IAAI,YAAY,IAAI,aAAa,KAAK,MAAM,CAAC,CAAC;AAAA,YAChE;AAAA,UACJ;AAEM,gBAAA,SAAS,IAAI,OAAO,YAAY;AAC/B,iBAAA,SAAS,QAAQ,QAAQ;AAE5B,cAAA,KAAK,KAAK,SAAS;AACd,iBAAA,KAAK,QAAQ,WAAW,QAAQ;AAChC,iBAAA,KAAK,QAAQ,QAAQ,QAAQ;AAAA,UACtC;AAEO,iBAAA,SAAS,KAAK,OAAO,KAAK,KAAK,WAAW,SAAS,CAAC,SAAS;AAChE,kBAAM,cAAc,CAAA;AAEhB,gBAAA,UAAU,OAAO,QAAQ;AACb,0BAAA,KAAK,MAAM,aAAa,MAAM;AAAA,YAC9C;AAEI,gBAAA,QAAQ,KAAK,QAAQ;AACT,0BAAA,KAAK,MAAM,aAAa,IAAI;AAAA,YAC5C;AAEK,iBAAA,YAAY,SAAS,cAAc,IAAI;AAAA,UAAA,CAC/C;AAAA,QACL;AAAA,MACJ;AAEI,UAAA;AAEJ,UAAI,KAAK,gBAAgB;AACf,cAAA,KAAK,eAAe,MAAM,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO;AAAA,MAAA,WAC7D,KAAK,WAAW;AACjB,cAAA,KAAK,UAAU,MAAM,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO;AAE/D,YAAI,QAAQ,MAAM;AACX;QAAA,WACI,QAAQ,OAAO;AACtB,aAAG,KAAK,WAAW,GAAG,KAAK,KAAK,QAAQ;AAAA,QAAA,WACjC,eAAe,OAAO;AAC7B,aAAG,GAAG;AAAA,QAAA,WACC,eAAe,OAAO;AAC7B,aAAG,IAAI,OAAO;AAAA,QAClB;AAAA,MACJ;AAEI,UAAA,OAAO,IAAI,MAAM;AACb,YAAA,KAAK,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;AAAA,MACrC;AAAA,IACJ,GAAG,CAAC,YAAY;AACZ,eAAS,OAAO;AAAA,IAAA,CACnB;AAAA,EACL;AAAA,EACA,SAAS,SAAS,QAAQ,MAAM;AAC5B,QAAI,KAAK,SAAS,UAAa,KAAK,mBAAmB,QAAQ;AAC3D,WAAK,OAAO;AAAA,IAChB;AAEI,QAAA,OAAO,KAAK,cAAc,cAAc,KAAK,QAAQ,CAAC,WAAW,eAAe,KAAK,IAAI,GAAG;AAC5F,YAAM,IAAI,MAAM,OAAO,wBAAwB,KAAK,IAAI,CAAC;AAAA,IAC7D;AAEA,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EACA,qBAAqB,SAAS,oBAAoB,MAAM;AAChD,QAAA,OAAO,KAAK,cAAc,YAAY;AACtC,aAAO,KAAK;AAAA,IAChB;AAEM,UAAA,OAAO,OAAO,KAAK,IAAI;AACvB,UAAA,eAAe,KAAK,QAAQ,SAAS;AAE3C,QAAI,iBAAiB,IAAI;AAChB,WAAA,OAAO,cAAc,CAAC;AAAA,IAC/B;AAEA,QAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,YAAY;AAC7C,aAAO,WAAW;AAAA,IACtB;AAEA,WAAO,WAAW,KAAK,QAAQ,IAAI,CAAC,KAAK;AAAA,EAC7C;AACJ;AAEA,OAAO,WAAW,SAAS,SAASb,OAAM,WAAW;AAC7C,MAAA,OAAO,cAAc,YAAY;AAC3B,UAAA,IAAI,MAAM,kEAAkE;AAAA,EACtF;AAEA,aAAWA,KAAI,IAAI;AACvB;AAEA,OAAO,UAAU;AACjB,OAAO,WAAW;;"}