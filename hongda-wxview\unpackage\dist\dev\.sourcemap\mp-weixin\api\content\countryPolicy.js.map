{"version": 3, "file": "countryPolicy.js", "sources": ["api/content/countryPolicy.js"], "sourcesContent": ["// [!] 修改：为了清晰，我们将导入的对象命名为 http\r\nimport http from '@/utils/request'\r\n\r\n// 定义API的基础路径\r\nconst BASE_URL = '/content/countryPolicy'\r\n\r\n/**\r\n * 查询国别政策文章列表\r\n * @param {object} query - 查询参数\r\n * @param {number} query.countryId - 国家ID\r\n * @param {string} query.policyType - 政策大类 (investment, customs, tax)\r\n * @param {number} [query.pageNum] - 页码\r\n * @param {number} [query.pageSize] - 每页数量\r\n * @returns {Promise<AjaxResult>}\r\n */\r\n/**\r\n * 查询国别政策文章列表\r\n * @param {object} query - 查询参数\r\n * @param {number} query.countryId - 国家ID\r\n * @param {string} query.policyType - 政策大类 (investment, customs, tax)\r\n * @param {number} [query.pageNum] - 页码\r\n * @param {number} [query.pageSize] - 每页数量\r\n * @returns {Promise<AjaxResult>}\r\n */\r\nexport function listCountryPolicyArticle(query) {\r\n    // [!] 修改：直接调用 http 对象的 get 方法\r\n    return http.get(`${BASE_URL}/list`, query);\r\n}\r\n\r\n/**\r\n * 获取国别政策文章详细信息\r\n * @param {number} articleId - 文章ID\r\n * @returns {Promise<AjaxResult>}\r\n */\r\nexport function getCountryPolicyArticle(articleId) {\r\n    // [!] 修改：调用 http 对象内部的 request 方法\r\n    return http.request({\r\n        url: `${BASE_URL}/${articleId}`,\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n// 注意：目前我们的设计中，小程序端只需要查询功能。\r\n// 如果未来需要在小程序端进行新增、修改、删除等操作，可以在此文件中继续添加对应的API函数。\r\n"], "names": ["http"], "mappings": ";;AAIA,MAAM,WAAW;AAoBV,SAAS,yBAAyB,OAAO;AAE5C,SAAOA,cAAAA,KAAK,IAAI,GAAG,QAAQ,SAAS,KAAK;AAC7C;AAOO,SAAS,wBAAwB,WAAW;AAE/C,SAAOA,cAAAA,KAAK,QAAQ;AAAA,IAChB,KAAK,GAAG,QAAQ,IAAI,SAAS;AAAA,IAC7B,QAAQ;AAAA,EAChB,CAAK;AACL;;;"}