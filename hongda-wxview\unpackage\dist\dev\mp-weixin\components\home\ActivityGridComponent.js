"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_image = require("../../utils/image.js");
const utils_tools = require("../../utils/tools.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_loading_icon2 = common_vendor.resolveComponent("up-loading-icon");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  (_easycom_up_icon2 + _easycom_up_loading_icon2 + _easycom_up_empty2)();
}
const _easycom_up_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_loading_icon + _easycom_up_empty)();
}
const _sfc_main = {
  __name: "ActivityGridComponent",
  setup(__props) {
    const activityList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const titleParts = common_vendor.computed(() => {
      const title = "精选活动";
      if (title.length > 2) {
        return { main: title.slice(0, 2), gradient: title.slice(2) };
      }
      return { main: title, gradient: "" };
    });
    const icons = {
      time: "",
      location: ""
    };
    const parseSafeDate = (input) => {
      if (!input)
        return null;
      if (input instanceof Date) {
        return isNaN(input.getTime()) ? null : input;
      }
      if (typeof input === "number") {
        const d = new Date(input);
        return isNaN(d.getTime()) ? null : d;
      }
      if (typeof input === "string") {
        let s = input.trim();
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(s)) {
          s = s.replace(" ", "T");
        }
        let d = new Date(s);
        if (isNaN(d.getTime())) {
          const m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);
          if (m) {
            const y = m[1];
            const mo = m[2];
            const day = m[3];
            const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || "00"}` : "";
            d = /* @__PURE__ */ new Date(`${y}/${mo}/${day}${rest}`);
          }
        }
        return isNaN(d.getTime()) ? null : d;
      }
      return null;
    };
    const formatActivityDate = (dateString) => {
      if (!dateString)
        return "";
      const date = parseSafeDate(dateString);
      if (!date)
        return "";
      const month = date.getMonth() + 1;
      const day = String(date.getDate()).padStart(2, "0");
      return `${month}.${day}`;
    };
    const getWeekday = (dateString) => {
      if (!dateString)
        return "";
      const date = parseSafeDate(dateString);
      if (!date)
        return "";
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return weekdays[date.getDay()];
    };
    const calculateRemaining = (maxParticipants, registeredCount) => {
      if (!maxParticipants || maxParticipants <= 0) {
        return "不限";
      }
      const remaining = maxParticipants - (registeredCount || 0);
      return remaining > 0 ? remaining : 0;
    };
    const formatActivityLocation = (item) => {
      if (item.city && item.city.trim()) {
        return item.city.trim();
      }
      return "待定";
    };
    const fetchHotEvents = async () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const response = await api_data_event.getHotEventListApi(8);
        const { rows = [] } = response;
        const registeringRows = rows.filter((item) => utils_tools.getStatusClass(item.status) !== "ended").slice(0, 4);
        activityList.value = registeringRows.map((item) => ({
          id: item.id,
          title: item.title,
          image: utils_image.getFullImageUrl(item.coverImageUrl),
          location: formatActivityLocation(item),
          status: utils_tools.formatEventStatus(item.status),
          statusClass: utils_tools.getStatusClass(item.status) === "ended" ? "status-ended" : "status-registering",
          dateTime: formatActivityDate(item.startTime),
          weekday: getWeekday(item.startTime),
          remainingSpots: calculateRemaining(item.maxParticipants, item.registeredCount)
        }));
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取热门活动失败:", error);
        activityList.value = [
          {
            id: 1,
            title: "2025 Ozen卖家增长峰会·北京站",
            status: "报名中",
            statusClass: "status-registering",
            location: "北京",
            image: "https://via.placeholder.com/170x100/3c9cff/fff?text=活动1",
            dateTime: "7.15",
            weekday: "周二",
            remainingSpots: "29"
          }
        ];
        common_vendor.index.showToast({
          title: "获取活动数据失败，显示示例数据",
          icon: "none",
          duration: 2e3
        });
      } finally {
        isLoading.value = false;
      }
    };
    const goToEventDetail = (activity) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${activity.id}`
      });
    };
    const goToEventList = () => {
      common_vendor.index.switchTab({
        url: "/pages/event/index",
        fail: () => {
          common_vendor.index.navigateTo({ url: "/pages/event/index" });
        }
      });
    };
    common_vendor.onMounted(() => {
      try {
        const assets = common_vendor.index.getStorageSync("staticAssets");
        icons.time = (assets == null ? void 0 : assets.detail_icon_time) || "";
        icons.location = (assets == null ? void 0 : assets.detail_icon_location) || "";
      } catch (e) {
      }
      fetchHotEvents();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(titleParts.value.main),
        b: common_vendor.t(titleParts.value.gradient),
        c: common_vendor.p({
          name: "arrow-right",
          size: "14"
        }),
        d: common_vendor.o(goToEventList),
        e: isLoading.value
      }, isLoading.value ? {
        f: common_vendor.p({
          mode: "spinner",
          size: "40"
        })
      } : {
        g: common_vendor.f(activityList.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.status),
            c: common_vendor.n(item.statusClass),
            d: common_vendor.t(item.title),
            e: common_vendor.t(item.dateTime),
            f: common_vendor.t(item.weekday),
            g: common_vendor.t(item.location),
            h: common_vendor.t(item.remainingSpots),
            i: item.id || index,
            j: common_vendor.o(($event) => goToEventDetail(item), item.id || index)
          };
        }),
        h: icons.time,
        i: icons.location
      }, {
        j: !isLoading.value && activityList.value.length === 0
      }, !isLoading.value && activityList.value.length === 0 ? {
        k: common_vendor.p({
          mode: "data",
          text: "暂无精选活动",
          textColor: "#909399",
          iconSize: "80"
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-03377cb7"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
