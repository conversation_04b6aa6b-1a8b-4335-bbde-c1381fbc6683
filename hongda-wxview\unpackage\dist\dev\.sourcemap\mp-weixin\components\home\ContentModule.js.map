{"version": 3, "file": "ContentModule.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9Db250ZW50TW9kdWxlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <!-- 【修改】根容器不再需要额外样式，由父组件 policy_detail.vue 控制 -->\r\n  <view class=\"content-module-container\">\r\n    <!-- 【修改】使用自定义的 Tab 栏，并包裹在 .sub-tabs-container 中 -->\r\n    <view class=\"sub-tabs-container\">\r\n      <scroll-view class=\"sub-tabs-wrapper\" scroll-x=\"true\" :show-scrollbar=\"false\">\r\n        <view\r\n            v-for=\"(category, index) in categoryList\"\r\n            :key=\"index\"\r\n            class=\"sub-tab-item\"\r\n            :class=\"{ active: activeCategoryIndex === index }\"\r\n            @click=\"onCategoryChange(index)\"\r\n        >\r\n          {{ category.name }}\r\n        </view>\r\n      </scroll-view>\r\n      <!-- 分隔线 -->\r\n      <view class=\"divider\"></view>\r\n    </view>\r\n\r\n    <!-- 【修改】资讯列表容器，背景色为 #FFFFFF -->\r\n    <view class=\"article-list-container\">\r\n      <view v-if=\"loading\" class=\"loading-state\">\r\n        <u-loading-icon mode=\"circle\" text=\"加载中...\" size=\"24\"></u-loading-icon>\r\n      </view>\r\n      <view v-else-if=\"filteredArticleList.length > 0\" class=\"article-list\">\r\n        <view\r\n            v-for=\"article in filteredArticleList\"\r\n            :key=\"article.articleId\"\r\n            class=\"article-item\"\r\n            @click=\"goToArticleDetail(article.articleId)\"\r\n        >\r\n          <view class=\"dot\"></view>\r\n          <text class=\"article-title\">{{ article.title }}</text>\r\n          <u-icon name=\"arrow-right\" color=\"#BFBFBF\" size=\"16\"></u-icon>\r\n        </view>\r\n      </view>\r\n      <view v-else class=\"empty-state\">\r\n        <u-empty mode=\"list\" text=\"暂无相关内容\"></u-empty>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch, onMounted, computed } from 'vue';\r\nimport { listCountryPolicyArticle } from '@/api/content/countryPolicy.js';\r\n\r\nconst props = defineProps({\r\n  countryId: {\r\n    type: [Number, String],\r\n    required: true\r\n  },\r\n  policyType: {\r\n    type: String,\r\n    required: true\r\n  }\r\n});\r\n\r\nconst allArticles = ref([]);\r\nconst categoryList = ref([]);\r\nconst activeCategoryIndex = ref(0);\r\nconst loading = ref(true);\r\n\r\nconst filteredArticleList = computed(() => {\r\n  if (loading.value || categoryList.value.length === 0 || !categoryList.value[activeCategoryIndex.value]) {\r\n    return [];\r\n  }\r\n  const currentCategoryName = categoryList.value[activeCategoryIndex.value].name;\r\n  // 如果是“全部”，则返回所有文章\r\n  if (currentCategoryName === '全部') {\r\n    return allArticles.value;\r\n  }\r\n  // 否则，按分类名称筛选\r\n  return allArticles.value.filter(item => item.categoryName === currentCategoryName);\r\n});\r\n\r\nconst fetchData = async () => {\r\n  loading.value = true;\r\n  if (!props.countryId || !props.policyType) {\r\n    allArticles.value = [];\r\n    categoryList.value = [];\r\n    loading.value = false;\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const res = await listCountryPolicyArticle({\r\n      countryId: props.countryId,\r\n      policyType: props.policyType,\r\n      pageNum: 1,\r\n      pageSize: 100\r\n    });\r\n\r\n    allArticles.value = res.rows || [];\r\n\r\n    // 【恢复】从接口动态获取分类，并添加“全部”选项\r\n    const categories = [...new Set(allArticles.value.map(item => item.categoryName).filter(Boolean))];\r\n    categoryList.value = [{ name: '全部' }, ...categories.map(name => ({ name }))];\r\n\r\n    // 重置选中项为“全部”\r\n    activeCategoryIndex.value = 0;\r\n\r\n  } catch (error) {\r\n    console.error(`获取${props.policyType}政策文章失败:`, error);\r\n    allArticles.value = [];\r\n    categoryList.value = [];\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst onCategoryChange = (index) => {\r\n  activeCategoryIndex.value = index;\r\n};\r\n\r\nconst goToArticleDetail = (articleId) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_country/policy_detail?id=${articleId}`\r\n  });\r\n};\r\n\r\nwatch(() => props.policyType, (newVal, oldVal) => {\r\n  if (newVal && newVal !== oldVal) {\r\n    fetchData();\r\n  }\r\n});\r\n\r\nonMounted(() => {\r\n  fetchData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content-module-container {\r\n  // 容器本身不需要额外样式\r\n}\r\n\r\n.sub-tabs-container {\r\n  position: relative;\r\n}\r\n\r\n.sub-tabs-wrapper {\r\n  display: flex;\r\n  white-space: nowrap; // 允许横向滚动\r\n}\r\n\r\n.sub-tab-item {\r\n  display: inline-block; // 改为 inline-block 以适应滚动\r\n  position: relative;\r\n  font-size: 28rpx;\r\n  color: #66666E;\r\n  padding: 0 4rpx 16rpx; // 左右留出一点空间\r\n  margin-right: 40rpx;\r\n  cursor: pointer;\r\n  transition: color 0.3s;\r\n  flex-shrink: 0; // 防止被压缩\r\n\r\n  &.active {\r\n    color: #023F98;\r\n    font-weight: bold;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 6rpx;\r\n      background-color: #023F98;\r\n      border-radius: 3rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.divider {\r\n  height: 1rpx;\r\n  background-color: #E5E5E5;\r\n  // 通过相对定位的父元素和绝对定位的自身，实现tab下划线和分割线的分离\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n}\r\n\r\n.article-list-container {\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  margin-top: 20rpx;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.article-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .dot {\r\n    width: 12rpx;\r\n    height: 12rpx;\r\n    background-color: #4E8AF3;\r\n    border-radius: 50%;\r\n    margin-right: 20rpx;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .article-title {\r\n    flex: 1;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    margin-right: 20rpx;\r\n  }\r\n}\r\n\r\n.loading-state, .empty-state {\r\n  padding: 60rpx 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "listCountryPolicyArticle", "uni", "watch", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,UAAM,QAAQ;AAWd,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAC3B,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AACjC,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAExB,UAAM,sBAAsBC,cAAQ,SAAC,MAAM;AACzC,UAAI,QAAQ,SAAS,aAAa,MAAM,WAAW,KAAK,CAAC,aAAa,MAAM,oBAAoB,KAAK,GAAG;AACtG,eAAO;MACR;AACD,YAAM,sBAAsB,aAAa,MAAM,oBAAoB,KAAK,EAAE;AAE1E,UAAI,wBAAwB,MAAM;AAChC,eAAO,YAAY;AAAA,MACpB;AAED,aAAO,YAAY,MAAM,OAAO,UAAQ,KAAK,iBAAiB,mBAAmB;AAAA,IACnF,CAAC;AAED,UAAM,YAAY,YAAY;AAC5B,cAAQ,QAAQ;AAChB,UAAI,CAAC,MAAM,aAAa,CAAC,MAAM,YAAY;AACzC,oBAAY,QAAQ;AACpB,qBAAa,QAAQ;AACrB,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAED,UAAI;AACF,cAAM,MAAM,MAAMC,mDAAyB;AAAA,UACzC,WAAW,MAAM;AAAA,UACjB,YAAY,MAAM;AAAA,UAClB,SAAS;AAAA,UACT,UAAU;AAAA,QAChB,CAAK;AAED,oBAAY,QAAQ,IAAI,QAAQ,CAAA;AAGhC,cAAM,aAAa,CAAC,GAAG,IAAI,IAAI,YAAY,MAAM,IAAI,UAAQ,KAAK,YAAY,EAAE,OAAO,OAAO,CAAC,CAAC;AAChG,qBAAa,QAAQ,CAAC,EAAE,MAAM,KAAI,GAAI,GAAG,WAAW,IAAI,WAAS,EAAE,KAAM,EAAC,CAAC;AAG3E,4BAAoB,QAAQ;AAAA,MAE7B,SAAQ,OAAO;AACdC,sBAAAA,iEAAc,KAAK,MAAM,UAAU,WAAW,KAAK;AACnD,oBAAY,QAAQ;AACpB,qBAAa,QAAQ;MACzB,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,UAAM,oBAAoB,CAAC,cAAc;AACvCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6CAA6C,SAAS;AAAA,MAC/D,CAAG;AAAA,IACH;AAEAC,kBAAK,MAAC,MAAM,MAAM,YAAY,CAAC,QAAQ,WAAW;AAChD,UAAI,UAAU,WAAW,QAAQ;AAC/B;MACD;AAAA,IACH,CAAC;AAEDC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjID,GAAG,gBAAgB,SAAS;"}