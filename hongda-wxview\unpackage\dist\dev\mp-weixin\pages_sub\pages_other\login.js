"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_user = require("../../api/data/user.js");
const pages_sub_pages_other_api_data_policy = require("./api/data/policy.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  _easycom_up_navbar2();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
if (!Math) {
  _easycom_up_navbar();
}
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "LoginPage"
}, {
  __name: "login",
  setup(__props) {
    const isAgreementChecked = common_vendor.ref(false);
    const loginBgUrl = common_vendor.ref("");
    const logoHdUrl = common_vendor.ref("");
    const policyVersions = common_vendor.ref({
      userAgreement: null,
      privacyPolicy: null
    });
    const toggleAgreement = () => {
      isAgreementChecked.value = !isAgreementChecked.value;
    };
    const onGetPhoneNumber = async (e) => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:94", "=== 开始微信手机号快捷登录流程 ===");
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:95", "授权事件详情:", e);
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:96", "e.detail:", e.detail);
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:97", "e.detail.code:", e.detail.code);
      if (!e.detail.code) {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:101", "微信未返回授权code");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:102", "错误信息:", e.detail.errMsg);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:103", "可能原因：1. 用户拒绝授权 2. 配置问题 3. 网络异常");
        common_vendor.index.showToast({
          title: "获取手机号失败，请重试",
          icon: "none"
        });
        return;
      }
      const phoneCode = e.detail.code;
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:112", "获取到微信手机号授权码:", phoneCode);
      common_vendor.index.showLoading({ title: "正在登录..." });
      try {
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("userInfo");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:121", "开始执行uni.login...");
        const loginRes = await common_vendor.index.login();
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:123", "uni.login结果:", loginRes);
        const loginCode = loginRes.code;
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:125", "获得loginCode:", loginCode);
        const res = await api_data_user.wxLoginApi(loginCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:129", "后端登录成功:", res);
        const token = res.token;
        common_vendor.index.setStorageSync("token", token);
        common_vendor.index.setStorageSync("userInfo", res.userInfo);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:137", "使用phoneCode:", phoneCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:138", "使用token:", token);
        const phoneRes = await api_data_user.getPhoneNumberApi(phoneCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:140", "获取手机号成功:", phoneRes);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:143", "开始更新本地用户信息...");
        try {
          const userInfo = common_vendor.index.getStorageSync("userInfo");
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:146", "当前userInfo:", userInfo);
          if (!userInfo) {
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:149", "userInfo为空，创建新的用户信息对象");
            const newUserInfo = {
              phoneNumber: phoneRes.phoneNumber
            };
            common_vendor.index.setStorageSync("userInfo", newUserInfo);
          } else {
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:155", "更新现有userInfo的手机号");
            userInfo.phoneNumber = phoneRes.phoneNumber;
            common_vendor.index.setStorageSync("userInfo", userInfo);
          }
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:160", "用户信息更新完成");
        } catch (updateError) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:162", "更新用户信息时出错:", updateError);
          throw updateError;
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:166", "登录流程完全成功，最终保存的数据:");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:167", "- token:", common_vendor.index.getStorageSync("token"));
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:168", "- userInfo:", common_vendor.index.getStorageSync("userInfo"));
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:170", "准备隐藏加载提示...");
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:173", "准备显示成功提示...");
        common_vendor.index.showToast({ title: "登录成功", icon: "success" });
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:177", "开始上报协议同意记录...");
        try {
          await reportPolicyAcceptance();
        } catch (error) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:181", "协议同意记录上报异常:", error);
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:185", "设置延迟返回定时器...");
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:187", "定时器触发，准备返回上一页...");
          try {
            const loginBackPage = common_vendor.index.getStorageSync("loginBackPage");
            if (loginBackPage) {
              common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:193", "检测到指定的返回页面:", loginBackPage);
              common_vendor.index.navigateBack({
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:197", "成功返回上一页");
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:200", "返回上一页失败，尝试直接跳转到指定页面:", err);
                  if (loginBackPage.startsWith("/pages/")) {
                    common_vendor.index.redirectTo({
                      url: loginBackPage,
                      fail: () => {
                        common_vendor.index.switchTab({ url: "/pages/index/index" });
                      }
                    });
                  }
                }
              });
            } else {
              common_vendor.index.navigateBack({
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:217", "成功返回上一页");
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:220", "返回上一页失败，跳转到首页:", err);
                  common_vendor.index.switchTab({ url: "/pages/index/index" });
                }
              });
            }
          } catch (e2) {
            common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:227", "检查返回页面标记失败:", e2);
            common_vendor.index.navigateBack({
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:231", "返回上一页失败，跳转到首页:", err);
                common_vendor.index.switchTab({ url: "/pages/index/index" });
              }
            });
          }
        }, 2e3);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:238", "登录方法执行完成，等待定时器触发返回...");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:241", "登录过程中发生错误:", error);
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:242", "错误详情:", {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        common_vendor.index.hideLoading();
        const errorMessage = error.message || "网络请求失败";
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:251", "显示错误提示:", errorMessage);
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      }
    };
    const handleLoginClick = () => {
      if (!isAgreementChecked.value) {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:266", "用户未同意协议");
        common_vendor.index.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none"
        });
      }
    };
    const goToUserAgreement = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:276", "点击用户协议链接");
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/policy?type=user_agreement",
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:280", "成功跳转到用户协议页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:283", "跳转用户协议页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const goToPrivacyPolicy = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:295", "点击隐私政策链接");
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/policy?type=privacy_policy",
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:299", "成功跳转到隐私政策页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:302", "跳转隐私政策页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const loadPolicyVersions = async () => {
      try {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:319", "开始加载协议版本信息...");
        const [userAgreementRes, privacyPolicyRes] = await Promise.all([
          pages_sub_pages_other_api_data_policy.getLatestPolicyApi("user_agreement"),
          pages_sub_pages_other_api_data_policy.getLatestPolicyApi("privacy_policy")
        ]);
        if (userAgreementRes && userAgreementRes.code === 200 && userAgreementRes.data) {
          policyVersions.value.userAgreement = userAgreementRes.data.version;
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:329", "用户协议版本:", userAgreementRes.data.version);
        }
        if (privacyPolicyRes && privacyPolicyRes.code === 200 && privacyPolicyRes.data) {
          policyVersions.value.privacyPolicy = privacyPolicyRes.data.version;
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:334", "隐私政策版本:", privacyPolicyRes.data.version);
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:337", "协议版本信息加载完成:", policyVersions.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:339", "加载协议版本信息失败:", error);
        policyVersions.value.userAgreement = "1.0.0";
        policyVersions.value.privacyPolicy = "1.0.0";
      }
    };
    const reportPolicyAcceptance = async () => {
      try {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:349", "=== 开始上报协议同意记录 ===");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:350", "当前协议版本信息:", policyVersions.value);
        if (!policyVersions.value.userAgreement || !policyVersions.value.privacyPolicy) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:354", "协议版本信息不完整，尝试重新加载...");
          await loadPolicyVersions();
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:356", "重新加载后的协议版本信息:", policyVersions.value);
        }
        const reports = [];
        if (policyVersions.value.userAgreement) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:363", "准备上报用户协议，版本:", policyVersions.value.userAgreement);
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("user_agreement", policyVersions.value.userAgreement)
          );
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:368", "用户协议版本为空，使用默认版本1.0.0");
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("user_agreement", "1.0.0")
          );
        }
        if (policyVersions.value.privacyPolicy) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:376", "准备上报隐私政策，版本:", policyVersions.value.privacyPolicy);
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("privacy_policy", policyVersions.value.privacyPolicy)
          );
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:381", "隐私政策版本为空，使用默认版本1.0.0");
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("privacy_policy", "1.0.0")
          );
        }
        if (reports.length === 0) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:388", "无法创建上报请求");
          return;
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:392", `准备并行执行${reports.length}个上报请求...`);
        const results = await Promise.all(reports);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:396", "协议同意记录上报原始结果:", results);
        let successCount = 0;
        results.forEach((result, index) => {
          const type = index === 0 ? "用户协议" : "隐私政策";
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:402", `${type}上报结果:`, result);
          if (result && result.code === 200) {
            successCount++;
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:406", `${type}同意记录上报成功`);
          } else {
            common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:408", `${type}同意记录上报失败:`, result);
          }
        });
        if (successCount === results.length) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:413", "所有协议同意记录上报成功");
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:415", `部分协议同意记录上报失败，成功数量: ${successCount}/${results.length}`);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:419", "上报协议同意记录过程中发生异常:", error);
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:420", "错误详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    common_vendor.onLoad(() => {
      try {
        const assets = common_vendor.index.getStorageSync("staticAssets");
        loginBgUrl.value = (assets == null ? void 0 : assets["login-bg"]) || "";
        logoHdUrl.value = (assets == null ? void 0 : assets["logo-hd"]) || "";
      } catch (e) {
      }
      loadPolicyVersions();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loginBgUrl.value,
        b: common_vendor.p({
          title: "登录",
          autoBack: true,
          safeAreaInsetTop: true,
          fixed: true,
          placeholder: true,
          bgColor: "transparent",
          zIndex: 99,
          leftIconColor: "#333333",
          titleStyle: {
            fontFamily: "Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",
            fontWeight: "normal",
            fontSize: "32rpx",
            color: "#000000",
            lineHeight: "44rpx"
          }
        }),
        c: logoHdUrl.value,
        d: isAgreementChecked.value ? "getPhoneNumber" : "",
        e: common_vendor.o(onGetPhoneNumber),
        f: common_vendor.o(handleLoginClick),
        g: isAgreementChecked.value
      }, isAgreementChecked.value ? {} : {}, {
        h: isAgreementChecked.value ? 1 : "",
        i: common_vendor.o(goToUserAgreement),
        j: common_vendor.o(goToPrivacyPolicy),
        k: common_vendor.o(toggleAgreement)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-de5c63ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/login.js.map
