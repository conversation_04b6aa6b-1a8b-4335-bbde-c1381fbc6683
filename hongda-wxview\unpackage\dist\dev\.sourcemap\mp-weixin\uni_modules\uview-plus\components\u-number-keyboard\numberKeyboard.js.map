{"version": 3, "file": "numberKeyboard.js", "sources": ["uni_modules/uview-plus/components/u-number-keyboard/numberKeyboard.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:08:05\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberKeyboard.js\r\n */\r\nexport default {\r\n    // 数字键盘\r\n    numberKeyboard: {\r\n        mode: 'number',\r\n        dotDisabled: false,\r\n        random: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,iBAAA;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACX;AACL;;"}