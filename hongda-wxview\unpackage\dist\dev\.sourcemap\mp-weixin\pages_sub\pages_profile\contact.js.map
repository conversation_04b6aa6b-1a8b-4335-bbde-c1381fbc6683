{"version": 3, "file": "contact.js", "sources": ["pages_sub/pages_profile/contact.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX3Byb2ZpbGVcY29udGFjdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <!-- [无变化] 自定义导航栏 -->\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">联系顾问</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <!-- [无变化] 加载状态 -->\r\n      <view v-if=\"loading\" class=\"loading-state\">\r\n        <uni-load-more status=\"loading\" :show-icon=\"true\"></uni-load-more>\r\n      </view>\r\n\r\n      <view v-if=\"!loading && consultant\" class=\"content-wrapper\">\r\n        <view class=\"header-text\">\r\n          Hi~ 很高兴为您服务\r\n        </view>\r\n\r\n        <view class=\"card\">\r\n          <view class=\"consultant-info\">\r\n            <!-- ================================================== -->\r\n            <!-- (1/2) 修改点：使用 computed 属性来绑定完整的图片URL -->\r\n            <!-- ================================================== -->\r\n            <image class=\"avatar\" :src=\"displayAvatarUrl\" mode=\"aspectFill\" @error=\"onImageError('avatar')\"></image>\r\n            <view class=\"details\">\r\n              <view class=\"name-badge\">\r\n                <text class=\"name\">{{ consultant.name }}</text>\r\n                <view class=\"badge\">官方</view>\r\n              </view>\r\n              <text class=\"intro\">{{ consultant.introduction }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"more-questions\">\r\n            更多问题，请添加顾问在线共解答\r\n          </view>\r\n\r\n          <view class=\"qr-code-section\">\r\n            <!-- ================================================== -->\r\n            <!-- (2/2) 修改点：使用 computed 属性来绑定完整的图片URL -->\r\n            <!-- ================================================== -->\r\n            <image class=\"qr-code\" :src=\"displayQrCodeUrl\" mode=\"aspectFit\" @error=\"onImageError('qrCode')\" @click=\"previewQrCode\"></image>\r\n            <text class=\"qr-code-tip\">长按识别二维码</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- [无变化] 空状态 -->\r\n      <view v-if=\"!loading && !consultant\" class=\"empty-state\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"empty-text\">暂无在线顾问，请稍后再试</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n// ==================================================\r\n// (1/3) 新增代码：引入 computed 和图片处理函数\r\n// ==================================================\r\nimport { ref, computed } from 'vue';\r\nimport { getFullImageUrl } from '@/utils/image.js'; // 引入图片路径处理函数\r\n\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n// [注意] 请确保这里的路径是正确的，根据您项目结构可能是 @/api/consultant.js\r\nimport { getDisplayConsultantApi } from '@/pages_sub/pages_profile/api/platform/consultant.js';\r\n\r\n// --- [无变化] 自定义导航栏相关逻辑 ---\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n\r\n// --- [无变化] 核心业务逻辑 ---\r\nconst loading = ref(true);\r\nconst consultant = ref(null);\r\n\r\n// ==================================================\r\n// (2/3) 新增代码：使用 computed 创建响应式的完整图片URL\r\n// ==================================================\r\nconst displayAvatarUrl = computed(() => {\r\n  return consultant.value ? getFullImageUrl(consultant.value.avatarUrl) : '';\r\n});\r\n\r\nconst displayQrCodeUrl = computed(() => {\r\n  return consultant.value ? getFullImageUrl(consultant.value.qrCodeUrl) : '';\r\n});\r\n// ==================================================\r\n\r\nconst fetchConsultantData = async () => {\r\n  try {\r\n    const res = await getDisplayConsultantApi();\r\n    if (res.code === 200 && res.data) {\r\n      consultant.value = res.data;\r\n    } else {\r\n      consultant.value = null;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取顾问信息失败:', error);\r\n    consultant.value = null;\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst previewQrCode = () => {\r\n  // ==================================================\r\n  // (3/3) 修改点：使用处理过的URL进行预览\r\n  // ==================================================\r\n  if (displayQrCodeUrl.value) {\r\n    uni.previewImage({\r\n      urls: [displayQrCodeUrl.value]\r\n    });\r\n  }\r\n};\r\n\r\nconst onImageError = (type) => {\r\n  console.error(`${type} image failed to load.`);\r\n  // 这里的逻辑可以保留，作为最终的保障\r\n  if (type === 'avatar') {\r\n    consultant.value.avatarUrl = '/static/images/default-avatar.png';\r\n  } else if (type === 'qrCode') {\r\n    consultant.value.qrCodeUrl = '/static/images/default-qrcode.png';\r\n  }\r\n};\r\n\r\nonLoad(() => {\r\n  getNavBarInfo();\r\n  loading.value = true;\r\n  fetchConsultantData();\r\n});\r\n</script>\r\n\r\n<!-- [无变化] 样式部分 -->\r\n<style lang=\"scss\" scoped>\r\n/* --- 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background: linear-gradient(180deg, #FFDEA1 0%, #FFFFFF 50%);\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: transparent;\r\n}\r\n\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  padding: 32rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n}\r\n/* --- 导航栏样式结束 --- */\r\n\r\n.loading-state, .empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: calc(100vh - 200rpx);\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n}\r\n\r\n.empty-text {\r\n  margin-top: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.content-wrapper {\r\n  /* 此容器用于在滚动区内实现原有的布局 */\r\n}\r\n\r\n.header-text {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n  color: #23232A;\r\n  margin: 20rpx 20rpx 40rpx 40rpx;\r\n}\r\n\r\n/* ================================================== */\r\n/* ↓↓↓ 这里是您要修改的地方 ↓↓↓           */\r\n/* ================================================== */\r\n.card {\r\n  /* [已修改] 根据您的要求，更新背景渐变色 */\r\n  background: linear-gradient(180deg, #FFDEA1 0%, #FFBF51 100%);\r\n  border-radius: 24rpx;\r\n  padding: 40rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  margin: 20rpx;\r\n}\r\n/* ================================================== */\r\n/* ↑↑↑ 这里是您要修改的地方 ↑↑↑           */\r\n/* ================================================== */\r\n\r\n.consultant-info {\r\n  display: flex;\r\n  align-items: center;\r\n  padding-bottom: 40rpx;\r\n  border-bottom: 2rpx solid #F0F0F0;\r\n}\r\n\r\n.avatar {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 50%;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.details {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.name-badge {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.name {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #452D03;\r\n}\r\n\r\n.badge {\r\n  background-color: #023F98;\r\n  color: #FFFFFF;\r\n  font-size: 24rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.intro {\r\n  font-size: 24rpx;\r\n  color: #452D03;\r\n  line-height: 1.5;\r\n}\r\n\r\n.more-questions {\r\n  font-size: 28rpx;\r\n  color: #023F98;\r\n  margin-top: 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.qr-code {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n  background-color: #fff; /* 为二维码添加白色背景板 */\r\n  border-radius: 16rpx;\r\n  padding: 20rpx;\r\n  box-sizing: border-box;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.qr-code-tip {\r\n  margin-top: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #452D03;\r\n}\r\n</style>\r\n\r\n", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_profile/contact.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "getFullImageUrl", "getDisplayConsultantApi", "onLoad"], "mappings": ";;;;;;;;;;;;;;;AA0EA,MAAM,yBAAyB;;;;AAC/B,UAAM,wBAAwBA,cAAG,MAAC,OAAO,sBAAsB;AAC/D,UAAM,kBAAkBC,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAE1B,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AACF,cAAM,iBAAiBD,oBAAI;AAC3B,wBAAgB,QAAQ,eAAe;AACvC,qBAAa,QAAQ,eAAe;AACpC,qBAAa,QAAQ,eAAe,SAAS;AAAA,MAC9C,SAAQ,GAAG;AACV,cAAM,aAAaA,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AACtD,qBAAa,QAAQ;AACrB,qBAAa,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAAA,MACnE;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa,EAAE,OAAO,EAAG,CAAA;AAAA,IAC/B;AAGA,UAAM,UAAUC,cAAAA,IAAI,IAAI;AACxB,UAAM,aAAaA,cAAAA,IAAI,IAAI;AAK3B,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACtC,aAAO,WAAW,QAAQC,YAAe,gBAAC,WAAW,MAAM,SAAS,IAAI;AAAA,IAC1E,CAAC;AAED,UAAM,mBAAmBD,cAAQ,SAAC,MAAM;AACtC,aAAO,WAAW,QAAQC,YAAe,gBAAC,WAAW,MAAM,SAAS,IAAI;AAAA,IAC1E,CAAC;AAGD,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,MAAM,MAAMC,gDAAAA;AAClB,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,qBAAW,QAAQ,IAAI;AAAA,QAC7B,OAAW;AACL,qBAAW,QAAQ;AAAA,QACpB;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAc,MAAA,MAAA,SAAA,8CAAA,aAAa,KAAK;AAChC,mBAAW,QAAQ;AAAA,MACvB,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM;AAI1B,UAAI,iBAAiB,OAAO;AAC1BA,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,iBAAiB,KAAK;AAAA,QACnC,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,SAAS;AAC7BA,oBAAA,MAAA,MAAA,SAAA,8CAAc,GAAG,IAAI,wBAAwB;AAE7C,UAAI,SAAS,UAAU;AACrB,mBAAW,MAAM,YAAY;AAAA,MACjC,WAAa,SAAS,UAAU;AAC5B,mBAAW,MAAM,YAAY;AAAA,MAC9B;AAAA,IACH;AAEAK,kBAAAA,OAAO,MAAM;AACX;AACA,cAAQ,QAAQ;AAChB;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1JD,GAAG,WAAW,eAAe;"}