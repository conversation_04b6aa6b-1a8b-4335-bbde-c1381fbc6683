{"version": 3, "file": "PopupAdComponent.js", "sources": ["components/common/PopupAdComponent.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvY29tbW9uL1BvcHVwQWRDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"visible\" class=\"popup-overlay\" @touchmove.prevent>\r\n    <view class=\"popup-content\">\r\n      <image\r\n          :src=\"adData.imageUrl\"\r\n          class=\"popup-image\"\r\n          mode=\"widthFix\"\r\n          @click=\"handleAdClick\"\r\n      />\r\n      <view class=\"close-button\" @click=\"closePopup\">\r\n        <uni-icons type=\"closeempty\" size=\"24\" color=\"#fff\"></uni-icons>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch } from 'vue';\r\n\r\n// 定义 props，用于从父组件接收广告数据和显示状态\r\nconst props = defineProps({\r\n  show: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  adData: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n});\r\n\r\n// 定义 emit 事件，用于通知父组件关闭弹窗\r\nconst emit = defineEmits(['close']);\r\n\r\nconst visible = ref(props.show);\r\n\r\n// 监听父组件传入的 show 属性变化，同步控制弹窗的显示/隐藏\r\nwatch(() => props.show, (newVal) => {\r\n  visible.value = newVal;\r\n});\r\n\r\n// 处理广告图片的点击事件\r\nconst handleAdClick = () => {\r\n  if (props.adData && props.adData.linkUrl) {\r\n    uni.navigateTo({\r\n      url: props.adData.linkUrl\r\n    });\r\n    // 点击后也关闭弹窗\r\n    closePopup();\r\n  }\r\n};\r\n\r\n// 关闭弹窗的函数\r\nconst closePopup = () => {\r\n  visible.value = false;\r\n  emit('close'); // 通知父组件更新状态\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.popup-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n\r\n  /* --- ↓↓↓ 这是关键修改 ↓↓↓ --- */\r\n  z-index: 9999; /* 使用一个极大的值，确保它永远在最上层 */\r\n}\r\n\r\n.popup-content {\r\n  position: relative;\r\n  width: 80vw;\r\n  max-width: 600rpx;\r\n}\r\n\r\n.popup-image {\r\n  width: 100%;\r\n  border-radius: 16rpx;\r\n  display: block; /* 避免图片下方有空隙 */\r\n}\r\n\r\n.close-button {\r\n  position: absolute;\r\n  /* 将关闭按钮放在图片正下方，更美观 */\r\n  bottom: -100rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: rgba(0, 0, 0, 0.3); /* 半透明背景，更柔和 */\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/common/PopupAdComponent.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAoBA,UAAM,QAAQ;AAYd,UAAM,OAAO;AAEb,UAAM,UAAUA,cAAG,IAAC,MAAM,IAAI;AAG9BC,kBAAK,MAAC,MAAM,MAAM,MAAM,CAAC,WAAW;AAClC,cAAQ,QAAQ;AAAA,IAClB,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1B,UAAI,MAAM,UAAU,MAAM,OAAO,SAAS;AACxCC,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,MAAM,OAAO;AAAA,QACxB,CAAK;AAED;MACD;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvB,cAAQ,QAAQ;AAChB,WAAK,OAAO;AAAA,IACd;;;;;;;;;;;;;;;;;;;;ACvDA,GAAG,gBAAgB,SAAS;"}