"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_country = require("../../api/content/country.js");
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_u_image2 = common_vendor.resolveComponent("u-image");
  (_easycom_uni_load_more2 + _easycom_uni_icons2 + _easycom_u_image2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_u_image = () => "../../uni_modules/uview-plus/components/u-image/u-image.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_icons + _easycom_u_image + ContentModule)();
}
const ContentModule = () => "../../components/home/<USER>";
const navBarPaddingBottomRpx = 20;
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const navBarPaddingBottomPx = common_vendor.index.upx2px(navBarPaddingBottomRpx);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const getNavBarInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        statusBarHeight.value = menuButtonInfo.top;
        navBarHeight.value = menuButtonInfo.height;
        headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
      } catch (e) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        navBarHeight.value = 44;
        headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
      }
    };
    const goBack = () => {
      common_vendor.index.switchTab({
        url: "/pages/country/index",
        fail: () => common_vendor.index.reLaunch({ url: "/pages/country/index" })
      });
    };
    const country = common_vendor.ref(null);
    const loading = common_vendor.ref(true);
    const activeTab = common_vendor.ref(0);
    const assets = common_vendor.ref({});
    const basicInfoCardStyle = common_vendor.computed(() => ({
      backgroundImage: assets.value.bg_basic_info_card ? `url('${assets.value.bg_basic_info_card}')` : "none"
    }));
    const activeTabStyle = common_vendor.computed(() => ({
      backgroundImage: assets.value.bg_tab_active ? `url('${assets.value.bg_tab_active}')` : "none"
    }));
    const tabsConfig = common_vendor.ref([
      { id: "basic", name: "基本信息", iconKey: "icon_tab_basic_normal", activeIconKey: "icon_tab_basic_active" },
      { id: "investment", name: "招商政策", iconKey: "icon_tab_investment_normal", activeIconKey: "icon_tab_investment_active" },
      { id: "customs", name: "海关政策", iconKey: "icon_tab_customs_normal", activeIconKey: "icon_tab_customs_active" },
      { id: "tax", name: "税务政策", iconKey: "icon_tab_tax_normal", activeIconKey: "icon_tab_tax_active" },
      { id: "parks", name: "工业园区", iconKey: "icon_tab_parks_normal", activeIconKey: "icon_tab_parks_active" }
    ]);
    const tabs = common_vendor.computed(() => {
      return tabsConfig.value.map((tab) => ({
        id: tab.id,
        name: tab.name,
        icon: assets.value[tab.iconKey] || tab.fallbackIcon,
        activeIcon: assets.value[tab.activeIconKey] || tab.fallbackActiveIcon
      }));
    });
    const basicInfoList = common_vendor.computed(() => {
      if (country.value && Array.isArray(country.value.basicInfoJson)) {
        return country.value.basicInfoJson;
      }
      return [];
    });
    const pairedBasicInfoList = common_vendor.computed(() => {
      const result = [];
      const list = basicInfoList.value;
      for (let i = 0; i < list.length; i += 2) {
        const pair = [list[i]];
        if (list[i + 1])
          pair.push(list[i + 1]);
        result.push(pair);
      }
      return result;
    });
    const onTabClick = (index) => {
      activeTab.value = index;
    };
    const isPolicyTabActive = common_vendor.computed(() => {
      var _a;
      const policyIds = ["investment", "customs", "tax"];
      return policyIds.includes((_a = tabs.value[activeTab.value]) == null ? void 0 : _a.id);
    });
    const currentPolicyType = common_vendor.computed(() => {
      var _a;
      return (_a = tabs.value[activeTab.value]) == null ? void 0 : _a.id;
    });
    const currentPolicyName = common_vendor.computed(() => {
      var _a;
      return (_a = tabs.value[activeTab.value]) == null ? void 0 : _a.name;
    });
    const currentPolicyContent = common_vendor.computed(() => {
      if (!country.value)
        return "";
      switch (currentPolicyType.value) {
        case "investment":
          return country.value.investmentPolicy;
        case "customs":
          return country.value.customsPolicy;
        case "tax":
          return country.value.taxPolicy;
        default:
          return "";
      }
    });
    const goToParkDetail = (parkId) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_other/park_detail?id=${parkId}`
      });
    };
    common_vendor.onLoad(async (options) => {
      getNavBarInfo();
      assets.value = common_vendor.index.getStorageSync("staticAssets") || {};
      if (!options.id) {
        common_vendor.index.showToast({ title: "参数错误", icon: "none" });
        common_vendor.index.navigateBack();
        return;
      }
      if (options.tab) {
        const initialTabIndex = tabs.value.findIndex((t) => t.id === options.tab);
        if (initialTabIndex !== -1)
          activeTab.value = initialTabIndex;
      }
      try {
        const res = await api_content_country.getCountryDetail(options.id);
        country.value = res.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_country/detail.vue:262", "获取详情失败:", error);
        common_vendor.index.showToast({ title: error.message || "加载失败", icon: "none" });
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onBackPress(() => {
      common_vendor.index.reLaunch({ url: "/pages/country/index" });
      return true;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loading.value
      }, loading.value ? {
        b: common_vendor.p({
          status: "loading"
        })
      } : country.value ? common_vendor.e({
        d: statusBarHeight.value + "px",
        e: common_vendor.p({
          type: "left",
          color: "#000000",
          size: "22"
        }),
        f: common_vendor.o(goBack),
        g: common_vendor.t(country.value.nameCn || "国别详情"),
        h: navBarHeight.value + "px",
        i: common_vendor.unref(navBarPaddingBottomPx) + "px",
        j: headerHeight.value + "px",
        k: country.value.detailsCoverUrl,
        l: common_vendor.t(country.value.nameCn),
        m: common_vendor.t(country.value.nameEn),
        n: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: activeTab.value === index ? tab.activeIcon : tab.icon,
            b: common_vendor.t(tab.name),
            c: tab.id,
            d: activeTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => onTabClick(index), tab.id),
            f: common_vendor.s(activeTab.value === index ? activeTabStyle.value : {})
          };
        }),
        o: !isPolicyTabActive.value
      }, !isPolicyTabActive.value ? common_vendor.e({
        p: common_vendor.t(country.value.introduction),
        q: basicInfoList.value.length > 0
      }, basicInfoList.value.length > 0 ? {
        r: common_vendor.f(pairedBasicInfoList.value, (pair, pairIndex, i0) => {
          var _a, _b, _c, _d;
          return {
            a: common_vendor.t((_a = pair[0]) == null ? void 0 : _a.key),
            b: common_vendor.t((_b = pair[1]) == null ? void 0 : _b.key),
            c: common_vendor.t((_c = pair[0]) == null ? void 0 : _c.value),
            d: common_vendor.t((_d = pair[1]) == null ? void 0 : _d.value),
            e: pairIndex
          };
        }),
        s: common_vendor.s(basicInfoCardStyle.value)
      } : {}, {
        t: activeTab.value === 0,
        v: common_vendor.f(country.value.industrialParks, (park, k0, i0) => {
          return {
            a: "46cca27f-2-" + i0,
            b: common_vendor.p({
              src: park.coverImageUrl,
              width: "100%",
              height: "240rpx",
              fade: true,
              ["lazy-load"]: true
            }),
            c: common_vendor.t(park.name),
            d: common_vendor.t(park.location),
            e: common_vendor.t(park.industries),
            f: common_vendor.t(park.features),
            g: park.id,
            h: common_vendor.o(($event) => goToParkDetail(park.id), park.id)
          };
        }),
        w: assets.value.icon_park_location || "/static/icons/位置icon金@2x.png",
        x: assets.value.icon_park_industries || "/static/icons/企业浅金@2x.png",
        y: assets.value.icon_park_features || "/static/icons/亮点浅金@2x.png",
        z: activeTab.value === 4
      }) : {
        A: common_vendor.t(currentPolicyName.value),
        B: common_vendor.t(currentPolicyContent.value),
        C: currentPolicyType.value,
        D: common_vendor.p({
          ["country-id"]: country.value.id,
          ["policy-type"]: currentPolicyType.value
        })
      }, {
        E: headerHeight.value + "px"
      }) : {}, {
        c: country.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-46cca27f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_country/detail.js.map
