"use strict";
const common_vendor = require("../common/vendor.js");
function navigateTo(navItem) {
  if (!navItem || !navItem.linkType || !navItem.linkTarget) {
    common_vendor.index.__f__("warn", "at utils/navigation.js:7", "无效的导航项:", navItem);
    return;
  }
  const { linkType, linkTarget, title } = navItem;
  switch (linkType) {
    case "INTERNAL_PAGE":
      common_vendor.index.navigateTo({
        url: linkTarget
      });
      break;
    case "TAB_PAGE":
      common_vendor.index.switchTab({
        url: linkTarget
      });
      break;
    case "EXTERNAL_LINK":
      common_vendor.index.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(linkTarget)}&title=${title || ""}`
      });
      break;
    default:
      common_vendor.index.__f__("warn", "at utils/navigation.js:34", "未知的链接类型:", linkType);
      break;
  }
}
exports.navigateTo = navigateTo;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/navigation.js.map
