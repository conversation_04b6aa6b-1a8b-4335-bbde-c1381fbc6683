{"version": 3, "file": "row.js", "sources": ["uni_modules/uview-plus/components/u-row/row.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:18:58\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/row.js\r\n */\r\nexport default {\r\n    // row\r\n    row: {\r\n        gutter: 0,\r\n        justify: 'start',\r\n        align: 'center'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,MAAA;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,EACV;AACL;;"}