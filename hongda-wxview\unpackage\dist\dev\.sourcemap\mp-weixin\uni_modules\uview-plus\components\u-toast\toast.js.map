{"version": 3, "file": "toast.js", "sources": ["uni_modules/uview-plus/components/u-toast/toast.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:07:07\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/toast.js\r\n */\r\nexport default {\r\n    // toast组件\r\n    toast: {\r\n        zIndex: 10090,\r\n        loading: false,\r\n        message: '',\r\n        icon: '',\r\n        type: '',\r\n        loadingMode: '',\r\n        show: '',\r\n        overlay: false,\r\n        position: 'center',\r\n        params: {},\r\n        duration: 2000,\r\n        isTab: false,\r\n        url: '',\r\n        callback: null,\r\n        back: false\r\n    }\r\n\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,CAAE;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,MAAM;AAAA,EACT;AAEL;;"}