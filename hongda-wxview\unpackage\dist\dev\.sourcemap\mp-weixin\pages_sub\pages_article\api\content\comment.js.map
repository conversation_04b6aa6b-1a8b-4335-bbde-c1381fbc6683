{"version": 3, "file": "comment.js", "sources": ["pages_sub/pages_article/api/content/comment.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * 获取评论列表（通用）\r\n * @param {object} params - 查询参数，例如 { relatedId, relatedType }\r\n */\r\nexport function getCommentList(params) {\r\n  return http.get('/comment/list', params);\r\n}\r\n\r\n/**\r\n * 提交新评论\r\n * @param {object} data 评论数据\r\n */\r\nexport function addComment(data) {\r\n  return http.post('/comment/add', data);\r\n}\r\n\r\n\r\n"], "names": ["http"], "mappings": ";;AAMO,SAAS,eAAe,QAAQ;AACrC,SAAOA,mBAAK,IAAI,iBAAiB,MAAM;AACzC;AAMO,SAAS,WAAW,MAAM;AAC/B,SAAOA,mBAAK,KAAK,gBAAgB,IAAI;AACvC;;;"}