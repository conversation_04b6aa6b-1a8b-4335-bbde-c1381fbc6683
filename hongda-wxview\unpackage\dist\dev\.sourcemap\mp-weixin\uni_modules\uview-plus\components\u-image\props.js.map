{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-image/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 图片地址\r\n        src: {\r\n            type: String,\r\n            default: () => defProps.image.src\r\n        },\r\n        // 裁剪模式\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.image.mode\r\n        },\r\n        // 宽度，单位任意\r\n        width: {\r\n            type: [String, Number],\r\n            default: () => defProps.image.width\r\n        },\r\n        // 高度，单位任意\r\n        height: {\r\n            type: [String, Number],\r\n            default: () => defProps.image.height\r\n        },\r\n        // 图片形状，circle-圆形，square-方形\r\n        shape: {\r\n            type: String,\r\n            default: () => defProps.image.shape\r\n        },\r\n        // 圆角，单位任意\r\n        radius: {\r\n            type: [String, Number],\r\n            default: () => defProps.image.radius\r\n        },\r\n        // 是否懒加载，微信小程序、App、百度小程序、字节跳动小程序\r\n        lazyLoad: {\r\n            type: Boolean,\r\n            default: () => defProps.image.lazyLoad\r\n        },\r\n        // 开启长按图片显示识别微信小程序码菜单\r\n        showMenuByLongpress: {\r\n            type: Boolean,\r\n            default: () => defProps.image.showMenuByLongpress\r\n        },\r\n        // 加载中的图标，或者小图片\r\n        loadingIcon: {\r\n            type: String,\r\n            default: () => defProps.image.loadingIcon\r\n        },\r\n        // 加载失败的图标，或者小图片\r\n        errorIcon: {\r\n            type: String,\r\n            default: () => defProps.image.errorIcon\r\n        },\r\n        // 是否显示加载中的图标或者自定义的slot\r\n        showLoading: {\r\n            type: Boolean,\r\n            default: () => defProps.image.showLoading\r\n        },\r\n        // 是否显示加载错误的图标或者自定义的slot\r\n        showError: {\r\n            type: Boolean,\r\n            default: () => defProps.image.showError\r\n        },\r\n        // 是否需要淡入效果\r\n        fade: {\r\n            type: Boolean,\r\n            default: () => defProps.image.fade\r\n        },\r\n        // 只支持网络资源，只对微信小程序有效\r\n        webp: {\r\n            type: Boolean,\r\n            default: () => defProps.image.webp\r\n        },\r\n        // 过渡时间，单位ms\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.image.duration\r\n        },\r\n        // 背景颜色，用于深色页面加载图片时，为了和背景色融合\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.image.bgColor\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}