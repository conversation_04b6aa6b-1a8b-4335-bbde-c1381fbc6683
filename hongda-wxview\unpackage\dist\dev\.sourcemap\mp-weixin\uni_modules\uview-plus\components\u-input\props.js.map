{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-input/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\n\r\nexport const props = defineMixin({\r\n\tprops: {\r\n\t\t// #ifdef VUE3\r\n\t\t// 绑定的值\r\n\t\tmodelValue: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.value\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE2\r\n\t\t// 绑定的值\r\n\t\tvalue: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.value\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// number-数字输入键盘，app-vue下可以输入浮点数，app-nvue和小程序平台下只能输入整数\r\n\t\t// idcard-身份证输入键盘，微信、支付宝、百度、QQ小程序\r\n\t\t// digit-带小数点的数字键盘，App的nvue页面、微信、支付宝、百度、头条、QQ小程序\r\n\t\t// text-文本输入键盘\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.type\r\n\t\t},\r\n\t\t// 如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，\r\n\t\t// 兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序\r\n\t\tfixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.fixed\r\n\t\t},\r\n\t\t// 是否禁用输入框\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.disabled\r\n\t\t},\r\n\t\t// 禁用状态时的背景色\r\n\t\tdisabledColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.disabledColor\r\n\t\t},\r\n\t\t// 是否显示清除控件\r\n\t\tclearable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.clearable\r\n\t\t},\r\n\t\t// 是否密码类型\r\n\t\tpassword: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.password\r\n\t\t},\r\n\t\t// 最大输入长度，设置为 -1 的时候不限制最大长度\r\n\t\tmaxlength: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.maxlength\r\n\t\t},\r\n\t\t// \t输入框为空时的占位符\r\n\t\tplaceholder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.placeholder\r\n\t\t},\r\n\t\t// 指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/\r\n\t\tplaceholderClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.placeholderClass\r\n\t\t},\r\n\t\t// 指定placeholder的样式\r\n\t\tplaceholderStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => defProps.input.placeholderStyle\r\n\t\t},\r\n\t\t// 是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效\r\n\t\tshowWordLimit: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.showWordLimit\r\n\t\t},\r\n\t\t// 设置右下角按钮的文字，有效值：send|search|next|go|done，兼容性详见uni-app文档\r\n\t\t// https://uniapp.dcloud.io/component/input\r\n\t\t// https://uniapp.dcloud.io/component/textarea\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.confirmType\r\n\t\t},\r\n\t\t// 点击键盘右下角按钮时是否保持键盘不收起，H5无效\r\n\t\tconfirmHold: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.confirmHold\r\n\t\t},\r\n\t\t// focus时，点击页面的时候不收起键盘，微信小程序有效\r\n\t\tholdKeyboard: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.holdKeyboard\r\n\t\t},\r\n\t\t// 自动获取焦点\r\n\t\t// 在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.focus\r\n\t\t},\r\n\t\t// 键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效\r\n\t\tautoBlur: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.autoBlur\r\n\t\t},\r\n\t\t// 是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效\r\n\t\tdisableDefaultPadding: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.disableDefaultPadding\r\n\t\t},\r\n\t\t// 指定focus时光标的位置\r\n\t\tcursor: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.cursor\r\n\t\t},\r\n\t\t// 输入框聚焦时底部与键盘的距离\r\n\t\tcursorSpacing: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.cursorSpacing\r\n\t\t},\r\n\t\t// 光标起始位置，自动聚集时有效，需与selection-end搭配使用\r\n\t\tselectionStart: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.selectionStart\r\n\t\t},\r\n\t\t// 光标结束位置，自动聚集时有效，需与selection-start搭配使用\r\n\t\tselectionEnd: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.selectionEnd\r\n\t\t},\r\n\t\t// 键盘弹起时，是否自动上推页面\r\n\t\tadjustPosition: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.adjustPosition\r\n\t\t},\r\n\t\t// 输入框内容对齐方式，可选值为：left|center|right\r\n\t\tinputAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.inputAlign\r\n\t\t},\r\n\t\t// 输入框字体的大小\r\n\t\tfontSize: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.input.fontSize\r\n\t\t},\r\n\t\t// 输入框字体颜色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.color\r\n\t\t},\r\n\t\t// 输入框前置图标\r\n\t\tprefixIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.prefixIcon\r\n\t\t},\r\n\t\t// 前置图标样式，对象或字符串\r\n\t\tprefixIconStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => defProps.input.prefixIconStyle\r\n\t\t},\r\n\t\t// 输入框后置图标\r\n\t\tsuffixIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.suffixIcon\r\n\t\t},\r\n\t\t// 后置图标样式，对象或字符串\r\n\t\tsuffixIconStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => defProps.input.suffixIconStyle\r\n\t\t},\r\n\t\t// 边框类型，surround-四周边框，bottom-底部边框，none-无边框\r\n\t\tborder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.border\r\n\t\t},\r\n\t\t// 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会\r\n\t\treadonly: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.input.readonly\r\n\t\t},\r\n\t\t// 输入框形状，circle-圆形，square-方形\r\n\t\tshape: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.input.shape\r\n\t\t},\r\n\t\t// 用于处理或者过滤输入框内容的方法\r\n\t\tformatter: {\r\n\t\t\ttype: [Function, null],\r\n\t\t\tdefault: () => defProps.input.formatter\r\n\t\t},\r\n\t\t// 是否忽略组件内对文本合成系统事件的处理\r\n\t\tignoreCompositionEvent: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t}\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA;AAAA,IAGN,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,IAaD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA,IAGD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAID,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA,IAGD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,UAAU,IAAI;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AACF,CAAC;;"}