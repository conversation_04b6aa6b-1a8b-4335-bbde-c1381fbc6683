<view class="content-module-container data-v-6f0ef454"><view class="sub-tabs-container data-v-6f0ef454"><scroll-view class="sub-tabs-wrapper data-v-6f0ef454" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{a}}" wx:for-item="category" wx:key="b" class="{{['sub-tab-item', 'data-v-6f0ef454', category.c && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></scroll-view><view class="divider data-v-6f0ef454"></view></view><view class="article-list-container data-v-6f0ef454"><view wx:if="{{b}}" class="loading-state data-v-6f0ef454"><u-loading-icon wx:if="{{c}}" class="data-v-6f0ef454" u-i="6f0ef454-0" bind:__l="__l" u-p="{{c}}"></u-loading-icon></view><view wx:elif="{{d}}" class="article-list data-v-6f0ef454"><view wx:for="{{e}}" wx:for-item="article" wx:key="c" class="article-item data-v-6f0ef454" bindtap="{{article.d}}"><view class="dot data-v-6f0ef454"></view><text class="article-title data-v-6f0ef454">{{article.a}}</text><u-icon wx:if="{{f}}" class="data-v-6f0ef454" u-i="{{article.b}}" bind:__l="__l" u-p="{{f}}"></u-icon></view></view><view wx:else class="empty-state data-v-6f0ef454"><u-empty wx:if="{{g}}" class="data-v-6f0ef454" u-i="6f0ef454-2" bind:__l="__l" u-p="{{g}}"></u-empty></view></view></view>