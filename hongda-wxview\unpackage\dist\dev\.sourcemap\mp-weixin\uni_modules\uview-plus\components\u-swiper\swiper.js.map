{"version": 3, "file": "swiper.js", "sources": ["uni_modules/uview-plus/components/u-swiper/swiper.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:21:38\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiper.js\r\n */\r\nexport default {\r\n    // swiper 组件\r\n    swiper: {\r\n        list: [],\r\n        indicator: false,\r\n        indicatorActiveColor: '#FFFFFF',\r\n        indicatorInactiveColor: 'rgba(255, 255, 255, 0.35)',\r\n        indicatorStyle: '',\r\n        indicatorMode: 'line',\r\n        autoplay: true,\r\n        current: 0,\r\n        currentItemId: '',\r\n        interval: 3000,\r\n        duration: 300,\r\n        circular: false,\r\n        previousMargin: 0,\r\n        nextMargin: 0,\r\n        acceleration: false,\r\n        displayMultipleItems: 1,\r\n        easingFunction: 'default',\r\n        keyName: 'url',\r\n        imgMode: 'aspectFill',\r\n        height: 130,\r\n        bgColor: '#f3f4f6',\r\n        radius: 4,\r\n        loading: false,\r\n        showTitle: false\r\n    }\r\n\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM,CAAE;AAAA,IACR,WAAW;AAAA,IACX,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACd;AAEL;;"}