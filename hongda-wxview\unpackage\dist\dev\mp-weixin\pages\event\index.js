"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_tools = require("../../utils/tools.js");
const utils_date = require("../../utils/date.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_up_icon2 = common_vendor.resolveComponent("up-icon");
  const _easycom_up_subsection2 = common_vendor.resolveComponent("up-subsection");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_loadmore2 = common_vendor.resolveComponent("up-loadmore");
  (_easycom_up_icon2 + _easycom_up_subsection2 + _easycom_up_empty2 + _easycom_up_button2 + _easycom_up_loadmore2)();
}
const _easycom_up_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_up_subsection = () => "../../uni_modules/uview-plus/components/u-subsection/u-subsection.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_up_icon + _easycom_up_subsection + CustomSearchBox + _easycom_up_empty + _easycom_up_button + EventCard + _easycom_up_loadmore + EventCalendarTimeline + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const CustomSearchBox = () => "../../components/home/<USER>";
const EventCard = () => "../../components/event/EventCard.js";
const EventCalendarTimeline = () => "../../components/event/EventCalendarTimeline.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const searchKeyword = common_vendor.ref("");
    const eventList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const showRetry = common_vendor.ref(false);
    const eventBgUrl = common_vendor.ref("");
    const pagination = common_vendor.ref({
      pageNum: 1,
      pageSize: utils_config.PAGE_CONFIG.DEFAULT_PAGE_SIZE,
      total: 0,
      hasMore: true
    });
    const appliedFiltersList = common_vendor.ref({
      sortBy: 1,
      location: 1,
      timeRange: 1,
      status: 1
    });
    const appliedFiltersCalendar = common_vendor.ref({
      location: 1,
      timeRange: 1
    });
    const tempFiltersList = common_vendor.ref({
      sortBy: 1,
      location: 1,
      timeRange: 1,
      status: 1
    });
    const tempFiltersCalendar = common_vendor.ref({
      location: 1,
      timeRange: 1
    });
    const showSortPanel = common_vendor.ref(false);
    const showLocationPanel = common_vendor.ref(false);
    const showTimePanel = common_vendor.ref(false);
    const showStatusPanel = common_vendor.ref(false);
    const calendarNotchLeft = common_vendor.ref("60rpx");
    const options1 = common_vendor.ref([
      {
        label: "综合排序",
        value: 1
      },
      {
        label: "最新发布",
        value: 2
      },
      {
        label: "最近开始",
        value: 3
      }
    ]);
    const options2 = common_vendor.ref([
      {
        label: "全部地区",
        value: 1
      },
      {
        label: "北京",
        value: 2
      },
      {
        label: "上海",
        value: 3
      },
      {
        label: "厦门",
        value: 4
      },
      {
        label: "广州",
        value: 5
      }
    ]);
    const options3 = common_vendor.ref([
      {
        label: "全部时间",
        value: 1
      },
      {
        label: "1周内",
        value: 2
      },
      {
        label: "1月内",
        value: 3
      },
      {
        label: "1年内",
        value: 4
      }
    ]);
    const options4 = common_vendor.ref([
      {
        label: "全部状态",
        value: 1
      },
      {
        label: "报名中",
        value: 2
      },
      {
        label: "已结束",
        value: 3
      }
    ]);
    const loadMoreStatus = common_vendor.computed(() => {
      if (isLoading.value)
        return "loading";
      if (!pagination.value.hasMore)
        return "nomore";
      return "more";
    });
    const groupedEvents = common_vendor.computed(() => {
      if (!eventList.value || eventList.value.length === 0) {
        return [];
      }
      const groups = /* @__PURE__ */ new Map();
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      eventList.value.forEach((event) => {
        const eventDate = utils_date.parseDate(event.startTime);
        const year = eventDate.getFullYear();
        const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
        const month = String(eventDate.getMonth() + 1).padStart(2, "0");
        const day = String(eventDate.getDate()).padStart(2, "0");
        let displayDate;
        if (year !== currentYear) {
          displayDate = `${year}年${month}月${day}日`;
        } else {
          displayDate = `${month}.${day}`;
        }
        const monthLocal = String(eventDate.getMonth() + 1).padStart(2, "0");
        const dayLocal = String(eventDate.getDate()).padStart(2, "0");
        const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;
        if (!groups.has(dateKey)) {
          groups.set(dateKey, {
            date: dateKey,
            formattedDate: displayDate,
            // 使用新的日期格式
            dayOfWeek: weekdays[eventDate.getDay()],
            events: []
          });
        }
        groups.get(dateKey).events.push(event);
      });
      return Array.from(groups.values());
    });
    const limitedCalendarEvents = common_vendor.computed(() => {
      let totalEvents = 0;
      const limitedGroups = [];
      for (const group of groupedEvents.value) {
        if (totalEvents >= 10)
          break;
        const remainingSlots = 10 - totalEvents;
        const eventsToShow = group.events.slice(0, remainingSlots);
        limitedGroups.push({
          ...group,
          events: eventsToShow
        });
        totalEvents += eventsToShow.length;
      }
      return limitedGroups;
    });
    const hasMoreCalendarEvents = common_vendor.computed(() => {
      const totalEvents = groupedEvents.value.reduce((sum, group) => sum + group.events.length, 0);
      return totalEvents > 10;
    });
    const buildQueryParams = (isLoadMore = false) => {
      const params = {
        pageNum: isLoadMore ? pagination.value.pageNum : 1,
        pageSize: pagination.value.pageSize
      };
      if (searchKeyword.value.trim()) {
        params.title = searchKeyword.value.trim();
      }
      const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;
      if (filters.location > 1) {
        const locationMap = {
          2: "北京",
          3: "上海",
          4: "厦门",
          5: "广州"
        };
        params.location = locationMap[filters.location];
      }
      if (currentTab.value === 0 && filters.status > 1) {
        const statusMap = {
          2: 1,
          // 报名中
          3: 2
          // 已结束
        };
        if (statusMap.hasOwnProperty(filters.status)) {
          params.status = statusMap[filters.status];
          common_vendor.index.__f__("log", "at pages/event/index.vue:590", "状态筛选:", `前端值${filters.status} -> 数据库值${params.status}`);
        } else {
          common_vendor.index.__f__("warn", "at pages/event/index.vue:592", "未知的状态筛选值:", filters.status);
        }
      }
      if (currentTab.value === 1) {
        params.orderBy = "startTime";
        params.isAsc = "asc";
      } else {
        switch (filters.sortBy) {
          case 1:
            params.orderBy = "comprehensive";
            break;
          case 2:
            params.orderBy = "startTime";
            params.isAsc = "asc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:610", "按时间排序: 最近开始优先");
            break;
          case 3:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:615", "按最新发布排序: 最新创建的活动优先");
            break;
          default:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:620", "默认排序: 最新发布");
        }
      }
      if (filters.timeRange > 1) {
        const timeRange = buildTimeRangeParams(filters.timeRange);
        if (timeRange) {
          Object.assign(params, timeRange);
        }
      }
      return params;
    };
    const buildTimeRangeParams = (timeRangeValue) => {
      const now = /* @__PURE__ */ new Date();
      let startTime = null;
      let endTime = null;
      switch (timeRangeValue) {
        case 2:
          startTime = now;
          endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1e3);
          common_vendor.index.__f__("log", "at pages/event/index.vue:649", "时间筛选: 1周内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 3:
          startTime = now;
          endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:654", "时间筛选: 1月内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 4:
          startTime = now;
          endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:659", "时间筛选: 1年内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        default:
          common_vendor.index.__f__("log", "at pages/event/index.vue:662", "时间筛选: 全部时间");
          return null;
      }
      return {
        timeRangeStart: startTime.toISOString(),
        timeRangeEnd: endTime.toISOString()
      };
    };
    const fetchEventList = async (isLoadMore = false) => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const params = buildQueryParams(isLoadMore);
        common_vendor.index.__f__("log", "at pages/event/index.vue:682", "请求参数:", params);
        let response;
        if (currentTab.value === 1) {
          response = await api_data_event.getCalendarEventsApi(params);
        } else {
          response = await api_data_event.getEventListApi(params);
        }
        const {
          rows = [],
          total = 0
        } = response;
        if (isLoadMore) {
          eventList.value.push(...rows);
        } else {
          eventList.value = rows;
          pagination.value.pageNum = 1;
        }
        pagination.value.total = total;
        pagination.value.hasMore = eventList.value.length < total;
        showRetry.value = false;
        common_vendor.index.__f__("log", "at pages/event/index.vue:710", `获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/event/index.vue:713", "获取活动列表失败:", error);
        let errorMessage = "获取活动列表失败";
        if (error.message && error.message.includes("timeout")) {
          errorMessage = "网络请求超时，请重试";
        } else if (error.message && error.message.includes("Network")) {
          errorMessage = "网络连接失败，请检查网络";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
        if (!isLoadMore && eventList.value.length === 0) {
          showRetry.value = true;
        }
      } finally {
        isLoading.value = false;
        isRefreshing.value = false;
      }
    };
    const debouncedSearch = utils_tools.debounce(() => {
      fetchEventList();
    }, 500);
    const getCurrentSortTitleList = common_vendor.computed(() => {
      const currentOption = options1.value.find((option) => option.value === appliedFiltersList.value.sortBy);
      const title = currentOption ? currentOption.label : "综合排序";
      return title;
    });
    const getCurrentStatusTitleList = common_vendor.computed(() => {
      const currentOption = options4.value.find((option) => option.value === appliedFiltersList.value.status);
      const title = currentOption ? currentOption.label : "全部状态";
      return title;
    });
    const getCurrentLocationTitleList = common_vendor.computed(() => {
      const currentOption = options2.value.find((option) => option.value === appliedFiltersList.value.location);
      return currentOption ? currentOption.label : "全部地区";
    });
    const getCurrentTimeTitleList = common_vendor.computed(() => {
      const currentOption = options3.value.find((option) => option.value === appliedFiltersList.value.timeRange);
      return currentOption ? currentOption.label : "全部时间";
    });
    const getCurrentLocationTitleCalendar = common_vendor.computed(() => {
      const currentOption = options2.value.find((option) => option.value === appliedFiltersCalendar.value.location);
      return currentOption ? currentOption.label : "全部地区";
    });
    const getCurrentTimeTitleCalendar = common_vendor.computed(() => {
      const currentOption = options3.value.find((option) => option.value === appliedFiltersCalendar.value.timeRange);
      return currentOption ? currentOption.label : "全部时间";
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const tabChange = (index) => {
      currentTab.value = index;
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const onSearch = (value) => {
      searchKeyword.value = value;
      debouncedSearch();
    };
    const onFilterChange = () => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:837", "筛选条件变更，重置数据并重新加载");
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const toggleSortPanel = () => {
      showSortPanel.value = !showSortPanel.value;
      showLocationPanel.value = false;
      showTimePanel.value = false;
      showStatusPanel.value = false;
    };
    const toggleLocationPanel = () => {
      showLocationPanel.value = !showLocationPanel.value;
      showSortPanel.value = false;
      showTimePanel.value = false;
      showStatusPanel.value = false;
      calendarNotchLeft.value = "60rpx";
    };
    const toggleTimePanel = () => {
      showTimePanel.value = !showTimePanel.value;
      showSortPanel.value = false;
      showLocationPanel.value = false;
      showStatusPanel.value = false;
      calendarNotchLeft.value = "240rpx";
    };
    const toggleStatusPanel = () => {
      showStatusPanel.value = !showStatusPanel.value;
      showSortPanel.value = false;
      showLocationPanel.value = false;
      showTimePanel.value = false;
    };
    const selectSortOption = (value) => {
      tempFiltersList.value.sortBy = value;
      common_vendor.index.__f__("log", "at pages/event/index.vue:901", "临时选择排序:", value);
    };
    const selectLocationOption = (value) => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.location = value;
      } else {
        tempFiltersList.value.location = value;
      }
      common_vendor.index.__f__("log", "at pages/event/index.vue:914", "临时选择地区:", value);
    };
    const selectTimeOption = (value) => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.timeRange = value;
      } else {
        tempFiltersList.value.timeRange = value;
      }
      common_vendor.index.__f__("log", "at pages/event/index.vue:927", "临时选择时间:", value);
    };
    const selectStatusOption = (value) => {
      tempFiltersList.value.status = value;
      common_vendor.index.__f__("log", "at pages/event/index.vue:937", "临时选择状态:", value);
    };
    const resetSortFilter = () => {
      tempFiltersList.value.sortBy = 1;
      appliedFiltersList.value.sortBy = 1;
      showSortPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:950", "重置排序筛选为初始状态");
      onFilterChange();
    };
    const completeSortFilter = () => {
      appliedFiltersList.value.sortBy = tempFiltersList.value.sortBy;
      showSortPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:961", "应用排序筛选:", tempFiltersList.value.sortBy);
      onFilterChange();
    };
    const resetLocationFilter = () => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.location = 1;
        appliedFiltersCalendar.value.location = 1;
      } else {
        tempFiltersList.value.location = 1;
        appliedFiltersList.value.location = 1;
      }
      showLocationPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:977", "重置地区筛选为初始状态");
      onFilterChange();
    };
    const completeLocationFilter = () => {
      if (currentTab.value === 1) {
        appliedFiltersCalendar.value.location = tempFiltersCalendar.value.location;
      } else {
        appliedFiltersList.value.location = tempFiltersList.value.location;
      }
      showLocationPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:991", "应用地区筛选:", currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location);
      onFilterChange();
      calendarNotchLeft.value = "60rpx";
    };
    const resetTimeFilter = () => {
      if (currentTab.value === 1) {
        tempFiltersCalendar.value.timeRange = 1;
        appliedFiltersCalendar.value.timeRange = 1;
      } else {
        tempFiltersList.value.timeRange = 1;
        appliedFiltersList.value.timeRange = 1;
      }
      showTimePanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1009", "重置时间筛选为初始状态");
      onFilterChange();
    };
    const completeTimeFilter = () => {
      if (currentTab.value === 1) {
        appliedFiltersCalendar.value.timeRange = tempFiltersCalendar.value.timeRange;
      } else {
        appliedFiltersList.value.timeRange = tempFiltersList.value.timeRange;
      }
      showTimePanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1023", "应用时间筛选:", currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange);
      onFilterChange();
      calendarNotchLeft.value = "240rpx";
    };
    const resetStatusFilter = () => {
      tempFiltersList.value.status = 1;
      appliedFiltersList.value.status = 1;
      showStatusPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1037", "重置状态筛选为初始状态");
      onFilterChange();
    };
    const completeStatusFilter = () => {
      appliedFiltersList.value.status = tempFiltersList.value.status;
      showStatusPanel.value = false;
      common_vendor.index.__f__("log", "at pages/event/index.vue:1048", "应用状态筛选:", tempFiltersList.value.status);
      onFilterChange();
    };
    const goToDetail = (event) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${event.id}`
      });
    };
    const switchToListView = () => {
      currentTab.value = 0;
      fetchEventList();
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      pagination.value.pageNum = 1;
      fetchEventList();
    };
    const onLoadMore = () => {
      if (!pagination.value.hasMore || isLoading.value)
        return;
      pagination.value.pageNum++;
      fetchEventList(true);
    };
    common_vendor.onLoad(() => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      eventBgUrl.value = (assets == null ? void 0 : assets.eventbg) || "";
      fetchEventList();
      common_vendor.index.$on("dataChanged", () => {
        common_vendor.index.__f__("log", "at pages/event/index.vue:1098", "活动列表页收到数据变化事件，刷新列表...");
        fetchEventList();
        common_vendor.index.showToast({
          title: "列表已更新",
          icon: "success",
          duration: 1500
        });
      });
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("dataChanged");
    });
    common_vendor.onReachBottom(() => {
      onLoadMore();
    });
    common_vendor.onPullDownRefresh(() => {
      onRefresh();
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: eventBgUrl.value,
        b: common_vendor.p({
          name: "arrow-left",
          size: "24",
          color: "#FFFFFF"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.o(tabChange),
        e: common_vendor.p({
          list: ["列表", "日历"],
          current: currentTab.value,
          mode: "subsection",
          activeColor: "#f56c6c"
        }),
        f: common_vendor.o(onSearch),
        g: common_vendor.o(common_vendor.unref(debouncedSearch)),
        h: common_vendor.o(($event) => searchKeyword.value = $event),
        i: common_vendor.p({
          placeholder: "搜索活动",
          modelValue: searchKeyword.value
        }),
        j: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        k: common_vendor.t(getCurrentSortTitleList.value),
        l: showSortPanel.value ? 1 : "",
        m: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        n: common_vendor.o(toggleSortPanel),
        o: common_vendor.t(getCurrentLocationTitleList.value),
        p: showLocationPanel.value ? 1 : "",
        q: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        r: common_vendor.o(toggleLocationPanel),
        s: common_vendor.t(getCurrentTimeTitleList.value),
        t: showTimePanel.value ? 1 : "",
        v: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        w: common_vendor.o(toggleTimePanel),
        x: common_vendor.t(getCurrentStatusTitleList.value),
        y: showStatusPanel.value ? 1 : "",
        z: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        A: common_vendor.o(toggleStatusPanel),
        B: showSortPanel.value
      }, showSortPanel.value ? {
        C: common_vendor.f(options1.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": tempFiltersList.value.sortBy === option.value
            }),
            d: common_vendor.o(($event) => selectSortOption(option.value), option.value)
          };
        }),
        D: common_vendor.o(resetSortFilter),
        E: common_vendor.o(completeSortFilter)
      } : {}, {
        F: showLocationPanel.value
      }, showLocationPanel.value ? {
        G: common_vendor.f(options2.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === option.value
            }),
            d: common_vendor.o(($event) => selectLocationOption(option.value), option.value)
          };
        }),
        H: common_vendor.o(resetLocationFilter),
        I: common_vendor.o(completeLocationFilter)
      } : {}, {
        J: showTimePanel.value
      }, showTimePanel.value ? {
        K: common_vendor.f(options3.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange) === option.value
            }),
            d: common_vendor.o(($event) => selectTimeOption(option.value), option.value)
          };
        }),
        L: common_vendor.o(resetTimeFilter),
        M: common_vendor.o(completeTimeFilter)
      } : {}, {
        N: showStatusPanel.value
      }, showStatusPanel.value ? {
        O: common_vendor.f(options4.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": tempFiltersList.value.status === option.value
            }),
            d: common_vendor.o(($event) => selectStatusOption(option.value), option.value)
          };
        }),
        P: common_vendor.o(resetStatusFilter),
        Q: common_vendor.o(completeStatusFilter)
      } : {}) : {}, {
        R: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        S: !isLoading.value && eventList.value.length === 0
      }, !isLoading.value && eventList.value.length === 0 ? common_vendor.e({
        T: common_vendor.p({
          mode: "data",
          text: "暂无活动数据",
          textColor: "#909399",
          iconSize: "120"
        }),
        U: showRetry.value
      }, showRetry.value ? {
        V: common_vendor.o(fetchEventList),
        W: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {}, {
        X: common_vendor.f(eventList.value, (event, k0, i0) => {
          return {
            a: event.id,
            b: common_vendor.o(($event) => goToDetail(event), event.id),
            c: "8e954d49-9-" + i0,
            d: common_vendor.p({
              event
            })
          };
        }),
        Y: common_vendor.p({
          status: loadMoreStatus.value,
          ["loading-text"]: "正在加载...",
          ["loadmore-text"]: "上拉加载更多",
          ["nomore-text"]: "没有更多了"
        }),
        Z: common_vendor.o(onLoadMore),
        aa: isRefreshing.value,
        ab: common_vendor.o(onRefresh)
      }) : {}, {
        ac: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        ad: common_vendor.t(getCurrentLocationTitleCalendar.value),
        ae: showLocationPanel.value ? 1 : "",
        af: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        ag: common_vendor.o(toggleLocationPanel),
        ah: common_vendor.t(getCurrentTimeTitleCalendar.value),
        ai: showTimePanel.value ? 1 : "",
        aj: common_vendor.p({
          name: "arrow-down",
          size: "14",
          color: "#666"
        }),
        ak: common_vendor.o(toggleTimePanel),
        al: showLocationPanel.value
      }, showLocationPanel.value ? {
        am: common_vendor.f(options2.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.location : tempFiltersList.value.location) === option.value
            }),
            d: common_vendor.o(($event) => selectLocationOption(option.value), option.value)
          };
        }),
        an: common_vendor.o(resetLocationFilter),
        ao: common_vendor.o(completeLocationFilter)
      } : {}, {
        ap: showTimePanel.value
      }, showTimePanel.value ? {
        aq: common_vendor.f(options3.value, (option, k0, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: option.value,
            c: common_vendor.n({
              "active": (currentTab.value === 1 ? tempFiltersCalendar.value.timeRange : tempFiltersList.value.timeRange) === option.value
            }),
            d: common_vendor.o(($event) => selectTimeOption(option.value), option.value)
          };
        }),
        ar: common_vendor.o(resetTimeFilter),
        as: common_vendor.o(completeTimeFilter)
      } : {}) : {}, {
        at: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        av: !isLoading.value && limitedCalendarEvents.value.length === 0
      }, !isLoading.value && limitedCalendarEvents.value.length === 0 ? common_vendor.e({
        aw: common_vendor.p({
          mode: "data",
          text: "暂无活动数据",
          textColor: "#909399",
          iconSize: "120"
        }),
        ax: showRetry.value
      }, showRetry.value ? {
        ay: common_vendor.o(fetchEventList),
        az: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {
        aA: common_vendor.o(goToDetail),
        aB: common_vendor.o(switchToListView),
        aC: common_vendor.p({
          groups: limitedCalendarEvents.value,
          ["has-more"]: hasMoreCalendarEvents.value,
          ["notch-left"]: calendarNotchLeft.value
        })
      }, {
        aD: common_vendor.o(onLoadMore),
        aE: isRefreshing.value,
        aF: common_vendor.o(onRefresh)
      }) : {}, {
        aG: common_vendor.p({
          current: 2
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e954d49"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/event/index.js.map
