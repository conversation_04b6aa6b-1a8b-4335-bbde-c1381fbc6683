{"version": 3, "file": "line.js", "sources": ["uni_modules/uview-plus/components/u-line/line.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:04:49\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/line.js\r\n */\r\nexport default {\r\n    // line组件\r\n    line: {\r\n        color: '#d6d7d9',\r\n        length: '100%',\r\n        direction: 'row',\r\n        hairline: true,\r\n        margin: 0,\r\n        dashed: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACL;;"}