{"version": 3, "file": "indexAnchor.js", "sources": ["uni_modules/uview-plus/components/u-index-anchor/indexAnchor.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:13:15\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexAnchor.js\r\n */\r\nexport default {\r\n    // indexAnchor 组件\r\n    indexAnchor: {\r\n        text: '',\r\n        color: '#606266',\r\n        size: 14,\r\n        bgColor: '#f1f1f1',\r\n        height: 32\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACX;AACL;;"}