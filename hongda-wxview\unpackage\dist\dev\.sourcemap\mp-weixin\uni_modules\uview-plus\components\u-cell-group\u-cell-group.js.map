{"version": 3, "file": "u-cell-group.js", "sources": ["uni_modules/uview-plus/components/u-cell-group/u-cell-group.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWNlbGwtZ3JvdXAvdS1jZWxsLWdyb3VwLnZ1ZQ"], "sourcesContent": ["<template>\r\n    <view :style=\"[addStyle(customStyle)]\" :class=\"[customClass]\" class=\"u-cell-group\">\r\n        <view v-if=\"title\" class=\"u-cell-group__title\">\r\n            <slot name=\"title\">\r\n\t\t\t\t<text class=\"u-cell-group__title__text\">{{ title }}</text>\r\n\t\t\t</slot>\r\n        </view>\r\n        <view class=\"u-cell-group__wrapper\">\r\n\t\t\t<u-line v-if=\"border\"></u-line>\r\n            <slot />\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle } from '../../libs/function/index';\r\n\t/**\r\n\t * cellGroup  单元格\r\n\t * @description cell单元格一般用于一组列表的情况，比如个人中心页，设置页等。\r\n\t * @tutorial https://uview-plus.jiangruyi.com/components/cell.html\r\n\t * \r\n\t * @property {String}\ttitle\t\t分组标题\r\n\t * @property {Boolean}\tborder\t\t是否显示外边框 (默认 true )\r\n\t * @property {Object}\tcustomStyle\t定义需要用到的外部样式\r\n\t * \r\n\t * @pages_event {Function} click \t点击cell列表时触发\r\n\t * @example <u-cell-group title=\"设置喜好\">\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-cell-group',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tmethods: {\r\n\t\t\taddStyle\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t\r\n\t$u-cell-group-title-padding: 16px 16px 8px !default;\r\n\t$u-cell-group-title-font-size: 15px !default;\r\n\t$u-cell-group-title-line-height: 16px !default;\r\n\t$u-cell-group-title-color: $u-main-color !default;\r\n\r\n    .u-cell-group {\r\n\t\tflex: 1;\r\n\t\t\r\n        &__title {\r\n            padding: $u-cell-group-title-padding;\r\n\r\n            &__text {\r\n                font-size: $u-cell-group-title-font-size;\r\n                line-height: $u-cell-group-title-line-height;\r\n                color: $u-cell-group-title-color;\r\n            }\r\n        }\r\n\t\t\r\n\t\t&__wrapper {\r\n\t\t\tposition: relative;\r\n\t\t}\r\n    }\r\n</style>\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-cell-group/u-cell-group.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle"], "mappings": ";;;;;;AA+BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,uDAAK;AAAA,EAC9B,SAAS;AAAA,IACR,UAAAC,0CAAO;AAAA,EACR;AACD;;;;;;;;;;;;;;;;;;;;;;ACpCD,GAAG,gBAAgB,SAAS;"}