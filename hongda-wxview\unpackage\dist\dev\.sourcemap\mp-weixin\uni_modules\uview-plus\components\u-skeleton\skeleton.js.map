{"version": 3, "file": "skeleton.js", "sources": ["uni_modules/uview-plus/components/u-skeleton/skeleton.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:20:14\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/skeleton.js\r\n */\r\nexport default {\r\n    // skeleton\r\n    skeleton: {\r\n        loading: true,\r\n        animate: true,\r\n        rows: 0,\r\n        rowsWidth: '100%',\r\n        rowsHeight: 18,\r\n        title: true,\r\n        titleWidth: '50%',\r\n        titleHeight: 18,\r\n        avatar: false,\r\n        avatarSize: 32,\r\n        avatarShape: 'circle'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,EAChB;AACL;;"}