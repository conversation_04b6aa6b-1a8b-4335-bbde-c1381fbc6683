{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-badge/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否显示圆点\r\n        isDot: {\r\n            type: Boolean,\r\n            default: () => defProps.badge.isDot\r\n        },\r\n        // 显示的内容\r\n        value: {\r\n            type: [Number, String],\r\n            default: () => defProps.badge.value\r\n        },\r\n        // 显示的内容\r\n        modelValue: {\r\n            type: [Number, String],\r\n            default: () => defProps.badge.modelValue\r\n        },\r\n        // 是否显示\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.badge.show\r\n        },\r\n        // 最大值，超过最大值会显示 '{max}+'\r\n        max: {\r\n            type: [Number, String],\r\n            default: () => defProps.badge.max\r\n        },\r\n        // 主题类型，error|warning|success|primary\r\n        type: {\r\n            type: String,\r\n            default: () => defProps.badge.type\r\n        },\r\n        // 当数值为 0 时，是否展示 Badge\r\n        showZero: {\r\n            type: Boolean,\r\n            default: () => defProps.badge.showZero\r\n        },\r\n        // 背景颜色，优先级比type高，如设置，type参数会失效\r\n        bgColor: {\r\n            type: [String, null],\r\n            default: () => defProps.badge.bgColor\r\n        },\r\n        // 字体颜色\r\n        color: {\r\n            type: [String, null],\r\n            default: () => defProps.badge.color\r\n        },\r\n        // 徽标形状，circle-四角均为圆角，horn-左下角为直角\r\n        shape: {\r\n            type: String,\r\n            default: () => defProps.badge.shape\r\n        },\r\n        // 设置数字的显示方式，overflow|ellipsis|limit\r\n        // overflow会根据max字段判断，超出显示`${max}+`\r\n        // ellipsis会根据max判断，超出显示`${max}...`\r\n        // limit会依据1000作为判断条件，超出1000，显示`${value/1000}K`，比如2.2k、3.34w，最多保留2位小数\r\n        numberType: {\r\n            type: String,\r\n            default: () => defProps.badge.numberType\r\n        },\r\n        // 设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，absolute为true时有效\r\n        offset: {\r\n            type: Array,\r\n            default: () => defProps.badge.offset\r\n        },\r\n        // 是否反转背景和字体颜色\r\n        inverted: {\r\n            type: Boolean,\r\n            default: () => defProps.badge.inverted\r\n        },\r\n        // 是否绝对定位\r\n        absolute: {\r\n            type: Boolean,\r\n            default: () => defProps.badge.absolute\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}