{"version": 3, "file": "detail.js", "sources": ["pages_sub/pages_country/detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2NvdW50cnlcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"loading\" class=\"loading-container\">\r\n    <uni-load-more status=\"loading\"/>\r\n  </view>\r\n\r\n  <view v-else-if=\"country\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">{{ country.nameCn || '国别详情' }}</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"cover-section\">\r\n        <image class=\"cover-image\" :src=\"country.detailsCoverUrl\" mode=\"aspectFill\"/>\r\n        <view class=\"cover-overlay\">\r\n          <view class=\"country-name-cn\">{{ country.nameCn }}</view>\r\n          <view class=\"country-name-en\">{{ country.nameEn }}</view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"tabs-wrapper\">\r\n        <scroll-view class=\"tabs\" scroll-x=\"true\" :show-scrollbar=\"false\">\r\n          <!-- 【已修改】这里现在遍历的是动态的 'tabs' 计算属性 -->\r\n          <view\r\n              v-for=\"(tab, index) in tabs\"\r\n              :key=\"tab.id\"\r\n              class=\"tab-item\"\r\n              :class=\"{ active: activeTab === index }\"\r\n              @click=\"onTabClick(index)\"\r\n              :style=\"activeTab === index ? activeTabStyle : {}\"\r\n          >\r\n            <image class=\"tab-icon\" :src=\"activeTab === index ? tab.activeIcon : tab.icon\"></image>\r\n            <text class=\"tab-text\">{{ tab.name }}</text>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n\r\n      <view class=\"content-wrapper\">\r\n        <view v-if=\"!isPolicyTabActive\">\r\n          <view v-show=\"activeTab === 0\" class=\"content-panel\">\r\n            <view class=\"section-card\">\r\n              <view class=\"section-title\">国家简介</view>\r\n              <view class=\"plain-text-content introduction-text\">\r\n                <text selectable user-select>{{ country.introduction }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"section-card\" v-if=\"basicInfoList.length > 0\">\r\n              <view class=\"section-title\">基本信息</view>\r\n              <view class=\"info-grid-container basic-info-card\" :style=\"basicInfoCardStyle\">\r\n                <view class=\"info-pair-row\" v-for=\"(pair, pairIndex) in pairedBasicInfoList\" :key=\"pairIndex\">\r\n                  <view class=\"info-row key-row\">\r\n                    <view class=\"info-column\">{{ pair[0]?.key }}</view>\r\n                    <view class=\"info-column\">{{ pair[1]?.key }}</view>\r\n                  </view>\r\n                  <view class=\"info-row value-row\">\r\n                    <view class=\"info-column\">{{ pair[0]?.value }}</view>\r\n                    <view class=\"info-column\">{{ pair[1]?.value }}</view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view v-show=\"activeTab === 4\" class=\"content-panel\">\r\n            <view\r\n                class=\"park-card\"\r\n                v-for=\"park in country.industrialParks\"\r\n                :key=\"park.id\"\r\n                @click=\"goToParkDetail(park.id)\"\r\n            >\r\n              <view class=\"park-card-image-wrapper\">\r\n                <u-image\r\n                    :src=\"park.coverImageUrl\"\r\n                    width=\"100%\"\r\n                    height=\"240rpx\"\r\n                    :fade=\"true\"\r\n                    :lazy-load=\"true\"\r\n                ></u-image>\r\n              </view>\r\n              <view class=\"park-card-content\">\r\n                <view class=\"park-name\">{{ park.name }}</view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_location || '/static/icons/位置icon金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.location }}</text>\r\n                </view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_industries || '/static/icons/企业浅金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.industries }}</text>\r\n                </view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_features || '/static/icons/亮点浅金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.features }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-else class=\"policy-page-container\">\r\n          <view class=\"policy-title-header\">\r\n            <h1 class=\"policy-title\">{{ currentPolicyName }}</h1>\r\n          </view>\r\n          <view class=\"policy-main-content\">\r\n            <view class=\"policy-intro-wrapper\">\r\n              <view class=\"plain-text-content policy-text\">\r\n                <text selectable user-select>{{ currentPolicyContent }}</text>\r\n              </view>\r\n            </view>\r\n            <ContentModule\r\n                :key=\"currentPolicyType\"\r\n                :country-id=\"country.id\"\r\n                :policy-type=\"currentPolicyType\"\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n\r\n<script setup>\r\nimport {onLoad, onBackPress} from '@dcloudio/uni-app'\r\nimport {computed, ref} from 'vue';\r\nimport {getCountryDetail} from '@/api/content/country.js';\r\nimport ContentModule from '@/components/home/<USER>';\r\n\r\n// --- 自定义导航栏相关逻辑 ---\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch(e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.switchTab({\r\n    url: '/pages/country/index',\r\n    fail: () => uni.reLaunch({ url: '/pages/country/index' })\r\n  });\r\n};\r\n\r\n// --- 页面核心数据和状态 ---\r\nconst country = ref(null);\r\nconst loading = ref(true);\r\nconst activeTab = ref(0);\r\nconst assets = ref({});\r\n\r\nconst basicInfoCardStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_basic_info_card ? `url('${assets.value.bg_basic_info_card}')` : 'none'\r\n}));\r\n\r\nconst activeTabStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_tab_active ? `url('${assets.value.bg_tab_active}')` : 'none'\r\n}));\r\n\r\n// 【已修改】这里现在是标签页的静态配置\r\nconst tabsConfig = ref([\r\n  { id: 'basic', name: '基本信息', iconKey: 'icon_tab_basic_normal', activeIconKey: 'icon_tab_basic_active' },\r\n  { id: 'investment', name: '招商政策', iconKey: 'icon_tab_investment_normal', activeIconKey: 'icon_tab_investment_active' },\r\n  { id: 'customs', name: '海关政策', iconKey: 'icon_tab_customs_normal', activeIconKey: 'icon_tab_customs_active' },\r\n  { id: 'tax', name: '税务政策', iconKey: 'icon_tab_tax_normal', activeIconKey: 'icon_tab_tax_active' },\r\n  { id: 'parks', name: '工业园区', iconKey: 'icon_tab_parks_normal', activeIconKey: 'icon_tab_parks_active' },\r\n]);\r\n\r\n// 【新增】一个计算属性，用于动态构建带有正确图标URL的标签页列表\r\nconst tabs = computed(() => {\r\n  return tabsConfig.value.map(tab => ({\r\n    id: tab.id,\r\n    name: tab.name,\r\n    icon: assets.value[tab.iconKey] || tab.fallbackIcon,\r\n    activeIcon: assets.value[tab.activeIconKey] || tab.fallbackActiveIcon\r\n  }));\r\n});\r\n\r\nconst basicInfoList = computed(() => {\r\n  if (country.value && Array.isArray(country.value.basicInfoJson)) {\r\n    return country.value.basicInfoJson;\r\n  }\r\n  return [];\r\n});\r\n\r\nconst pairedBasicInfoList = computed(() => {\r\n  const result = [];\r\n  const list = basicInfoList.value;\r\n  for (let i = 0; i < list.length; i += 2) {\r\n    const pair = [list[i]];\r\n    if (list[i + 1]) pair.push(list[i + 1]);\r\n    result.push(pair);\r\n  }\r\n  return result;\r\n});\r\n\r\nconst onTabClick = (index) => {\r\n  activeTab.value = index;\r\n};\r\n\r\nconst isPolicyTabActive = computed(() => {\r\n  const policyIds = ['investment', 'customs', 'tax'];\r\n  return policyIds.includes(tabs.value[activeTab.value]?.id);\r\n});\r\n\r\nconst currentPolicyType = computed(() => tabs.value[activeTab.value]?.id);\r\nconst currentPolicyName = computed(() => tabs.value[activeTab.value]?.name);\r\n\r\nconst currentPolicyContent = computed(() => {\r\n  if (!country.value) return '';\r\n  switch (currentPolicyType.value) {\r\n    case 'investment': return country.value.investmentPolicy;\r\n    case 'customs': return country.value.customsPolicy;\r\n    case 'tax': return country.value.taxPolicy;\r\n    default: return '';\r\n  }\r\n});\r\n\r\nconst goToParkDetail = (parkId) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_other/park_detail?id=${parkId}`\r\n  });\r\n};\r\n\r\nonLoad(async (options) => {\r\n  getNavBarInfo();\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n\r\n  if (!options.id) {\r\n    uni.showToast({title: '参数错误', icon: 'none'});\r\n    uni.navigateBack();\r\n    return;\r\n  }\r\n\r\n  if (options.tab) {\r\n    const initialTabIndex = tabs.value.findIndex(t => t.id === options.tab);\r\n    if (initialTabIndex !== -1) activeTab.value = initialTabIndex;\r\n  }\r\n\r\n  try {\r\n    const res = await getCountryDetail(options.id);\r\n    // 【已修改】不再需要为图片URL拼接baseUrl\r\n    // 后端的AOP切面已经处理好了完整的URL\r\n    country.value = res.data;\r\n  } catch (error) {\r\n    console.error('获取详情失败:', error);\r\n    uni.showToast({title: error.message || '加载失败', icon: 'none'});\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n\r\nonBackPress(() => {\r\n  uni.reLaunch({ url: '/pages/country/index' });\r\n  return true;\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n}\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n}\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n}\r\n.loading-container {\r\n  padding-top: 200rpx;\r\n}\r\n.cover-section {\r\n  position: relative;\r\n  height: 400rpx;\r\n}\r\n.cover-section .cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.cover-section .cover-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  padding: 30rpx;\r\n  box-sizing: border-box;\r\n  color: #fff;\r\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);\r\n}\r\n.cover-section .cover-overlay .country-name-cn {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n}\r\n.cover-section .cover-overlay .country-name-en {\r\n  font-size: 32rpx;\r\n  opacity: 0.9;\r\n}\r\n.tabs-wrapper {\r\n  margin: 30rpx 30rpx 0;\r\n  background-color: #F4F4F4;\r\n  border-radius: 16rpx;\r\n  padding: 12rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n.tabs {\r\n  display: flex;\r\n  white-space: nowrap;\r\n}\r\n.tab-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  padding: 12rpx 24rpx;\r\n  margin-right: 20rpx;\r\n}\r\n.tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n.tab-item .tab-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n.tab-item .tab-text {\r\n  font-size: 28rpx;\r\n  color: #9B9A9A;\r\n}\r\n.tab-item.active {\r\n  background-size: cover;\r\n  background-position: center;\r\n}\r\n.tab-item.active .tab-text {\r\n  color: #23232A;\r\n  font-weight: bold;\r\n}\r\n.content-wrapper {\r\n  padding: 24rpx;\r\n}\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n.section-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  padding-bottom: 15rpx;\r\n  margin-bottom: 20rpx;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n.plain-text-content {\r\n  font-size: 28rpx;\r\n  line-height: 1.7;\r\n  white-space: pre-wrap;\r\n}\r\n.introduction-text {\r\n  color: #66666E;\r\n}\r\n.policy-text {\r\n  color: #23232A;\r\n}\r\n.basic-info-card {\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  padding: 20rpx;\r\n}\r\n.info-grid-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24rpx;\r\n}\r\n.info-row {\r\n  display: flex;\r\n  width: 100%;\r\n}\r\n.info-column {\r\n  flex: 1;\r\n  width: 50%;\r\n  padding-right: 20rpx;\r\n  box-sizing: border-box;\r\n}\r\n.key-row .info-column {\r\n  font-size: 24rpx;\r\n  color: #66666E;\r\n}\r\n.value-row {\r\n  margin-top: 8rpx;\r\n}\r\n.value-row .info-column {\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  font-weight: 500;\r\n}\r\n.policy-title-header {\r\n  padding: 30rpx;\r\n  border-radius: 16rpx 16rpx 0 0;\r\n  background: linear-gradient(to bottom, #CEDEF5, #F0F2F3) center;\r\n  border-bottom: 2rpx solid #DCDFE6;\r\n}\r\n.policy-title-header .policy-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #023F98;\r\n}\r\n.policy-main-content {\r\n  background-color: #F2F4FA;\r\n  border-radius: 0 0 16rpx 16rpx;\r\n  padding: 30rpx;\r\n}\r\n.policy-intro-wrapper {\r\n  margin-bottom: 30rpx;\r\n}\r\n.park-card {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  transition: transform 0.2s;\r\n  overflow: hidden;\r\n}\r\n.park-card:active {\r\n  transform: scale(0.98);\r\n}\r\n.park-card .park-card-image-wrapper {\r\n  width: 100%;\r\n  height: 240rpx;\r\n  background-color: #f0f2f5;\r\n}\r\n.park-card .park-card-content {\r\n  padding: 30rpx;\r\n}\r\n.park-card .park-name {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n}\r\n.park-card .park-info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 16rpx;\r\n}\r\n.park-card .park-info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.park-card .park-info-icon {\r\n  width: 26rpx;\r\n  height: 26rpx;\r\n  margin-right: 12rpx;\r\n  flex-shrink: 0;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_country/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "onLoad", "getCountryDetail", "onBackPress"], "mappings": ";;;;;;;;;;;;;;;AAmIA,MAAM,gBAAgB,MAAW;AAGjC,MAAM,yBAAyB;;;;AAC/B,UAAM,wBAAwBA,cAAG,MAAC,OAAO,sBAAsB;AAC/D,UAAM,kBAAkBC,cAAAA,IAAI,CAAC;AAC7B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAE1B,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AACF,cAAM,iBAAiBD,oBAAI;AAC3B,wBAAgB,QAAQ,eAAe;AACvC,qBAAa,QAAQ,eAAe;AACpC,qBAAa,QAAQ,eAAe,SAAS;AAAA,MAC9C,SAAO,GAAG;AACT,cAAM,aAAaA,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AACtD,qBAAa,QAAQ;AACrB,qBAAa,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAAA,MACnE;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM,MAAMA,cAAAA,MAAI,SAAS,EAAE,KAAK,uBAAsB,CAAE;AAAA,MAC5D,CAAG;AAAA,IACH;AAGA,UAAM,UAAUC,cAAAA,IAAI,IAAI;AACxB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AAErB,UAAM,qBAAqBC,cAAQ,SAAC,OAAO;AAAA,MACzC,iBAAiB,OAAO,MAAM,qBAAqB,QAAQ,OAAO,MAAM,kBAAkB,OAAO;AAAA,IACnG,EAAE;AAEF,UAAM,iBAAiBA,cAAQ,SAAC,OAAO;AAAA,MACrC,iBAAiB,OAAO,MAAM,gBAAgB,QAAQ,OAAO,MAAM,aAAa,OAAO;AAAA,IACzF,EAAE;AAGF,UAAM,aAAaD,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,SAAS,MAAM,QAAQ,SAAS,yBAAyB,eAAe,wBAAyB;AAAA,MACvG,EAAE,IAAI,cAAc,MAAM,QAAQ,SAAS,8BAA8B,eAAe,6BAA8B;AAAA,MACtH,EAAE,IAAI,WAAW,MAAM,QAAQ,SAAS,2BAA2B,eAAe,0BAA2B;AAAA,MAC7G,EAAE,IAAI,OAAO,MAAM,QAAQ,SAAS,uBAAuB,eAAe,sBAAuB;AAAA,MACjG,EAAE,IAAI,SAAS,MAAM,QAAQ,SAAS,yBAAyB,eAAe,wBAAyB;AAAA,IACzG,CAAC;AAGD,UAAM,OAAOC,cAAQ,SAAC,MAAM;AAC1B,aAAO,WAAW,MAAM,IAAI,UAAQ;AAAA,QAClC,IAAI,IAAI;AAAA,QACR,MAAM,IAAI;AAAA,QACV,MAAM,OAAO,MAAM,IAAI,OAAO,KAAK,IAAI;AAAA,QACvC,YAAY,OAAO,MAAM,IAAI,aAAa,KAAK,IAAI;AAAA,MACpD,EAAC;AAAA,IACJ,CAAC;AAED,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,QAAQ,SAAS,MAAM,QAAQ,QAAQ,MAAM,aAAa,GAAG;AAC/D,eAAO,QAAQ,MAAM;AAAA,MACtB;AACD,aAAO;IACT,CAAC;AAED,UAAM,sBAAsBA,cAAQ,SAAC,MAAM;AACzC,YAAM,SAAS,CAAA;AACf,YAAM,OAAO,cAAc;AAC3B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,YAAI,KAAK,IAAI,CAAC;AAAG,eAAK,KAAK,KAAK,IAAI,CAAC,CAAC;AACtC,eAAO,KAAK,IAAI;AAAA,MACjB;AACD,aAAO;AAAA,IACT,CAAC;AAED,UAAM,aAAa,CAAC,UAAU;AAC5B,gBAAU,QAAQ;AAAA,IACpB;AAEA,UAAM,oBAAoBA,cAAQ,SAAC,MAAM;;AACvC,YAAM,YAAY,CAAC,cAAc,WAAW,KAAK;AACjD,aAAO,UAAU,UAAS,UAAK,MAAM,UAAU,KAAK,MAA1B,mBAA6B,EAAE;AAAA,IAC3D,CAAC;AAED,UAAM,oBAAoBA,cAAAA,SAAS,MAAA;;AAAM,wBAAK,MAAM,UAAU,KAAK,MAA1B,mBAA6B;AAAA,KAAE;AACxE,UAAM,oBAAoBA,cAAAA,SAAS,MAAA;;AAAM,wBAAK,MAAM,UAAU,KAAK,MAA1B,mBAA6B;AAAA,KAAI;AAE1E,UAAM,uBAAuBA,cAAQ,SAAC,MAAM;AAC1C,UAAI,CAAC,QAAQ;AAAO,eAAO;AAC3B,cAAQ,kBAAkB,OAAK;AAAA,QAC7B,KAAK;AAAc,iBAAO,QAAQ,MAAM;AAAA,QACxC,KAAK;AAAW,iBAAO,QAAQ,MAAM;AAAA,QACrC,KAAK;AAAO,iBAAO,QAAQ,MAAM;AAAA,QACjC;AAAS,iBAAO;AAAA,MACjB;AAAA,IACH,CAAC;AAED,UAAM,iBAAiB,CAAC,WAAW;AACjCF,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yCAAyC,MAAM;AAAA,MACxD,CAAG;AAAA,IACH;AAEAG,kBAAM,OAAC,OAAO,YAAY;AACxB;AACA,aAAO,QAAQH,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA;AAErD,UAAI,CAAC,QAAQ,IAAI;AACfA,sBAAG,MAAC,UAAU,EAAC,OAAO,QAAQ,MAAM,OAAM,CAAC;AAC3CA,sBAAG,MAAC,aAAY;AAChB;AAAA,MACD;AAED,UAAI,QAAQ,KAAK;AACf,cAAM,kBAAkB,KAAK,MAAM,UAAU,OAAK,EAAE,OAAO,QAAQ,GAAG;AACtE,YAAI,oBAAoB;AAAI,oBAAU,QAAQ;AAAA,MAC/C;AAED,UAAI;AACF,cAAM,MAAM,MAAMI,oBAAAA,iBAAiB,QAAQ,EAAE;AAG7C,gBAAQ,QAAQ,IAAI;AAAA,MACrB,SAAQ,OAAO;AACdJ,sBAAA,MAAA,MAAA,SAAA,6CAAc,WAAW,KAAK;AAC9BA,4BAAI,UAAU,EAAC,OAAO,MAAM,WAAW,QAAQ,MAAM,OAAM,CAAC;AAAA,MAChE,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;AAEDK,kBAAAA,YAAY,MAAM;AAChBL,oBAAAA,MAAI,SAAS,EAAE,KAAK,uBAAwB,CAAA;AAC5C,aAAO;AAAA,IACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9QD,GAAG,WAAW,eAAe;"}