{"version": 3, "file": "EventDetailContent.js", "sources": ["components/event/EventDetailContent.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnREZXRhaWxDb250ZW50LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"detail-section\">\r\n    <text class=\"section-title\">活动详情</text>\r\n\r\n    <view class=\"rich-text-content\" v-if=\"processedDetails && processedDetails.trim().length > 0\">\r\n      <rich-text :nodes=\"processedDetails\" class=\"rich-text\"></rich-text>\r\n    </view>\r\n\r\n    <view class=\"summary-content\" v-else-if=\"eventDetail.summary && eventDetail.summary.trim().length > 0\">\r\n      <text class=\"summary-text\">{{ eventDetail.summary }}</text>\r\n    </view>\r\n\r\n    <view class=\"placeholder-content\" v-else>\r\n      <text class=\"placeholder-text\">暂无详细介绍</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { getFullImageUrl } from '@/utils/image.js'\r\n\r\nconst props = defineProps({\r\n  eventDetail: { type: Object, required: true }\r\n})\r\n\r\nconst processedDetails = computed(() => {\r\n  if (!props.eventDetail?.details || typeof props.eventDetail.details !== 'string') {\r\n    return ''\r\n  }\r\n\r\n  let processedHtml = props.eventDetail.details\r\n\r\n  try {\r\n    processedHtml = processedHtml.replace(\r\n      /<img([^>]*?)src=[\"']([^\"']*?)[\"']([^>]*?)>/gi,\r\n      (match, beforeSrc, srcValue, afterSrc) => {\r\n        const formattedSrc = getFullImageUrl(srcValue)\r\n        const hasStyle = /style\\s*=/.test(beforeSrc + afterSrc)\r\n\r\n        if (hasStyle) {\r\n          const styleRegex = /style\\s*=\\s*[\"']([^\"']*?)[\"']/i\r\n          const styleMatch = (beforeSrc + afterSrc).match(styleRegex)\r\n          if (styleMatch) {\r\n            const existingStyle = styleMatch[1]\r\n            const newStyle = existingStyle.includes('max-width')\r\n              ? existingStyle\r\n              : existingStyle + '; max-width: 100%; height: auto;'\r\n            return `<img${beforeSrc}src=\"${formattedSrc}\"${afterSrc}`.replace(\r\n              styleRegex,\r\n              `style=\"${newStyle}\"`\r\n            )\r\n          }\r\n        }\r\n        return `<img${beforeSrc}src=\"${formattedSrc}\" style=\"max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;\"${afterSrc}>`\r\n      }\r\n    )\r\n\r\n    processedHtml = processedHtml.replace(\r\n      /<img([^>]*?)>/gi,\r\n      (match) => {\r\n        if (!/style\\s*=/.test(match)) {\r\n          return match.replace('>', ' style=\"max-width: 100%; height: auto; border-radius: 8rpx; margin: 16rpx 0;\">')\r\n        }\r\n        return match\r\n      }\r\n    )\r\n\r\n    return processedHtml\r\n  } catch (error) {\r\n    console.error('富文本处理失败:', error)\r\n    return props.eventDetail.details\r\n  }\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.detail-section {\r\n  background-color: #ffffff;\r\n  margin: 30rpx 0;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\r\n  padding: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.placeholder-content {\r\n  background-color: #f8f8f8;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n  text-align: center;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.placeholder-text {\r\n  color: #909399;\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n.rich-text-content {\r\n  line-height: 1.6;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n}\r\n\r\n.rich-text {\r\n  width: 100%;\r\n  line-height: 1.6;\r\n}\r\n\r\n:deep(.rich-text) {\r\n  p {\r\n    margin: 16rpx 0;\r\n    line-height: 1.6;\r\n  }\r\n  img {\r\n    max-width: 100%;\r\n    height: auto;\r\n    border-radius: 8rpx;\r\n    margin: 16rpx 0;\r\n  }\r\n  h1, h2, h3, h4, h5, h6 {\r\n    margin: 24rpx 0 16rpx 0;\r\n    font-weight: bold;\r\n  }\r\n  ul, ol {\r\n    margin: 16rpx 0;\r\n    padding-left: 40rpx;\r\n  }\r\n  li {\r\n    margin: 8rpx 0;\r\n  }\r\n  blockquote {\r\n    background-color: #f8f8f8;\r\n    border-left: 8rpx solid #409eff;\r\n    padding: 16rpx;\r\n    margin: 16rpx 0;\r\n    border-radius: 8rpx;\r\n  }\r\n}\r\n\r\n.summary-content {\r\n  padding: 20rpx;\r\n  background-color: #f8f8f8;\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.summary-text {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  line-height: 1.6;\r\n}\r\n</style>\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventDetailContent.vue'\nwx.createComponent(Component)"], "names": ["computed", "getFullImageUrl", "uni"], "mappings": ";;;;;;;;;AAsBA,UAAM,QAAQ;AAId,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;;AACtC,UAAI,GAAC,WAAM,gBAAN,mBAAmB,YAAW,OAAO,MAAM,YAAY,YAAY,UAAU;AAChF,eAAO;AAAA,MACR;AAED,UAAI,gBAAgB,MAAM,YAAY;AAEtC,UAAI;AACF,wBAAgB,cAAc;AAAA,UAC5B;AAAA,UACA,CAAC,OAAO,WAAW,UAAU,aAAa;AACxC,kBAAM,eAAeC,YAAe,gBAAC,QAAQ;AAC7C,kBAAM,WAAW,YAAY,KAAK,YAAY,QAAQ;AAEtD,gBAAI,UAAU;AACZ,oBAAM,aAAa;AACnB,oBAAM,cAAc,YAAY,UAAU,MAAM,UAAU;AAC1D,kBAAI,YAAY;AACd,sBAAM,gBAAgB,WAAW,CAAC;AAClC,sBAAM,WAAW,cAAc,SAAS,WAAW,IAC/C,gBACA,gBAAgB;AACpB,uBAAO,OAAO,SAAS,QAAQ,YAAY,IAAI,QAAQ,GAAG;AAAA,kBACxD;AAAA,kBACA,UAAU,QAAQ;AAAA,gBACnB;AAAA,cACF;AAAA,YACF;AACD,mBAAO,OAAO,SAAS,QAAQ,YAAY,iFAAiF,QAAQ;AAAA,UACrI;AAAA,QACF;AAED,wBAAgB,cAAc;AAAA,UAC5B;AAAA,UACA,CAAC,UAAU;AACT,gBAAI,CAAC,YAAY,KAAK,KAAK,GAAG;AAC5B,qBAAO,MAAM,QAAQ,KAAK,gFAAgF;AAAA,YAC3G;AACD,mBAAO;AAAA,UACR;AAAA,QACF;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,iDAAc,YAAY,KAAK;AAC/B,eAAO,MAAM,YAAY;AAAA,MAC1B;AAAA,IACH,CAAC;;;;;;;;;;;;;;;ACxED,GAAG,gBAAgB,SAAS;"}