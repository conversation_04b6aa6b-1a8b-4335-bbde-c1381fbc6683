{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-icon/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 图标类名\r\n        name: {\r\n            type: String,\r\n            default: () => defProps.icon.name\r\n        },\r\n        // 图标颜色，可接受主题色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.icon.color\r\n        },\r\n        // 字体大小，单位px\r\n        size: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.size\r\n        },\r\n        // 是否显示粗体\r\n        bold: {\r\n            type: Boolean,\r\n            default: () => defProps.icon.bold\r\n        },\r\n        // 点击图标的时候传递事件出去的index（用于区分点击了哪一个）\r\n        index: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.index\r\n        },\r\n        // 触摸图标时的类名\r\n        hoverClass: {\r\n            type: String,\r\n            default: () => defProps.icon.hoverClass\r\n        },\r\n        // 自定义扩展前缀，方便用户扩展自己的图标库\r\n        customPrefix: {\r\n            type: String,\r\n            default: () => defProps.icon.customPrefix\r\n        },\r\n        // 图标右边或者下面的文字\r\n        label: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.label\r\n        },\r\n        // label的位置，只能右边或者下边\r\n        labelPos: {\r\n            type: String,\r\n            default: () => defProps.icon.labelPos\r\n        },\r\n        // label的大小\r\n        labelSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.labelSize\r\n        },\r\n        // label的颜色\r\n        labelColor: {\r\n            type: String,\r\n            default: () => defProps.icon.labelColor\r\n        },\r\n        // label与图标的距离\r\n        space: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.space\r\n        },\r\n        // 图片的mode\r\n        imgMode: {\r\n            type: String,\r\n            default: () => defProps.icon.imgMode\r\n        },\r\n        // 用于显示图片小图标时，图片的宽度\r\n        width: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.width\r\n        },\r\n        // 用于显示图片小图标时，图片的高度\r\n        height: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.height\r\n        },\r\n        // 用于解决某些情况下，让图标垂直居中的用途\r\n        top: {\r\n            type: [String, Number],\r\n            default: () => defProps.icon.top\r\n        },\r\n        // 是否阻止事件传播\r\n        stop: {\r\n            type: Boolean,\r\n            default: () => defProps.icon.stop\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}