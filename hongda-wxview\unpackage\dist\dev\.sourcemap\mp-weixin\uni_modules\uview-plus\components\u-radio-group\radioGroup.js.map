{"version": 3, "file": "radioGroup.js", "sources": ["uni_modules/uview-plus/components/u-radio-group/radioGroup.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : CPS\r\n * @lastTime     : 2024-11-05 16:01:12\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radioGroup.js\r\n */\r\nexport default {\r\n    // radio-group组件\r\n    radioGroup: {\r\n        value: '',\r\n        disabled: false,\r\n        shape: 'circle',\r\n        activeColor: '#2979ff',\r\n        inactiveColor: '#c8c9cc',\r\n        name: '',\r\n        size: 18,\r\n        placement: 'row',\r\n        label: '',\r\n        labelColor: '#303133',\r\n        labelSize: 14,\r\n        labelDisabled: false,\r\n        iconColor: '#ffffff',\r\n        iconSize: 12,\r\n        borderBottom: false,\r\n        iconPlacement: 'left',\r\n        gap: \"10px\"\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,KAAK;AAAA,EACR;AACL;;"}