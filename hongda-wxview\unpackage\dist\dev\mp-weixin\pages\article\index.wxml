<view class="page-container data-v-fd1fc04a"><view class="header-section data-v-fd1fc04a"><view class="custom-nav-bar data-v-fd1fc04a"><view class="status-bar data-v-fd1fc04a"></view><view class="nav-title data-v-fd1fc04a">资讯列表</view><view class="filter-bar data-v-fd1fc04a"><view class="{{['sort-button', 'data-v-fd1fc04a', c && 'is-active']}}" bindtap="{{d}}">{{a}} <u-icon wx:if="{{b}}" class="data-v-fd1fc04a" u-i="fd1fc04a-0" bind:__l="__l" u-p="{{b}}"></u-icon></view><view class="{{['filter-button', 'data-v-fd1fc04a', h && 'is-active']}}" bindtap="{{i}}">{{e}} <u-icon wx:if="{{f}}" class="data-v-fd1fc04a" u-i="fd1fc04a-1" bind:__l="__l" u-p="{{f}}"></u-icon><view wx:if="{{g}}" class="active-dot data-v-fd1fc04a"></view></view><view class="search-box data-v-fd1fc04a"><uni-easyinput wx:if="{{n}}" class="search-input data-v-fd1fc04a" bindconfirm="{{j}}" bindclear="{{k}}" bindinput="{{l}}" u-i="fd1fc04a-2" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uni-easyinput></view></view></view></view><view class="content-section data-v-fd1fc04a"><view class="tabs-container data-v-fd1fc04a"><u-tabs wx:if="{{o}}" class="data-v-fd1fc04a" bindchange="{{p}}" u-i="fd1fc04a-3" bind:__l="__l" u-p="{{q}}"></u-tabs></view><scroll-view scroll-y class="article-list-scroll data-v-fd1fc04a" bindscrolltolower="{{y}}" enable-flex><view class="article-list data-v-fd1fc04a"><view wx:for="{{r}}" wx:for-item="item" wx:key="l" class="article-card data-v-fd1fc04a" bindtap="{{item.m}}"><view class="card-cover data-v-fd1fc04a"><u-image wx:if="{{item.f}}" class="data-v-fd1fc04a" u-s="{{['loading','error']}}" binderror="{{item.c}}" bindload="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"><view class="image-loading data-v-fd1fc04a" slot="loading"><u-loading-icon wx:if="{{s}}" class="data-v-fd1fc04a" u-i="{{item.a}}" bind:__l="__l" u-p="{{s}}"></u-loading-icon><text class="loading-text data-v-fd1fc04a">加载中...</text></view><view class="image-error data-v-fd1fc04a" slot="error"><u-icon wx:if="{{t}}" class="data-v-fd1fc04a" u-i="{{item.b}}" bind:__l="__l" u-p="{{t}}"></u-icon><text class="error-text data-v-fd1fc04a">图片加载失败</text></view></u-image></view><view class="card-content data-v-fd1fc04a"><text class="card-title data-v-fd1fc04a">{{item.g}}</text><view wx:if="{{item.h}}" class="card-tags data-v-fd1fc04a"><text wx:for="{{item.i}}" wx:for-item="tag" wx:key="b" class="tag-item data-v-fd1fc04a">{{tag.a}}</text></view><view class="card-meta data-v-fd1fc04a"><text class="meta-source data-v-fd1fc04a">{{item.j}}</text><text class="meta-date data-v-fd1fc04a">{{item.k}}</text></view></view></view></view><u-empty wx:if="{{v}}" class="data-v-fd1fc04a" u-i="fd1fc04a-7" bind:__l="__l" u-p="{{w}}"></u-empty><u-loadmore wx:else class="data-v-fd1fc04a" u-i="fd1fc04a-8" bind:__l="__l" u-p="{{x||''}}"/></scroll-view></view><view wx:if="{{z}}" class="dropdown-wrapper data-v-fd1fc04a"><view class="dropdown-mask data-v-fd1fc04a" bindtap="{{A}}"></view><view class="{{['dropdown-panel', 'data-v-fd1fc04a', I && 'show']}}"><view wx:if="{{B}}" class="sort-panel data-v-fd1fc04a"><view wx:for="{{C}}" wx:for-item="sort" wx:key="e" class="{{['sort-option', 'data-v-fd1fc04a', sort.f && 'active']}}" bindtap="{{sort.g}}">{{sort.a}} <u-icon wx:if="{{sort.b}}" class="data-v-fd1fc04a" u-i="{{sort.c}}" bind:__l="__l" u-p="{{sort.d}}"></u-icon></view></view><view wx:if="{{D}}" class="filter-panel data-v-fd1fc04a"><scroll-view scroll-y class="filter-scroll data-v-fd1fc04a"><view class="panel-section data-v-fd1fc04a"><text class="section-title data-v-fd1fc04a">内容地区</text><view class="panel-options data-v-fd1fc04a"><view wx:for="{{E}}" wx:for-item="region" wx:key="b" class="{{['option-btn', 'data-v-fd1fc04a', region.c && 'active']}}" bindtap="{{region.d}}">{{region.a}}</view></view></view><view class="panel-section data-v-fd1fc04a"><text class="section-title data-v-fd1fc04a">发布时间</text><view class="panel-options data-v-fd1fc04a"><view wx:for="{{F}}" wx:for-item="time" wx:key="b" class="{{['option-btn', 'data-v-fd1fc04a', time.c && 'active']}}" bindtap="{{time.d}}">{{time.a}}</view></view></view></scroll-view><view class="panel-footer data-v-fd1fc04a"><button class="footer-btn reset data-v-fd1fc04a" bindtap="{{G}}">重置</button><button class="footer-btn confirm data-v-fd1fc04a" bindtap="{{H}}">确定</button></view></view></view></view><custom-tab-bar wx:if="{{J}}" class="data-v-fd1fc04a" u-i="fd1fc04a-10" bind:__l="__l" u-p="{{J}}"/></view>