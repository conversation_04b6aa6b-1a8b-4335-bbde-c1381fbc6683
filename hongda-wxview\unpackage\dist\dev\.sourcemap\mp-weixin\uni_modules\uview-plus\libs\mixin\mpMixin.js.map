{"version": 3, "file": "mpMixin.js", "sources": ["uni_modules/uview-plus/libs/mixin/mpMixin.js"], "sourcesContent": ["import { defineMixin } from '../vue'\r\n\r\nexport const mpMixin = defineMixin({\r\n    // #ifdef MP-WEIXIN\r\n    // 将自定义节点设置成虚拟的，更加接近Vue组件的表现，能更好的使用flex属性\r\n    options: {\r\n        virtualHost: true\r\n    }\r\n    // #endif\r\n})\r\n\r\nexport default mpMixin\r\n\r\n"], "names": ["defineMixin"], "mappings": ";;AAEY,MAAC,UAAUA,+BAAAA,YAAY;AAAA;AAAA,EAG/B,SAAS;AAAA,IACL,aAAa;AAAA,EAChB;AAEL,CAAC;;"}