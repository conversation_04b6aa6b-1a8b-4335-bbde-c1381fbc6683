{"version": 3, "file": "u-loadmore.js", "sources": ["uni_modules/uview-plus/components/u-loadmore/u-loadmore.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxvYWRtb3JlL3UtbG9hZG1vcmUudnVl"], "sourcesContent": ["<template>\r\n\t<view\r\n\t    class=\"u-loadmore\"\r\n\t    :style=\"[\r\n\t\t\taddStyle(customStyle),\r\n\t\t\t{\r\n\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\tmarginBottom: addUnit(marginBottom),\r\n\t\t\t\tmarginTop: addUnit(marginTop),\r\n\t\t\t\theight: addUnit(height),\r\n\t\t\t},\r\n\t\t]\"\r\n\t>\r\n\t\t<u-line\r\n\t\t    length=\"140rpx\"\r\n\t\t    :color=\"lineColor\"\r\n\t\t    :hairline=\"false\"\r\n\t\t\t:dashed=\"dashed\"\r\n\t\t\tv-if=\"line\"\r\n\t\t></u-line>\r\n\t\t<!-- 加载中和没有更多的状态才显示两边的横线 -->\r\n\t\t<view\r\n\t\t    :class=\"status == 'loadmore' || status == 'nomore' ? 'u-more' : ''\"\r\n\t\t    class=\"u-loadmore__content\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-loadmore__content__icon-wrap\"\r\n\t\t\t    v-if=\"status === 'loading' && icon\"\r\n\t\t\t>\r\n\t\t\t\t<u-loading-icon\r\n\t\t\t\t    :color=\"iconColor\"\r\n\t\t\t\t    :size=\"iconSize\"\r\n\t\t\t\t    :mode=\"loadingIcon\"\r\n\t\t\t\t></u-loading-icon>\r\n\t\t\t</view>\r\n\t\t\t<!-- 如果没有更多的状态下，显示内容为dot（粗点），加载特定样式 -->\r\n\t\t\t<text\r\n\t\t\t    class=\"u-line-1\"\r\n\t\t\t    :style=\"[loadTextStyle]\"\r\n\t\t\t    :class=\"[(status == 'nomore' && isDot == true) ? 'u-loadmore__content__dot-text' : 'u-loadmore__content__text']\"\r\n\t\t\t    @tap=\"loadMore\"\r\n\t\t\t>{{ showText }}</text>\r\n\t\t</view>\r\n\t\t<u-line\r\n\t\t    length=\"140rpx\"\r\n\t\t    :color=\"lineColor\"\r\n\t\t\t:hairline=\"false\"\r\n\t\t\t:dashed=\"dashed\"\r\n\t\t\tv-if=\"line\"\r\n\t\t></u-line>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle } from '../../libs/function/index';\r\n\t/**\r\n\t * loadmore 加载更多\r\n\t * @description 此组件一般用于标识页面底部加载数据时的状态。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/loadMore.html\r\n\t * @property {String}\t\t\tstatus\t\t\t组件状态（默认 'loadmore' ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t组件背景颜色，在页面是非白色时会用到（默认 'transparent' ）\r\n\t * @property {Boolean}\t\t\ticon\t\t\t加载中时是否显示图标（默认 true ）\r\n\t * @property {String | Number}\tfontSize\t\t字体大小（默认 14 ）\r\n\t * @property {String | Number}\ticonSize\t\t图标大小（默认 17 ）\r\n\t * @property {String}\t\t\tcolor\t\t\t字体颜色（默认 '#606266' ）\r\n\t * @property {String}\t\t\tloadingIcon\t\t加载图标（默认 'circle' ）\r\n\t * @property {String}\t\t\tloadmoreText\t加载前的提示语（默认 '加载更多' ）\r\n\t * @property {String}\t\t\tloadingText\t\t加载中提示语（默认 '正在加载...' ）\r\n\t * @property {String}\t\t\tnomoreText\t\t没有更多的提示语（默认 '没有更多了' ）\r\n\t * @property {Boolean}\t\t\tisDot\t\t\t到上一个相邻元素的距离 （默认 false ）\r\n\t * @property {String}\t\t\ticonColor\t\t加载中图标的颜色 （默认 '#b7b7b7' ）\r\n\t * @property {String}\t\t\tlineColor\t\t线条颜色（默认 #E6E8EB ）\r\n\t * @property {String | Number}\tmarginTop\t\t上边距 （默认 10 ）\r\n\t * @property {String | Number}\tmarginBottom\t下边距 （默认 10 ）\r\n\t * @property {String | Number}\theight\t\t\t高度，单位px （默认 'auto' ）\r\n\t * @property {Boolean}\t\t\tline\t\t\t是否显示左边分割线  （默认 false ）\r\n\t * @property {Boolean}\t\t\tdashed\t\t// 是否虚线，true-虚线，false-实线  （默认 false ）\r\n\t * @pages_event {Function} loadmore status为loadmore时，点击组件会发出此事件\r\n\t * @example <u-loadmore :status=\"status\" icon-type=\"iconType\" load-text=\"loadText\" />\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-loadmore\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 粗点\r\n\t\t\t\tdotText: \"●\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 加载的文字显示的样式\r\n\t\t\tloadTextStyle() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tcolor: this.color,\r\n\t\t\t\t\tfontSize: addUnit(this.fontSize),\r\n\t\t\t\t\tlineHeight: addUnit(this.fontSize),\r\n\t\t\t\t\tbackgroundColor: this.bgColor,\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 显示的提示文字\r\n\t\t\tshowText() {\r\n\t\t\t\tlet text = '';\r\n\t\t\t\tif (this.status == 'loadmore') text = this.loadmoreText\r\n\t\t\t\telse if (this.status == 'loading') text = this.loadingText\r\n\t\t\t\telse if (this.status == 'nomore' && this.isDot) text = this.dotText;\r\n\t\t\t\telse text = this.nomoreText;\r\n\t\t\t\treturn text;\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: [\"loadmore\"],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\tloadMore() {\r\n\t\t\t\t// 只有在“加载更多”的状态下才发送点击事件，内容不满一屏时无法触发底部上拉事件，所以需要点击来触发\r\n\t\t\t\tif (this.status == 'loadmore') this.$emit('loadmore');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-loadmore {\r\n\t\t@include flex(row);\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\r\n\t\t&__content {\r\n\t\t\tmargin: 0 15px;\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__icon-wrap {\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t&__text {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: $u-content-color;\r\n\t\t\t}\r\n\r\n\t\t\t&__dot-text {\r\n\t\t\t\tfont-size: 15px;\r\n\t\t\t\tcolor: $u-tips-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-loadmore/u-loadmore.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "addStyle"], "mappings": ";;;;;;AAmFC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,sDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,gBAAgB;AACf,aAAO;AAAA,QACN,OAAO,KAAK;AAAA,QACZ,UAAUC,0CAAAA,QAAQ,KAAK,QAAQ;AAAA,QAC/B,YAAYA,0CAAAA,QAAQ,KAAK,QAAQ;AAAA,QACjC,iBAAiB,KAAK;AAAA,MACvB;AAAA,IACA;AAAA;AAAA,IAED,WAAW;AACV,UAAI,OAAO;AACX,UAAI,KAAK,UAAU;AAAY,eAAO,KAAK;AAAA,eAClC,KAAK,UAAU;AAAW,eAAO,KAAK;AAAA,eACtC,KAAK,UAAU,YAAY,KAAK;AAAO,eAAO,KAAK;AAAA;AACvD,eAAO,KAAK;AACjB,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO,CAAC,UAAU;AAAA,EAClB,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAD,0CAAO;AAAA,IACP,WAAW;AAEV,UAAI,KAAK,UAAU;AAAY,aAAK,MAAM,UAAU;AAAA,IACrD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxHD,GAAG,gBAAgB,SAAS;"}