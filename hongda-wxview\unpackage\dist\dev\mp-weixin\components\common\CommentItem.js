"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "CommentItem",
  props: {
    comment: {
      type: Object,
      required: true
    },
    maxDisplayReplies: {
      type: Number,
      default: 3
    }
  },
  emits: ["reply"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const formatDateTime = (time) => {
      if (!time)
        return "";
      const date = new Date(time);
      const Y = date.getFullYear();
      const M = (date.getMonth() + 1).toString().padStart(2, "0");
      const D = date.getDate().toString().padStart(2, "0");
      const h = date.getHours().toString().padStart(2, "0");
      const m = date.getMinutes().toString().padStart(2, "0");
      const s = date.getSeconds().toString().padStart(2, "0");
      return `${Y}-${M}-${D} ${h}:${m}:${s}`;
    };
    const isExpanded = common_vendor.ref(false);
    const flattenReplies = (children) => {
      const result = [];
      const traverse = (nodes) => {
        if (!nodes || !Array.isArray(nodes))
          return;
        nodes.forEach((node) => {
          result.push({
            ...node,
            replyToNickname: node.replyToNickname || null
          });
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        });
      };
      traverse(children);
      return result;
    };
    const flatReplies = common_vendor.computed(() => {
      if (!props.comment.children || !Array.isArray(props.comment.children)) {
        return [];
      }
      return flattenReplies(props.comment.children);
    });
    const displayedReplies = common_vendor.computed(() => {
      if (isExpanded.value || flatReplies.value.length <= props.maxDisplayReplies) {
        return flatReplies.value;
      }
      return flatReplies.value.slice(0, props.maxDisplayReplies);
    });
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
    };
    const getCurrentPageUrl = () => {
      try {
        const pages = getCurrentPages();
        const current = pages[pages.length - 1];
        const route = "/" + current.route;
        const options = current.options || {};
        const query = Object.keys(options).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`).join("&");
        return query ? `${route}?${query}` : route;
      } catch (e) {
        return "";
      }
    };
    const ensureLoggedIn = () => {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          const backUrl = getCurrentPageUrl();
          try {
            if (backUrl)
              common_vendor.index.setStorageSync("loginBackPage", backUrl);
          } catch (e) {
          }
          common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
          return false;
        }
        return true;
      } catch (e) {
        common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
        return false;
      }
    };
    const handleReplyClick = (target) => {
      if (!ensureLoggedIn())
        return;
      emit("reply", target);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.comment.avatarUrl || "/static/images/default-avatar.png",
        b: common_vendor.t(__props.comment.nickname || "匿名用户"),
        c: common_vendor.t(formatDateTime(__props.comment.createTime)),
        d: common_vendor.t(__props.comment.content),
        e: common_vendor.o(($event) => handleReplyClick(__props.comment)),
        f: flatReplies.value.length > 0
      }, flatReplies.value.length > 0 ? common_vendor.e({
        g: common_vendor.f(displayedReplies.value, (reply, k0, i0) => {
          return common_vendor.e({
            a: reply.avatarUrl || "/static/images/default-avatar.png",
            b: common_vendor.t(reply.nickname || "匿名用户"),
            c: reply.replyToNickname
          }, reply.replyToNickname ? {
            d: common_vendor.t(reply.replyToNickname)
          } : {}, {
            e: common_vendor.t(reply.content),
            f: common_vendor.t(formatDateTime(reply.createTime)),
            g: common_vendor.o(($event) => handleReplyClick(reply), reply.id),
            h: reply.id
          });
        }),
        h: flatReplies.value.length > __props.maxDisplayReplies
      }, flatReplies.value.length > __props.maxDisplayReplies ? {
        i: common_vendor.t(isExpanded.value ? `收起回复` : `共${flatReplies.value.length}条回复 >`),
        j: common_vendor.o(toggleExpand)
      } : {}) : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c3ec56f9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/CommentItem.js.map
