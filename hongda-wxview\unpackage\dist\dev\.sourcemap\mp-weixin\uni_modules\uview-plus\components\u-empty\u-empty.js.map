{"version": 3, "file": "u-empty.js", "sources": ["uni_modules/uview-plus/components/u-empty/u-empty.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWVtcHR5L3UtZW1wdHkudnVl"], "sourcesContent": ["<template>\r\n\t<view\r\n\t    class=\"u-empty\"\r\n\t    :style=\"[emptyStyle]\"\r\n\t    v-if=\"show\"\r\n\t>\r\n\t\t<u-icon\r\n\t\t    v-if=\"!isSrc\"\r\n\t\t    :name=\"mode === 'message' ? 'chat' : `empty-${mode}`\"\r\n\t\t    :size=\"iconSize\"\r\n\t\t    :color=\"iconColor\"\r\n\t\t    margin-top=\"14\"\r\n\t\t></u-icon>\r\n\t\t<image\r\n\t\t    v-else\r\n\t\t    :style=\"{\r\n\t\t\t\twidth: addUnit(width),\r\n\t\t\t\theight: addUnit(height),\r\n\t\t\t}\"\r\n\t\t    :src=\"icon\"\r\n\t\t    mode=\"widthFix\"\r\n\t\t></image>\r\n\t\t<text\r\n\t\t    class=\"u-empty__text\"\r\n\t\t    :style=\"[textStyle]\"\r\n\t\t>{{text ? text : icons[mode]}}</text>\r\n\t\t<view class=\"u-empty__wrap\" v-if=\"$slots.default || $slots.$default\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\r\n\t/**\r\n\t * empty 内容为空\r\n\t * @description 该组件用于需要加载内容，但是加载的第一页数据就为空，提示一个\"没有内容\"的场景， 我们精心挑选了十几个场景的图标，方便您使用。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/empty.html\r\n\t * @property {String}\t\t\ticon\t\t内置图标名称，或图片路径，建议绝对路径\r\n\t * @property {String}\t\t\ttext\t\t提示文字\r\n\t * @property {String}\t\t\ttextColor\t文字颜色 (默认 '#c0c4cc' )\r\n\t * @property {String | Number}\ttextSize\t文字大小 （默认 14 ）\r\n\t * @property {String}\t\t\ticonColor\t图标的颜色 （默认 '#c0c4cc' ）\r\n\t * @property {String | Number}\ticonSize\t图标的大小 （默认 90 ）\r\n\t * @property {String}\t\t\tmode\t\t选择预置的图标类型 （默认 'data' ）\r\n\t * @property {String | Number}\twidth\t\t图标宽度，单位px （默认 160 ）\r\n\t * @property {String | Number}\theight\t\t图标高度，单位px （默认 160 ）\r\n\t * @property {Boolean}\t\t\tshow\t\t是否显示组件 （默认 true ）\r\n\t * @property {String | Number}\tmarginTop\t组件距离上一个元素之间的距离，默认px单位 （默认 0 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * \r\n\t * @pages_event {Function} click 点击组件时触发\r\n\t * @pages_event {Function} close 点击关闭按钮时触发\r\n\t * @example <u-empty text=\"所谓伊人，在水一方\" mode=\"list\"></u-empty>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-empty\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ticons: {\r\n\t\t\t\t\tcar: '购物车为空',\r\n\t\t\t\t\tpage: '页面不存在',\r\n\t\t\t\t\tsearch: '没有搜索结果',\r\n\t\t\t\t\taddress: '没有收货地址',\r\n\t\t\t\t\twifi: '没有WiFi',\r\n\t\t\t\t\torder: '订单为空',\r\n\t\t\t\t\tcoupon: '没有优惠券',\r\n\t\t\t\t\tfavor: '暂无收藏',\r\n\t\t\t\t\tpermission: '无权限',\r\n\t\t\t\t\thistory: '无历史记录',\r\n\t\t\t\t\tnews: '无新闻列表',\r\n\t\t\t\t\tmessage: '消息列表为空',\r\n\t\t\t\t\tlist: '列表为空',\r\n\t\t\t\t\tdata: '数据为空',\r\n\t\t\t\t\tcomment: '暂无评论',\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 组件样式\r\n\t\t\temptyStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tstyle.marginTop = addUnit(this.marginTop)\r\n\t\t\t\t// 合并customStyle样式，此参数通过mixin中的props传递\r\n\t\t\t\treturn deepMerge(addStyle(this.customStyle), style)\r\n\t\t\t},\r\n\t\t\t// 文本样式\r\n\t\t\ttextStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tstyle.color = this.textColor\r\n\t\t\t\tstyle.fontSize = addUnit(this.textSize)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 判断icon是否图片路径\r\n\t\t\tisSrc() {\r\n\t\t\t\treturn this.icon.indexOf('/') >= 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddUnit\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$u-empty-text-margin-top:20rpx !default;\r\n\t$u-empty-slot-margin-top:20rpx !default;\r\n\r\n\t.u-empty {\r\n\t\t@include flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\r\n\t\t&__text {\r\n\t\t\t@include flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-top: $u-empty-text-margin-top;\r\n\t\t}\r\n\t}\r\n\t\t.u-slot-wrap {\r\n\t\t\t@include flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-top:$u-empty-slot-margin-top;\r\n\t\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-empty/u-empty.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AA0DC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,mDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,aAAa;AACZ,YAAM,QAAQ,CAAC;AACf,YAAM,YAAYC,kDAAQ,KAAK,SAAS;AAExC,aAAOC,0CAAS,UAACC,0CAAQ,SAAC,KAAK,WAAW,GAAG,KAAK;AAAA,IAClD;AAAA;AAAA,IAED,YAAY;AACX,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAWF,kDAAQ,KAAK,QAAQ;AACtC,aAAO;AAAA,IACP;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,KAAK,QAAQ,GAAG,KAAK;AAAA,IAClC;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,SAAAA,0CAAM;AAAA,EACP;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxGD,GAAG,gBAAgB,SAAS;"}