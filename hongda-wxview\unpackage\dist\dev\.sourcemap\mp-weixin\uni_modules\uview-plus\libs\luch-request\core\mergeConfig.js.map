{"version": 3, "file": "mergeConfig.js", "sources": ["uni_modules/uview-plus/libs/luch-request/core/mergeConfig.js"], "sourcesContent": ["import { deepMerge, isUndefined } from '../utils'\r\n\r\n/**\r\n * 合并局部配置优先的配置，如果局部有该配置项则用局部，如果全局有该配置项则用全局\r\n * @param {Array} keys - 配置项\r\n * @param {Object} globalsConfig - 当前的全局配置\r\n * @param {Object} config2 - 局部配置\r\n * @return {{}}\r\n */\r\nconst mergeKeys = (keys, globalsConfig, config2) => {\r\n    const config = {}\r\n    keys.forEach((prop) => {\r\n        if (!isUndefined(config2[prop])) {\r\n            config[prop] = config2[prop]\r\n        } else if (!isUndefined(globalsConfig[prop])) {\r\n            config[prop] = globalsConfig[prop]\r\n        }\r\n    })\r\n    return config\r\n}\r\n/**\r\n *\r\n * @param globalsConfig - 当前实例的全局配置\r\n * @param config2 - 当前的局部配置\r\n * @return - 合并后的配置\r\n */\r\nexport default (globalsConfig, config2 = {}) => {\r\n    const method = config2.method || globalsConfig.method || 'GET'\r\n    let config = {\r\n        baseURL: globalsConfig.baseURL || '',\r\n        method,\r\n        url: config2.url || '',\r\n        params: config2.params || {},\r\n        custom: { ...(globalsConfig.custom || {}), ...(config2.custom || {}) },\r\n        header: deepMerge(globalsConfig.header || {}, config2.header || {})\r\n    }\r\n    const defaultToConfig2Keys = ['getTask', 'validateStatus']\r\n    config = { ...config, ...mergeKeys(defaultToConfig2Keys, globalsConfig, config2) }\r\n\r\n    // eslint-disable-next-line no-empty\r\n    if (method === 'DOWNLOAD') {\r\n    // #ifdef H5 || APP-PLUS\r\n        if (!isUndefined(config2.timeout)) {\r\n            config.timeout = config2.timeout\r\n        } else if (!isUndefined(globalsConfig.timeout)) {\r\n            config.timeout = globalsConfig.timeout\r\n        }\r\n    // #endif\r\n    } else if (method === 'UPLOAD') {\r\n        delete config.header['content-type']\r\n        delete config.header['Content-Type']\r\n        const uploadKeys = [\r\n            // #ifdef APP-PLUS || H5\r\n            'files',\r\n            // #endif\r\n            // #ifdef MP-ALIPAY\r\n            'fileType',\r\n            // #endif\r\n            // #ifdef H5\r\n            'file',\r\n            // #endif\r\n            'filePath',\r\n            'name',\r\n            // #ifdef H5 || APP-PLUS\r\n            'timeout',\r\n            // #endif\r\n            'formData'\r\n        ]\r\n        uploadKeys.forEach((prop) => {\r\n            if (!isUndefined(config2[prop])) {\r\n                config[prop] = config2[prop]\r\n            }\r\n        })\r\n        // #ifdef H5 || APP-PLUS\r\n        if (isUndefined(config.timeout) && !isUndefined(globalsConfig.timeout)) {\r\n            config.timeout = globalsConfig.timeout\r\n        }\r\n    // #endif\r\n    } else {\r\n        const defaultsKeys = [\r\n            'data',\r\n            // #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN\r\n            'timeout',\r\n            // #endif\r\n            'dataType',\r\n            // #ifndef MP-ALIPAY\r\n            'responseType',\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            'sslVerify',\r\n            // #endif\r\n            // #ifdef H5\r\n            'withCredentials',\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            'firstIpv4'\r\n            // #endif\r\n        ]\r\n        config = { ...config, ...mergeKeys(defaultsKeys, globalsConfig, config2) }\r\n    }\r\n\r\n    return config\r\n}\r\n"], "names": ["isUndefined", "deepMerge"], "mappings": ";;AASA,MAAM,YAAY,CAAC,MAAM,eAAe,YAAY;AAChD,QAAM,SAAS,CAAE;AACjB,OAAK,QAAQ,CAAC,SAAS;AACnB,QAAI,CAACA,6CAAW,YAAC,QAAQ,IAAI,CAAC,GAAG;AAC7B,aAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,IAC9B,WAAU,CAACA,6CAAW,YAAC,cAAc,IAAI,CAAC,GAAG;AAC1C,aAAO,IAAI,IAAI,cAAc,IAAI;AAAA,IACpC;AAAA,EACT,CAAK;AACD,SAAO;AACX;AAOA,MAAA,cAAe,CAAC,eAAe,UAAU,OAAO;AAC5C,QAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AACzD,MAAI,SAAS;AAAA,IACT,SAAS,cAAc,WAAW;AAAA,IAClC;AAAA,IACA,KAAK,QAAQ,OAAO;AAAA,IACpB,QAAQ,QAAQ,UAAU,CAAE;AAAA,IAC5B,QAAQ,EAAE,GAAI,cAAc,UAAU,CAAE,GAAG,GAAI,QAAQ,UAAU,GAAK;AAAA,IACtE,QAAQC,6CAAS,UAAC,cAAc,UAAU,CAAA,GAAI,QAAQ,UAAU,EAAE;AAAA,EACrE;AACD,QAAM,uBAAuB,CAAC,WAAW,gBAAgB;AACzD,WAAS,EAAE,GAAG,QAAQ,GAAG,UAAU,sBAAsB,eAAe,OAAO,EAAG;AAGlF,MAAI,WAAW;AAAY;AAAA,WAQhB,WAAW,UAAU;AAC5B,WAAO,OAAO,OAAO,cAAc;AACnC,WAAO,OAAO,OAAO,cAAc;AACnC,UAAM,aAAa;AAAA,MAUf;AAAA,MACA;AAAA,MAIA;AAAA,IACH;AACD,eAAW,QAAQ,CAAC,SAAS;AACzB,UAAI,CAACD,6CAAW,YAAC,QAAQ,IAAI,CAAC,GAAG;AAC7B,eAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,MAC9B;AAAA,IACb,CAAS;AAAA,EAMT,OAAW;AACH,UAAM,eAAe;AAAA,MACjB;AAAA,MAEA;AAAA,MAEA;AAAA,MAEA;AAAA,IAWH;AACD,aAAS,EAAE,GAAG,QAAQ,GAAG,UAAU,cAAc,eAAe,OAAO,EAAG;AAAA,EAC7E;AAED,SAAO;AACX;;"}