{"version": 3, "file": "u-navbar.js", "sources": ["uni_modules/uview-plus/components/u-navbar/u-navbar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LW5hdmJhci91LW5hdmJhci52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"u-navbar\" :class=\"[customClass]\">\r\n\t\t<view\r\n\t\t\tclass=\"u-navbar__placeholder\"\r\n\t\t\tv-if=\"fixed && placeholder\"\r\n\t\t\t:style=\"{\r\n\t\t\t\theight: addUnit(getPx(height) + getWindowInfo().statusBarHeight,'px'),\r\n\t\t\t}\"\r\n\t\t></view>\r\n\t\t<view :class=\"[fixed && 'u-navbar--fixed']\">\r\n\t\t\t<u-status-bar\r\n\t\t\t\tv-if=\"safeAreaInsetTop\"\r\n\t\t\t\t:bgColor=\"statusBarBgColor ? statusBarBgColor : bgColor\"\r\n\t\t\t></u-status-bar>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-navbar__content\"\r\n\t\t\t\t:class=\"[border && 'u-border-bottom']\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\theight: addUnit(height),\r\n\t\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-navbar__content__left\"\r\n\t\t\t\t\thover-class=\"u-navbar__content__left--hover\"\r\n\t\t\t\t\thover-start-time=\"150\"\r\n\t\t\t\t\t@tap=\"leftClick\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\tv-if=\"leftIcon\"\r\n\t\t\t\t\t\t\t:name=\"leftIcon\"\r\n\t\t\t\t\t\t\t:size=\"leftIconSize\"\r\n\t\t\t\t\t\t\t:color=\"leftIconColor\"\r\n\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tv-if=\"leftText\"\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\tcolor: leftIconColor\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\tclass=\"u-navbar__content__left__text\"\r\n\t\t\t\t\t\t>{{ leftText }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot name=\"center\">\r\n\t\t\t\t\t<text\r\n\t\t\t\t\t\tclass=\"u-line-1 u-navbar__content__title\"\r\n\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\twidth: addUnit(titleWidth),\r\n\t\t\t\t\t\t\tcolor: titleColor,\r\n\t\t\t\t\t\t}, addStyle(titleStyle)]\"\r\n\t\t\t\t\t>{{ title }}</text>\r\n\t\t\t\t</slot>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-navbar__content__right\"\r\n\t\t\t\t\tv-if=\"$slots.right || rightIcon || rightText\"\r\n\t\t\t\t\t@tap=\"rightClick\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\tv-if=\"rightIcon\"\r\n\t\t\t\t\t\t\t:name=\"rightIcon\"\r\n\t\t\t\t\t\t\tsize=\"20\"\r\n\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tv-if=\"rightText\"\r\n\t\t\t\t\t\t\tclass=\"u-navbar__content__right__text\"\r\n\t\t\t\t\t\t>{{ rightText }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport config  from '../../libs/config/config';\r\n\timport { addUnit, addStyle, getPx, getWindowInfo } from '../../libs/function/index';\r\n\t/**\r\n\t * Navbar 自定义导航栏\r\n\t * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uni-app带的导航栏。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/navbar.html\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否开启顶部安全区适配  （默认 true ）\r\n\t * @property {Boolean}\t\t\tplaceholder\t\t\t固定在顶部时，是否生成一个等高元素，以防止塌陷 （默认 false ）\r\n\t * @property {Boolean}\t\t\tfixed\t\t\t\t导航栏是否固定在顶部 （默认 false ）\r\n\t * @property {Boolean}\t\t\tborder\t\t\t\t导航栏底部是否显示下边框 （默认 false ）\r\n\t * @property {String}\t\t\tleftIcon\t\t\t左边返回图标的名称，只能为uview-pls自带的图标 （默认 'arrow-left' ）\r\n\t * @property {String}\t\t\tleftText\t\t\t左边的提示文字\r\n\t * @property {String}\t\t\trightText\t\t\t右边的提示文字\r\n\t * @property {String}\t\t\trightIcon\t\t\t右边返回图标的名称，只能为uview-plus自带的图标\r\n\t * @property {String}\t\t\ttitle\t\t\t\t导航栏标题，如设置为空字符，将会隐藏标题占位区域\r\n\t * @property {String}\t\t\ttitleColor\t\t\t文字颜色 （默认 '' ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t\t导航栏背景设置 （默认 '#ffffff' ）\r\n\t * @property {String}\t\t\tstatusBarBgColor\t状态栏背景颜色 不写同导航栏背景设置\r\n\t * @property {String | Number}\ttitleWidth\t\t\t导航栏标题的最大宽度，内容超出会以省略号隐藏 （默认 '400rpx' ）\r\n\t * @property {String | Number}\theight\t\t\t\t导航栏高度(不包括状态栏高度在内，内部自动加上)（默认 '44px' ）\r\n\t * @property {String | Number}\tleftIconSize\t\t左侧返回图标的大小（默认 20px ）\r\n\t * @property {String | Number}\tleftIconColor\t\t左侧返回图标的颜色（默认 #303133 ）\r\n\t * @property {Boolean}\t        autoBack\t\t\t点击左侧区域(返回图标)，是否自动返回上一页（默认 false ）\r\n\t * @property {Object | String}\ttitleStyle\t\t\t标题的样式，对象或字符串\r\n\t * @pages_event {Function} leftClick\t\t点击左侧区域\r\n\t * @pages_event {Function} rightClick\t\t点击右侧区域\r\n\t * @example <u-navbar title=\"剑未配妥，出门已是江湖\" left-text=\"返回\" right-text=\"帮助\" @click-left=\"onClickBack\" @click-right=\"onClickRight\"></u-navbar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-navbar',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: [\"leftClick\", \"rightClick\"],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\tgetWindowInfo,\r\n\t\t\tgetPx,\r\n\t\t\t// 点击左侧区域\r\n\t\t\tleftClick() {\r\n\t\t\t\t// 如果配置了autoBack，自动返回上一页\r\n\t\t\t\tthis.$emit('leftClick')\r\n\t\t\t\tif (config.interceptor.navbarLeftClick != null) {\r\n\t\t\t\t\tconfig.interceptor.navbarLeftClick()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif(this.autoBack) {\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击右侧区域\r\n\t\t\trightClick() {\r\n\t\t\t\tthis.$emit('rightClick')\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-navbar {\r\n\r\n\t\t&--fixed {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tz-index: 11;\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\theight: 44px;\r\n\t\t\tbackground-color: #9acafc;\r\n\t\t\tposition: relative;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__left,\r\n\t\t\t&__right {\r\n\t\t\t\tpadding: 0 13px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\t@include flex(row);\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\r\n\t\t\t&__left {\r\n\t\t\t\tleft: 0;\r\n\r\n\t\t\t\t&--hover {\r\n\t\t\t\t\topacity: 0.7;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\tmargin-left: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__title {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t}\r\n\r\n\t\t\t&__right {\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\tmargin-left: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-navbar/u-navbar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle", "addUnit", "getWindowInfo", "getPx", "config", "uni"], "mappings": ";;;;;;;AA2GC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,oDAAK;AAAA,EAC9B,OAAO;AACN,WAAO,CACP;AAAA,EACA;AAAA,EACD,OAAO,CAAC,aAAa,YAAY;AAAA,EACjC,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAC,0CAAO;AAAA,mBACPC,0CAAa;AAAA,WACbC,0CAAK;AAAA;AAAA,IAEL,YAAY;AAEX,WAAK,MAAM,WAAW;AACtB,UAAIC,gDAAO,YAAY,mBAAmB,MAAM;AAC/CA,iDAAM,OAAC,YAAY,gBAAgB;AAAA,aAC7B;AACN,YAAG,KAAK,UAAU;AACjBC,wBAAAA,MAAI,aAAa;AAAA,QAClB;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAED,aAAa;AACZ,WAAK,MAAM,YAAY;AAAA,IACvB;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxID,GAAG,gBAAgB,SAAS;"}