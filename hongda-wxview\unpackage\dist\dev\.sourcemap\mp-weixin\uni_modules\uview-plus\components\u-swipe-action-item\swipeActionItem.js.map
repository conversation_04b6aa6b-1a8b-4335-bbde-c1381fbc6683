{"version": 3, "file": "swipeActionItem.js", "sources": ["uni_modules/uview-plus/components/u-swipe-action-item/swipeActionItem.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:01:13\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeActionItem.js\r\n */\r\nexport default {\r\n    // swipeActionItem 组件\r\n    swipeActionItem: {\r\n        show: false,\r\n        closeOnClick: true,\r\n        name: '',\r\n        disabled: false,\r\n        threshold: 20,\r\n        autoClose: true,\r\n        options: [],\r\n        duration: 300\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,kBAAA;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS,CAAE;AAAA,IACX,UAAU;AAAA,EACb;AACL;;"}