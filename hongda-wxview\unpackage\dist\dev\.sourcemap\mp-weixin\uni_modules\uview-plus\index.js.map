{"version": 3, "file": "index.js", "sources": ["uni_modules/uview-plus/index.js"], "sourcesContent": ["// 看到此报错，是因为没有配置vite.config.js的【transpileDependencies】\r\n// const pleaseSetTranspileDependencies = {}, babelTest = pleaseSetTranspileDependencies?.test\r\n\r\n// 引入全局mixin\r\nimport { mixin } from './libs/mixin/mixin.js'\r\n// 小程序特有的mixin\r\nimport { mpMixin } from './libs/mixin/mpMixin.js'\r\n\r\n// 路由封装\r\nimport route from './libs/util/route.js'\r\n// 颜色渐变相关,colorGradient-颜色渐变,hexToRgb-十六进制颜色转rgb颜色,rgbToHex-rgb转十六进制\r\nimport colorGradient from './libs/function/colorGradient.js'\r\n\r\n// 规则检验\r\nimport test from './libs/function/test.js'\r\n// 防抖方法\r\nimport debounce from './libs/function/debounce.js'\r\n// 节流方法\r\nimport throttle from './libs/function/throttle.js'\r\n// 浮点计算\r\nimport calc from './libs/function/calc.js'\r\n// 浮点计算\r\nimport digit from './libs/function/digit.js'\r\n// 公共文件写入的方法\r\nimport index from './libs/function/index.js'\r\n\r\n// 配置信息\r\nimport config from './libs/config/config.js'\r\n// props配置信息\r\nimport props from './libs/config/props.js'\r\n// 各个需要fixed的地方的z-index配置文件\r\nimport zIndex from './libs/config/zIndex.js'\r\n// 关于颜色的配置，特殊场景使用\r\nimport color from './libs/config/color.js'\r\n// 平台\r\nimport platform from './libs/function/platform'\r\n\r\n// http\r\nimport http from './libs/function/http.js'\r\n\r\n// fontUtil\r\nimport fontUtil from './components/u-icon/util.js';\r\n\r\n// 导出\r\nlet themeType = ['primary', 'success', 'error', 'warning', 'info'];\r\nexport { route, http, debounce, throttle, calc, digit, platform, themeType, mixin, mpMixin, props, color, test, zIndex, fontUtil }\r\nexport * from './libs/function/index.js'\r\nexport * from './libs/function/colorGradient.js'\r\n\r\n/**\r\n * @description 修改uView内置属性值\r\n * @param {object} props 修改内置props属性\r\n * @param {object} config 修改内置config属性\r\n * @param {object} color 修改内置color属性\r\n * @param {object} zIndex 修改内置zIndex属性\r\n */\r\nexport function setConfig(configs) {\r\n\tindex.shallowMerge(config, configs.config || {})\r\n\tindex.shallowMerge(props, configs.props || {})\r\n\tindex.shallowMerge(color, configs.color || {})\r\n\tindex.shallowMerge(zIndex, configs.zIndex || {})\r\n}\r\nindex.setConfig = setConfig\r\n\r\nconst $u = {\r\n    route,\r\n    date: index.timeFormat, // 另名date\r\n    colorGradient: colorGradient.colorGradient,\r\n    hexToRgb: colorGradient.hexToRgb,\r\n    rgbToHex: colorGradient.rgbToHex,\r\n    colorToRgba: colorGradient.colorToRgba,\r\n    test,\r\n    type: themeType,\r\n    http,\r\n    config, // uview-plus配置信息相关，比如版本号\r\n    zIndex,\r\n    debounce,\r\n    throttle,\r\n\tcalc,\r\n    mixin,\r\n    mpMixin,\r\n    props,\r\n    ...index,\r\n    color,\r\n    platform\r\n}\r\n\r\nexport const mount$u = function() {\r\n    uni.$u = $u\r\n}\r\n\r\nfunction toCamelCase(str) {\r\n    return str.replace(/-([a-z])/g, function(match, group1) {\r\n      return group1.toUpperCase();\r\n    }).replace(/^[a-z]/, function(match) {\r\n      return match.toUpperCase();\r\n    });\r\n}\r\n\r\n// #ifdef APP || H5\r\nconst importFn = import.meta.glob('./components/u-*/u-*.vue', { eager: true })\r\nlet components = [];\r\n\r\n// 批量注册全局组件\r\nfor (const key in importFn) {\r\n    let component = importFn[key].default;\r\n    if (component.name && component.name.indexOf('u--') !== 0) {\r\n        component.install = function (Vue) {\r\n            Vue.component(name, component);\r\n        };\r\n        \r\n        // 导入组件\r\n        components.push(component);\r\n    }\r\n}\r\n// #endif\r\n\r\nconst install = (Vue, upuiParams = '') => {\r\n    // #ifdef APP || H5\r\n    components.forEach(function(component) {\r\n        const name = component.name.replace(/u-([a-zA-Z0-9-_]+)/g, 'up-$1');\r\n\t\tif (name != component.name) {\r\n\t\t\tVue.component(component.name, component); \r\n\t\t}\r\n        Vue.component(name, component); \r\n    });\r\n    // #endif\r\n\t\r\n\t// 初始化\r\n\tif (upuiParams) {\r\n\t\tuni.upuiParams = upuiParams\r\n\t\tlet temp = upuiParams()\r\n\t\tif (temp.httpIns) {\r\n\t\t\ttemp.httpIns(http)\r\n\t\t}\r\n\t\tif (temp.options) {\r\n\t\t\tsetConfig(temp.options)\r\n\t\t}\r\n\t}\r\n\r\n    // 同时挂载到uni和Vue.prototype中\r\n    // $u挂载到uni对象上\r\n    uni.$u = $u\r\n\r\n    // #ifndef APP-NVUE\r\n    // 只有vue，挂载到Vue.prototype才有意义，因为nvue中全局Vue.prototype和Vue.mixin是无效的\r\n    Vue.config.globalProperties.$u = $u\r\n    Vue.mixin(mixin)\r\n    // #endif\r\n}\r\n\r\nexport default {\r\n    install\r\n}\r\n"], "names": ["index", "config", "props", "color", "zIndex", "route", "colorGradient", "test", "http", "debounce", "throttle", "calc", "mixin", "mpMixin", "platform", "uni"], "mappings": ";;;;;;;;;;;;;;;;;AA4CA,IAAI,YAAY,CAAC,WAAW,WAAW,SAAS,WAAW,MAAM;AAY1D,SAAS,UAAU,SAAS;AAClCA,4CAAAA,MAAM,aAAaC,yCAAAA,QAAQ,QAAQ,UAAU,CAAA,CAAE;AAC/CD,4CAAAA,MAAM,aAAaE,wCAAAA,OAAO,QAAQ,SAAS,CAAA,CAAE;AAC7CF,4CAAAA,MAAM,aAAaG,wCAAAA,OAAO,QAAQ,SAAS,CAAA,CAAE;AAC7CH,4CAAAA,MAAM,aAAaI,yCAAAA,QAAQ,QAAQ,UAAU,CAAA,CAAE;AAChD;AACAJ,0CAAK,MAAC,YAAY;AAElB,MAAM,KAAK;AAAA,EACX,OAAIK,sCAAK;AAAA,EACL,MAAML,0CAAK,MAAC;AAAA;AAAA,EACZ,eAAeM,kDAAa,cAAC;AAAA,EAC7B,UAAUA,kDAAa,cAAC;AAAA,EACxB,UAAUA,kDAAa,cAAC;AAAA,EACxB,aAAaA,kDAAa,cAAC;AAAA,EAC/B,MAAIC,yCAAI;AAAA,EACJ,MAAM;AAAA,EACV,MAAIC,yCAAI;AAAA,EACR,QAAIP,yCAAM;AAAA;AAAA,EACV,QAAIG,yCAAM;AAAA,EACV,UAAIK,6CAAQ;AAAA,EACZ,UAAIC,6CAAQ;AAAA,EACZ,MAACC,yCAAI;AAAA,EACL,OAAIC,uCAAK;AAAA,EACT,SAAIC,yCAAO;AAAA,EACX,OAAIX,wCAAK;AAAA,EACL,GAAGF,0CAAK;AAAA,EACZ,OAAIG,wCAAK;AAAA,EACT,UAAIW,6CAAQ;AACZ;AAgCA,MAAM,UAAU,CAAC,KAAK,aAAa,OAAO;AAYzC,MAAI,YAAY;AACfC,kBAAG,MAAC,aAAa;AACjB,QAAI,OAAO,WAAY;AACvB,QAAI,KAAK,SAAS;AACjB,WAAK,QAAQP,6CAAI;AAAA,IACjB;AACD,QAAI,KAAK,SAAS;AACjB,gBAAU,KAAK,OAAO;AAAA,IACtB;AAAA,EACD;AAIEO,gBAAG,MAAC,KAAK;AAIT,MAAI,OAAO,iBAAiB,KAAK;AACjC,MAAI,MAAMH,4CAAK;AAEnB;AAEA,MAAe,YAAA;AAAA,EACX;AACJ;;"}