<view wx:if="{{a}}" class="{{['u-text', 'data-v-0a574502', M]}}" style="{{N}}" bindtap="{{O}}"><text wx:if="{{b}}" class="{{['data-v-0a574502', 'u-text__price', c]}}" style="{{d}}">￥</text><view wx:if="{{e}}" class="u-text__prefix-icon data-v-0a574502"><u-icon wx:if="{{f}}" class="data-v-0a574502" u-i="0a574502-0" bind:__l="__l" u-p="{{f}}"></u-icon></view><u-link wx:if="{{g}}" style="{{'font-weight:' + h + ';' + ('word-wrap:' + i) + ';' + ('font-size:' + j)}}" class="{{['u-text__value', 'data-v-0a574502', k, l]}}" u-i="0a574502-1" bind:__l="__l" u-p="{{m}}"></u-link><block wx:elif="{{n}}"><button class="u-reset-button u-text__value data-v-0a574502" style="{{p}}" data-index="{{q}}" openType="{{r}}" bindgetuserinfo="{{s}}" bindcontact="{{t}}" bindgetphonenumber="{{v}}" binderror="{{w}}" bindlaunchapp="{{x}}" bindopensetting="{{y}}" lang="{{z}}" session-from="{{A}}" send-message-title="{{B}}" send-message-path="{{C}}" send-message-img="{{D}}" show-message-card="{{E}}" app-parameter="{{F}}">{{o}}</button></block><text wx:else style="{{H}}" class="{{['u-text__value', 'data-v-0a574502', I, J]}}">{{G}}</text><view wx:if="{{K}}" class="u-text__suffix-icon data-v-0a574502"><u-icon wx:if="{{L}}" class="data-v-0a574502" u-i="0a574502-2" bind:__l="__l" u-p="{{L}}"></u-icon></view></view>