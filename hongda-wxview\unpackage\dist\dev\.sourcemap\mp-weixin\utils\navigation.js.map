{"version": 3, "file": "navigation.js", "sources": ["utils/navigation.js"], "sourcesContent": ["/**\r\n * 统一导航跳转工具\r\n * @param {object} navItem - 包含 linkType 和 linkTarget 的导航对象\r\n */\r\nexport function navigateTo(navItem) {\r\n    if (!navItem || !navItem.linkType || !navItem.linkTarget) {\r\n        console.warn('无效的导航项:', navItem);\r\n        return;\r\n    }\r\n\r\n    const { linkType, linkTarget, title } = navItem;\r\n\r\n    switch (linkType) {\r\n        case 'INTERNAL_PAGE': // 内部普通页面\r\n            uni.navigateTo({\r\n                url: linkTarget\r\n            });\r\n            break;\r\n        case 'TAB_PAGE': // Tab栏页面\r\n            uni.switchTab({\r\n                url: linkTarget\r\n            });\r\n            break;\r\n        case 'EXTERNAL_LINK': // 外部H5链接\r\n            uni.navigateTo({\r\n                url: `/pages/webview/index?url=${encodeURIComponent(linkTarget)}&title=${title || ''}`\r\n            });\r\n            break;\r\n        // 可以根据需要扩展其他类型，例如跳转小程序等\r\n        // case 'MINI_PROGRAM':\r\n        //   uni.navigateToMiniProgram({...});\r\n        //   break;\r\n        default:\r\n            console.warn('未知的链接类型:', linkType);\r\n            break;\r\n    }\r\n}"], "names": ["uni"], "mappings": ";;AAIO,SAAS,WAAW,SAAS;AAChC,MAAI,CAAC,WAAW,CAAC,QAAQ,YAAY,CAAC,QAAQ,YAAY;AACtDA,kBAAA,MAAA,MAAA,QAAA,4BAAa,WAAW,OAAO;AAC/B;AAAA,EACH;AAED,QAAM,EAAE,UAAU,YAAY,MAAK,IAAK;AAExC,UAAQ,UAAQ;AAAA,IACZ,KAAK;AACDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK;AAAA,MACrB,CAAa;AACD;AAAA,IACJ,KAAK;AACDA,oBAAAA,MAAI,UAAU;AAAA,QACV,KAAK;AAAA,MACrB,CAAa;AACD;AAAA,IACJ,KAAK;AACDA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,4BAA4B,mBAAmB,UAAU,CAAC,UAAU,SAAS,EAAE;AAAA,MACpG,CAAa;AACD;AAAA,IAKJ;AACIA,oBAAA,MAAA,MAAA,QAAA,6BAAa,YAAY,QAAQ;AACjC;AAAA,EACP;AACL;;"}