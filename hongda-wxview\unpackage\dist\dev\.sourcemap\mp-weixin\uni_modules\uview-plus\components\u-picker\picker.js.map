{"version": 3, "file": "picker.js", "sources": ["uni_modules/uview-plus/components/u-picker/picker.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:18:20\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/picker.js\r\n */\r\nexport default {\r\n    // picker\r\n    picker: {\r\n        show: false,\r\n\t\tpopupMode: 'bottom',\r\n        showToolbar: true,\r\n        title: '',\r\n        columns: [],\r\n        loading: false,\r\n        itemHeight: 44,\r\n        cancelText: '取消',\r\n        confirmText: '确定',\r\n        cancelColor: '#909193',\r\n        confirmColor: '',\r\n        visibleItemCount: 5,\r\n        keyName: 'text',\r\n\t\tvalueName: 'value',\r\n        closeOnClickOverlay: false,\r\n        defaultIndex: [],\r\n\t\timmediateChange: true,\r\n\t\tzIndex: 10076,\r\n        disabled: false,\r\n        disabledColor: '',\r\n        placeholder: '请选择',\r\n        inputProps: {},\r\n        bgColor: '',\r\n        round: 0,\r\n        duration: 300,\r\n        overlayOpacity: 0.5\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,SAAS,CAAE;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACf,WAAW;AAAA,IACL,qBAAqB;AAAA,IACrB,cAAc,CAAE;AAAA,IACtB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACF,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,IACd,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,gBAAgB;AAAA,EACnB;AACL;;"}