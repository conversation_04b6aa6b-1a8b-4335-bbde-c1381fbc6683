{"version": 3, "file": "numberBox.js", "sources": ["uni_modules/uview-plus/components/u-number-box/numberBox.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:11:46\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberBox.js\r\n */\r\nexport default {\r\n    // 步进器组件\r\n    numberBox: {\r\n        name: '',\r\n        value: 0,\r\n        min: 1,\r\n        max: Number.MAX_SAFE_INTEGER,\r\n        step: 1,\r\n        integer: false,\r\n        disabled: false,\r\n        disabledInput: false,\r\n        asyncChange: false,\r\n        inputWidth: 35,\r\n        showMinus: true,\r\n        showPlus: true,\r\n        decimalLength: null,\r\n        longPress: true,\r\n        color: '#323233',\r\n        buttonWidth: 30,\r\n        buttonSize: 30,\r\n        buttonRadius: '0px',\r\n        bgColor: '#EBECEE',\r\n        inputBgColor: '#EBECEE',\r\n        cursorSpacing: 100,\r\n        disableMinus: false,\r\n        disablePlus: false,\r\n        iconStyle: '',\r\n        miniMode: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,EACb;AACL;;"}