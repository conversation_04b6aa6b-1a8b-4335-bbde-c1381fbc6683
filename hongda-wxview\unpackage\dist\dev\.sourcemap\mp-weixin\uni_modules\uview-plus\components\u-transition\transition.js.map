{"version": 3, "file": "transition.js", "sources": ["uni_modules/uview-plus/components/u-transition/transition.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:59:00\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/transition.js\r\n */\r\nexport default {\r\n    // transition动画组件的props\r\n    transition: {\r\n        show: false,\r\n        mode: 'fade',\r\n        duration: '300',\r\n        timingFunction: 'ease-out'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,EACnB;AACL;;"}