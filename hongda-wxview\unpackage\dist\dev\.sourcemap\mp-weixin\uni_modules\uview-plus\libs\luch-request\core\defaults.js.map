{"version": 3, "file": "defaults.js", "sources": ["uni_modules/uview-plus/libs/luch-request/core/defaults.js"], "sourcesContent": ["/**\r\n * 默认的全局配置\r\n */\r\n\r\nexport default {\r\n    baseURL: '',\r\n    header: {},\r\n    method: 'GET',\r\n    dataType: 'json',\r\n    // #ifndef MP-ALIPAY\r\n    responseType: 'text',\r\n    // #endif\r\n    custom: {},\r\n    // #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN\r\n    timeout: 60000,\r\n    // #endif\r\n    // #ifdef APP-PLUS\r\n    sslVerify: true,\r\n    // #endif\r\n    // #ifdef H5\r\n    withCredentials: false,\r\n    // #endif\r\n    // #ifdef APP-PLUS\r\n    firstIpv4: false,\r\n    // #endif\r\n    validateStatus: function validateStatus(status) {\r\n        return status >= 200 && status < 300\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AAIA,MAAe,WAAA;AAAA,EACX,SAAS;AAAA,EACT,QAAQ,CAAE;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EAEV,cAAc;AAAA,EAEd,QAAQ,CAAE;AAAA,EAEV,SAAS;AAAA,EAWT,gBAAgB,SAAS,eAAe,QAAQ;AAC5C,WAAO,UAAU,OAAO,SAAS;AAAA,EACpC;AACL;;"}