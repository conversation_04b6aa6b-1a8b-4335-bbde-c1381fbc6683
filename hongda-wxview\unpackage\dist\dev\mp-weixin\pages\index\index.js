"use strict";
const common_vendor = require("../../common/vendor.js");
const api_platform_ad = require("../../api/platform/ad.js");
if (!Math) {
  (HeaderComponent + BannerComponent + QuickNavigationComponent + CountryHighlightComponent + EventPromotionComponent + NewsListComponent + ActivityGridComponent + CustomTabBar + PopupAdComponent)();
}
const HeaderComponent = () => "../../components/home/<USER>";
const BannerComponent = () => "../../components/home/<USER>";
const QuickNavigationComponent = () => "../../components/home/<USER>";
const CountryHighlightComponent = () => "../../components/home/<USER>";
const NewsListComponent = () => "../../components/home/<USER>";
const ActivityGridComponent = () => "../../components/home/<USER>";
const EventPromotionComponent = () => "../../components/home/<USER>";
const PopupAdComponent = () => "../../components/common/PopupAdComponent.js";
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const AD_POSITION_CODE = "splash_screen";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const fabIconUrl = common_vendor.ref("");
    const showPopupAd = common_vendor.ref(false);
    const popupAdData = common_vendor.ref(null);
    const checkAndShowPopupAd = async () => {
      try {
        const response = await api_platform_ad.getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 1 });
        if (response && response.data && response.data.length > 0) {
          popupAdData.value = response.data[0];
          showPopupAd.value = true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:84", "获取弹窗广告失败:", error.message || error);
      }
    };
    const navigateToService = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_profile/contact"
      });
    };
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
      checkAndShowPopupAd();
      const assets = common_vendor.index.getStorageSync("staticAssets");
      fabIconUrl.value = (assets == null ? void 0 : assets.fab_customer_service_icon) || "";
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          current: 0
        }),
        b: fabIconUrl.value,
        c: common_vendor.o(navigateToService),
        d: showPopupAd.value
      }, showPopupAd.value ? {
        e: common_vendor.o(($event) => showPopupAd.value = false),
        f: common_vendor.p({
          ["ad-data"]: popupAdData.value
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
