{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-picker/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        modelValue: {\r\n            type: Array,\r\n            default: () => []\r\n        },\r\n        hasInput: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        inputProps: {\r\n            type: Object,\r\n            default: () => {\r\n                return {}\r\n            }\r\n        },\r\n        disabled: {\r\n            type: Boolean,\r\n            default: () => defProps.picker.disabled\r\n        },\r\n\t\tdisabledColor:{\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.picker.disabledColor\r\n\t\t},\r\n        placeholder: {\r\n            type: String,\r\n            default: () => defProps.picker.placeholder\r\n        },\r\n        // 是否展示picker弹窗\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.picker.show\r\n        },\r\n\t\t// 弹出的方向，可选值为 top bottom right left center\r\n        popupMode: {\r\n            type: String,\r\n            default: () => defProps.picker.popupMode\r\n        },\r\n        // 是否展示顶部的操作栏\r\n        showToolbar: {\r\n            type: Boolean,\r\n            default: () => defProps.picker.showToolbar\r\n        },\r\n        // 顶部标题\r\n        title: {\r\n            type: String,\r\n            default: () => defProps.picker.title\r\n        },\r\n        // 对象数组，设置每一列的数据\r\n        columns: {\r\n            type: Array,\r\n            default: () => defProps.picker.columns\r\n        },\r\n        // 是否显示加载中状态\r\n        loading: {\r\n            type: Boolean,\r\n            default: () => defProps.picker.loading\r\n        },\r\n        // 各列中，单个选项的高度\r\n        itemHeight: {\r\n            type: [String, Number],\r\n            default: () => defProps.picker.itemHeight\r\n        },\r\n        // 取消按钮的文字\r\n        cancelText: {\r\n            type: String,\r\n            default: () => defProps.picker.cancelText\r\n        },\r\n        // 确认按钮的文字\r\n        confirmText: {\r\n            type: String,\r\n            default: () => defProps.picker.confirmText\r\n        },\r\n        // 取消按钮的颜色\r\n        cancelColor: {\r\n            type: String,\r\n            default: () => defProps.picker.cancelColor\r\n        },\r\n        // 确认按钮的颜色\r\n        confirmColor: {\r\n            type: String,\r\n            default: () => defProps.picker.confirmColor\r\n        },\r\n        // 每列中可见选项的数量\r\n        visibleItemCount: {\r\n            type: [String, Number],\r\n            default: () => defProps.picker.visibleItemCount\r\n        },\r\n        // 选项对象中，需要展示的属性键名\r\n        keyName: {\r\n            type: String,\r\n            default: () => defProps.picker.keyName\r\n        },\r\n\t\t// 选项对象中，需要获取的属性值键名\r\n\t\tvalueName: {\r\n\t\t    type: String,\r\n\t\t    default: () => defProps.picker.valueName\r\n\t\t},\r\n        // 是否允许点击遮罩关闭选择器\r\n        closeOnClickOverlay: {\r\n            type: Boolean,\r\n            default: () => defProps.picker.closeOnClickOverlay\r\n        },\r\n        // 各列的默认索引\r\n        defaultIndex: {\r\n            type: Array,\r\n            default: () => defProps.picker.defaultIndex\r\n        },\r\n\t\t// 是否在手指松开时立即触发 change 事件。若不开启则会在滚动动画结束后触发 change 事件，只在微信2.21.1及以上有效\r\n\t\timmediateChange: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.picker.immediateChange\r\n\t\t},\r\n        // 工具栏右侧插槽是否开启\r\n        toolbarRightSlot: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 层级\r\n\t\tzIndex: {\r\n\t\t    type: [String, Number],\r\n\t\t    default: () => defProps.picker.zIndex\r\n\t\t},\r\n        // 弹窗背景色，设置为transparent可去除白色背景\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.picker.bgColor\r\n        },\r\n        // 是否显示圆角\r\n        round: {\r\n            type: [Boolean, String, Number],\r\n            default: () => defProps.picker.round\r\n        },\r\n        // 动画时长，单位ms\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.picker.duration\r\n        },\r\n        // 遮罩的透明度，0-1之间\r\n        overlayOpacity: {\r\n            type: [Number, String],\r\n            default: () => defProps.picker.overlayOpacity\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,IACpB;AAAA,IACD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,IACD,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM;AACX,eAAO,CAAE;AAAA,MACZ;AAAA,IACJ;AAAA,IACD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA,IACP,eAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA,IACK,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEP,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEK,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEP,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAEK,kBAAkB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEK,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,MAC9B,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}