{"version": 3, "file": "EventCalendarTimeline.js", "sources": ["components/event/EventCalendarTimeline.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRDYWxlbmRhclRpbWVsaW5lLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"calendar-timeline-wrapper\">\n    <view class=\"vertical-timeline\">\n        <view class=\"corner-notch\" :style=\"{ left: notchLeft }\" />\n      <view v-for=\"(group, index) in groups\" :key=\"group.date\" class=\"date-section\">\n        <view class=\"date-header\">\n          <view class=\"timeline-dot\"></view>\n          <text class=\"time-text\">{{ group.formattedDate }}</text>\n          <text class=\"weekday-text\">{{ group.dayOfWeek }}</text>\n        </view>\n\n        <view class=\"line-connector\" v-if=\"index < groups.length - 1\">\n          <view class=\"timeline-line\"></view>\n        </view>\n\n        <view class=\"events-container\">\n          <view\n            v-for=\"event in group.events\"\n            :key=\"event.id\"\n            class=\"compact-event-card\"\n            @click=\"$emit('click-item', event)\"\n          >\n            <image :src=\"getFullImageUrl(event.iconUrl)\" class=\"event-avatar\" mode=\"aspectFill\" />\n            <view class=\"event-content\">\n              <text class=\"event-title-compact\">{{ event.title }}</text>\n              <view class=\"location-group\">\n                <view class=\"separator-line\"></view>\n                <view class=\"event-location-compact\">\n                  <image class=\"location-icon\" :src=\"goldenLocationIconUrl\" mode=\"aspectFit\" />\n                  <text class=\"location-text\">{{ formatEventLocation(event) }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <view v-if=\"hasMore\" class=\"no-more-divider\" @click=\"$emit('view-more')\">\n      <text class=\"no-more-text\">查看更多</text>\n    </view>\n  </view>\n  <view class=\"timeline-bottom-spacer\"></view>\n</template>\n  \n  <script setup>\n  import { ref, onMounted } from 'vue'\n  import { getFullImageUrl } from '@/utils/image.js'\n  \n  defineProps({\n    groups: { type: Array, required: true },\n    hasMore: { type: Boolean, default: false },\n    notchLeft: { type: String, default: '60rpx' }\n  })\n  \n  // 静态资源 URL（不再使用本地兜底）\n  const goldenLocationIconUrl = ref('')\n  \n  // 组件挂载时读取静态资源配置\n  onMounted(() => {\n    const assets = uni.getStorageSync('staticAssets')\n    \n    if (!assets) return\n    goldenLocationIconUrl.value = assets['golden-location'] || assets['golden_location'] || ''\n  })\n  \n  const formatEventLocation = (event) => {\n    if (event.city && event.city.trim()) {\n      return event.city.trim().replace(/市$/, '')\n    }\n    return '待定'\n  }\n  </script>\n  \n  <style lang=\"scss\" scoped>\n.calendar-timeline-wrapper {\n  width: 100%;\n}\n  .vertical-timeline {\n    width: 702rpx;\n    background: #F0F2F3;\n    border-radius: 32rpx;\n    margin: 0 24rpx;\n    margin-top: 37rpx; \n    padding: 16rpx 0 20rpx 0;\n    box-sizing: border-box;\n    position: relative;\n    min-height: 200rpx; \n    overflow: visible;\n  }\n\n  /* 左上角凸起的角 */\n  .corner-notch {\n    position: absolute;\n    top: -16rpx; \n    left: 60rpx; \n    width: 0;\n    height: 0;\n    border-left: 14rpx solid transparent;\n    border-right: 14rpx solid transparent;\n    border-bottom: 16rpx solid #F0F2F3; \n    z-index: 103; \n  }\n  \n    .date-section {\n    position: relative;\n    padding-left: 72rpx;\n\n    &:first-child {\n      margin-top: 0;\n    }\n\n    &:not(:last-child) {\n      margin-bottom: 24rpx;\n    }\n  }\n  \n  .date-header {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 32rpx;\n  \n    .timeline-dot {\n      position: absolute;\n      left: 24rpx;\n      top: 13rpx;\n      width: 18rpx;\n      height: 18rpx;\n      background: #FFFFFF;\n      border: 2rpx solid #023F98;\n      border-radius: 50%;\n      z-index: 2;\n    }\n  \n    .time-text {\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n      font-size: 32rpx;\n      color: #023F98;\n      line-height: 44rpx;\n      font-weight: normal;\n    }\n  \n    .weekday-text {\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n      font-size: 22rpx;\n      color: #66666E;\n      line-height: 44rpx;\n      font-weight: normal;\n      margin-left: 15rpx;\n    }\n  }\n  \n  .line-connector {\n    position: absolute;\n    left: 32rpx;\n    top: 44rpx;\n    bottom: -197rpx;\n    width: 2rpx;\n  \n    .timeline-line {\n      height: 100%;\n      width: 100%;\n      background: #023F98;\n    }\n  }\n  \n  .date-section:last-child .line-connector .timeline-line {\n    display: none;\n  }\n  \n  .events-container {\n    .compact-event-card {\n      width: 590rpx;\n      height: 100rpx;\n      background: #FFFFFF;\n      border: 2rpx solid #023F98;\n      border-radius: 16rpx;\n      padding: 0 24rpx;\n      margin-bottom: 20rpx;\n      display: flex;\n      align-items: center;\n      gap: 12rpx;\n      transition: transform 0.2s ease;\n      box-sizing: border-box;\n  \n      &:last-child { margin-bottom: 0; }\n      &:active { transform: scale(0.98); }\n  \n      .event-avatar {\n        width: 52rpx;\n        height: 52rpx;\n        border-radius: 50%;\n        flex-shrink: 0;\n        background-color: #f5f5f5;\n      }\n  \n      .event-content {\n        flex: 1;\n        min-width: 0;\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n  \n        .event-title-compact {\n          font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n          font-size: 28rpx;\n          color: #23232A;\n          font-weight: normal;\n          flex: 1;\n          min-width: 0;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          line-height: 1.2;\n          margin-right: 24rpx;\n        }\n  \n        .location-group {\n          display: flex;\n          align-items: center;\n          flex-shrink: 0;\n        }\n  \n        .separator-line {\n          width: 2rpx;\n          height: 40rpx;\n          background: #FA841C;\n          opacity: 0.3;\n          flex-shrink: 0;\n          margin-right: 24rpx;\n        }\n  \n        .event-location-compact {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          flex-shrink: 0;\n          gap: 4rpx;\n  \n          .location-icon {\n            width: 32rpx;\n            height: 32rpx;\n            flex-shrink: 0;\n          }\n  \n          .location-text {\n            font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n            font-size: 22rpx;\n            color: #452D03;\n            font-weight: normal;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            line-height: 1.2;\n          }\n        }\n      }\n    }\n  }\n  \n  .no-more-divider {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 24rpx 0;\n    margin-top: 60rpx;\n  }\n\n  .no-more-text {\n    width: 120rpx;\n    height: 34rpx;\n    line-height: 34rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n    font-weight: normal;\n    font-size: 24rpx;\n    color: #9B9A9A;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n  }\n\n  /* 底部占位 */\n  .timeline-bottom-spacer {\n    height: 220rpx; \n  }\n  </style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventCalendarTimeline.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;AAwDE,UAAM,wBAAwBA,cAAG,IAAC,EAAE;AAGpCC,kBAAAA,UAAU,MAAM;AACd,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAEhD,UAAI,CAAC;AAAQ;AACb,4BAAsB,QAAQ,OAAO,iBAAiB,KAAK,OAAO,iBAAiB,KAAK;AAAA,IAC5F,CAAG;AAED,UAAM,sBAAsB,CAAC,UAAU;AACrC,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAI,GAAI;AACnC,eAAO,MAAM,KAAK,KAAM,EAAC,QAAQ,MAAM,EAAE;AAAA,MAC1C;AACD,aAAO;AAAA,IACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEH,GAAG,gBAAgB,SAAS;"}