{"version": 3, "file": "HeaderComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9IZWFkZXJDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"header-container\">\r\n    <view class=\"header-content\">\r\n      <view class=\"search-bar-wrapper\" @click=\"handleSearchClick\">\r\n        <text class=\"search-placeholder\">搜索资讯、活动</text>\r\n        <uni-icons type=\"search\" size=\"18\" color=\"#606266\"></uni-icons>\r\n      </view>\r\n      <view class=\"capsule-placeholder\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n// 点击搜索栏时，跳转到搜索页面\r\nconst handleSearchClick = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_other/search' // 请确认您的搜索页面路径\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* header-container 和 header-content 样式保持不变 */\r\n.header-container {\r\n  width: 100%;\r\n  height: 176rpx;\r\n  background-color: #023f98;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 999;\r\n  box-sizing: border-box;\r\n  padding-top: var(--status-bar-height);\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-content {\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 24rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-bar-wrapper {\r\n  /* 3. 搜索栏容器样式 */\r\n  width: 522rpx;\r\n  height: 60rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n\r\n  /* 之前的 margin-top: 30rpx; 可能会导致垂直对不齐，建议移除或调整 */\r\n  /* margin-top: 30rpx; */\r\n\r\n  /* --- 核心修改 --- */\r\n  /* 将内部元素（文字和图标）在主轴上两端对齐 */\r\n  justify-content: space-between;\r\n  margin-top: 35rpx;\r\n}\r\n\r\n.search-placeholder {\r\n  /* 4. 搜索栏占位文字样式 */\r\n  font-size: 28rpx;\r\n  color: #9b9a9a;\r\n\r\n  /* --- 移除修改 --- */\r\n  /* 图标已不在左侧，不再需要这个外边距 */\r\n  /* margin-left: 10rpx; */\r\n}\r\n\r\n.capsule-placeholder {\r\n  /* 5. 胶囊按钮占位符，无需样式 */\r\n}\r\n\r\n/* 增加一个点击效果 */\r\n.search-bar-wrapper:active {\r\n  opacity: 0.8;\r\n}\r\n</style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;AAcA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA;AAAA,MACT,CAAG;AAAA,IACH;;;;;;;;;;;;;;ACjBA,GAAG,gBAAgB,SAAS;"}