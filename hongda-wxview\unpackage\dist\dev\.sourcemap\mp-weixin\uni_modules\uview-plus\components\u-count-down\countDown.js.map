{"version": 3, "file": "countDown.js", "sources": ["uni_modules/uview-plus/components/u-count-down/countDown.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:11:29\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/countDown.js\r\n */\r\nexport default {\r\n    // u-count-down 计时器组件\r\n    countDown: {\r\n        time: 0,\r\n        format: 'HH:mm:ss',\r\n        autoStart: true,\r\n        millisecond: false\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EAChB;AACL;;"}