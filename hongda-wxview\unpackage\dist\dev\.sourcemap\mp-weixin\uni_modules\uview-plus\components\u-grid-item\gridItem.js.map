{"version": 3, "file": "gridItem.js", "sources": ["uni_modules/uview-plus/components/u-grid-item/gridItem.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:06:13\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gridItem.js\r\n */\r\nexport default {\r\n    // grid-item组件\r\n    gridItem: {\r\n        name: null,\r\n        bgColor: 'transparent'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACZ;AACL;;"}