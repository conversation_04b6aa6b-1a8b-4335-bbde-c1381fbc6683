{"version": 3, "file": "button.js", "sources": ["uni_modules/uview-plus/components/u-button/button.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:51:27\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/button.js\r\n */\r\nexport default {\r\n    // button组件\r\n    button: {\r\n        hairline: false,\r\n        type: 'info',\r\n        size: 'normal',\r\n        shape: 'square',\r\n        plain: false,\r\n        disabled: false,\r\n        loading: false,\r\n        loadingText: '',\r\n        loadingMode: 'spinner',\r\n        loadingSize: 15,\r\n        openType: '',\r\n        formType: '',\r\n        appParameter: '',\r\n        hoverStopPropagation: true,\r\n        lang: 'en',\r\n        sessionFrom: '',\r\n        sendMessageTitle: '',\r\n        sendMessagePath: '',\r\n        sendMessageImg: '',\r\n        showMessageCard: false,\r\n        dataName: '',\r\n        throttleTime: 0,\r\n        hoverStartTime: 0,\r\n        hoverStayTime: 200,\r\n        text: '',\r\n        icon: '',\r\n        iconColor: '',\r\n        color: '',\r\n        stop: true,\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,EACT;AACL;;"}