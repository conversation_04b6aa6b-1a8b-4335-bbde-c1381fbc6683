{"version": 3, "file": "input.js", "sources": ["uni_modules/uview-plus/components/u-input/input.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:13:55\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/input.js\r\n */\r\nexport default {\r\n\t// index 组件\r\n\tinput: {\r\n\t\tvalue: '',\r\n\t\ttype: 'text',\r\n\t\tfixed: false,\r\n\t\tdisabled: false,\r\n\t\tdisabledColor: '#f5f7fa',\r\n\t\tclearable: false,\r\n\t\tpassword: false,\r\n\t\tmaxlength: 140,\r\n\t\tplaceholder: null,\r\n\t\tplaceholderClass: 'input-placeholder',\r\n\t\tplaceholderStyle: 'color: #c0c4cc',\r\n\t\tshowWordLimit: false,\r\n\t\tconfirmType: 'done',\r\n\t\tconfirmHold: false,\r\n\t\tholdKeyboard: false,\r\n\t\tfocus: false,\r\n\t\tautoBlur: false,\r\n\t\tdisableDefaultPadding: false,\r\n\t\tcursor: -1,\r\n\t\tcursorSpacing: 30,\r\n\t\tselectionStart: -1,\r\n\t\tselectionEnd: -1,\r\n\t\tadjustPosition: true,\r\n\t\tinputAlign: 'left',\r\n\t\tfontSize: '15px',\r\n\t\tcolor: '#303133',\r\n\t\tprefixIcon: '',\r\n\t\tprefixIconStyle: '',\r\n\t\tsuffixIcon: '',\r\n\t\tsuffixIconStyle: '',\r\n\t\tborder: 'surround',\r\n\t\treadonly: false,\r\n\t\tshape: 'square',\r\n\t\tformatter: null\r\n\t}\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEd,OAAO;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AACF;;"}