"use strict";
const common_vendor = require("../../common/vendor.js");
const pages_sub_pages_other_api_data_policy = require("./api/data/policy.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  const _easycom_mp_html2 = common_vendor.resolveComponent("mp-html");
  (_easycom_up_navbar2 + _easycom_u_loading_icon2 + _easycom_mp_html2)();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_mp_html = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
if (!Math) {
  (_easycom_up_navbar + _easycom_u_loading_icon + _easycom_mp_html)();
}
const htmlStyle = "font-size: 28rpx; line-height: 1.8; color: #111111; padding: 24rpx; background-color: #FFFFFF; word-break: break-word;";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "PolicyPage"
}, {
  __name: "policy",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const error = common_vendor.ref(false);
    const errorMessage = common_vendor.ref("");
    const policyData = common_vendor.ref(null);
    const policyType = common_vendor.ref("");
    const pageTitle = common_vendor.ref("协议详情");
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/policy.vue:77", "协议页面加载，参数:", options);
      if (options.type) {
        policyType.value = options.type;
        if (options.type === "user_agreement") {
          pageTitle.value = "用户协议";
        } else if (options.type === "privacy_policy") {
          pageTitle.value = "隐私政策";
        }
        loadPolicy();
      } else {
        error.value = true;
        errorMessage.value = "缺少协议类型参数";
        loading.value = false;
      }
    });
    const loadPolicy = async () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/policy.vue:101", "开始加载协议内容，类型:", policyType.value);
      loading.value = true;
      error.value = false;
      errorMessage.value = "";
      try {
        const response = await pages_sub_pages_other_api_data_policy.getLatestPolicyApi(policyType.value);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/policy.vue:109", "协议内容加载响应:", response);
        if (response && response.code === 200 && response.data) {
          policyData.value = response.data;
          if (response.data.title) {
            pageTitle.value = response.data.title;
          }
          common_vendor.index.__f__("log", "at pages_sub/pages_other/policy.vue:119", "协议内容加载成功");
        } else {
          throw new Error(response.msg || "获取协议内容失败");
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/policy.vue:124", "协议内容加载失败:", err);
        error.value = true;
        errorMessage.value = err.message || "网络请求失败，请检查网络连接";
        common_vendor.index.showToast({
          title: errorMessage.value,
          icon: "none",
          duration: 3e3
        });
      } finally {
        loading.value = false;
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: pageTitle.value,
          autoBack: true,
          safeAreaInsetTop: true,
          fixed: true,
          placeholder: true,
          bgColor: "transparent",
          zIndex: 99,
          leftIconColor: "#333333",
          titleStyle: {
            fontFamily: "Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",
            fontWeight: "normal",
            fontSize: "32rpx",
            color: "#000000",
            lineHeight: "44rpx"
          }
        }),
        b: loading.value
      }, loading.value ? {
        c: common_vendor.p({
          mode: "spinner",
          color: "#023F98",
          size: 60
        })
      } : error.value ? {
        e: common_vendor.t(errorMessage.value),
        f: common_vendor.o(loadPolicy)
      } : policyData.value ? {
        h: common_vendor.p({
          content: policyData.value.contentHtml,
          ["lazy-load"]: true,
          selectable: true,
          ["show-img-menu"]: false,
          ["container-style"]: htmlStyle
        })
      } : {}, {
        d: error.value,
        g: policyData.value
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a0fc6e38"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/policy.js.map
