"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_tools = require("../../utils/tools.js");
const utils_date = require("../../utils/date.js");
const utils_image = require("../../utils/image.js");
const _sfc_main = {
  __name: "EventCard",
  props: {
    event: { type: Object, required: true }
  },
  setup(__props) {
    const listTimeIconUrl = common_vendor.ref("");
    const listLocationIconUrl = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      listTimeIconUrl.value = (assets == null ? void 0 : assets.list_time) || "";
      listLocationIconUrl.value = (assets == null ? void 0 : assets.list_location) || "";
    });
    const formatEventLocation = (event) => {
      if (event.city && event.city.trim()) {
        return event.city.trim().replace(/市$/, "");
      }
      return "待定";
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(utils_image.getFullImageUrl)(__props.event.coverImageUrl),
        b: Number(__props.event.status) === 1 || Number(__props.event.status) === 2
      }, Number(__props.event.status) === 1 || Number(__props.event.status) === 2 ? {
        c: common_vendor.t(common_vendor.unref(utils_tools.formatEventStatus)(__props.event.status)),
        d: common_vendor.n(common_vendor.unref(utils_tools.getStatusClass)(__props.event.status))
      } : {}, {
        e: common_vendor.t(__props.event.title),
        f: listTimeIconUrl.value,
        g: common_vendor.t(common_vendor.unref(utils_date.formatEventDate)(__props.event.startTime)),
        h: listLocationIconUrl.value,
        i: common_vendor.t(formatEventLocation(__props.event)),
        j: common_vendor.t(common_vendor.unref(utils_tools.calculateRemainingSpots)(__props.event.maxParticipants, __props.event.registeredCount)),
        k: common_vendor.o(($event) => _ctx.$emit("click", __props.event))
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fa0ac472"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/event/EventCard.js.map
