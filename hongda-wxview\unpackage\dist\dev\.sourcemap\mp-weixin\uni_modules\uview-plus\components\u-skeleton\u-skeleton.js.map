{"version": 3, "file": "u-skeleton.js", "sources": ["uni_modules/uview-plus/components/u-skeleton/u-skeleton.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXNrZWxldG9uL3Utc2tlbGV0b24udnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"u-skeleton\">\r\n\t\t<view\r\n\t\t    class=\"u-skeleton__wrapper\"\r\n\t\t    ref=\"u-skeleton__wrapper\"\r\n\t\t    v-if=\"loading\"\r\n\t\t\tstyle=\"display: flex; flex-direction: row;\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-skeleton__wrapper__avatar\"\r\n\t\t\t    v-if=\"avatar\"\r\n\t\t\t    :class=\"[`u-skeleton__wrapper__avatar--${avatarShape}`, animate && 'animate']\"\r\n\t\t\t    :style=\"{\r\n\t\t\t\t\t\theight: addUnit(avatarSize),\r\n\t\t\t\t\t\twidth: addUnit(avatarSize)\r\n\t\t\t\t\t}\"\r\n\t\t\t></view>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-skeleton__wrapper__content\"\r\n\t\t\t    ref=\"u-skeleton__wrapper__content\"\r\n\t\t\t\tstyle=\"flex: 1;\"\r\n\t\t\t>\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-skeleton__wrapper__content__title\"\r\n\t\t\t\t    v-if=\"title\"\r\n\t\t\t\t    :style=\"{\r\n\t\t\t\t\t\t\twidth: uTitleWidth,\r\n\t\t\t\t\t\t\theight: addUnit(titleHeight),\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t    :class=\"[animate && 'animate']\"\r\n\t\t\t\t></view>\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-skeleton__wrapper__content__rows\"\r\n\t\t\t\t    :class=\"[animate && 'animate']\"\r\n\t\t\t\t    v-for=\"(item, index) in rowsArray\"\r\n\t\t\t\t    :key=\"index\"\r\n\t\t\t\t    :style=\"{\r\n\t\t\t\t\t\t\t width: item.width,\r\n\t\t\t\t\t\t\t height: item.height,\r\n\t\t\t\t\t\t\t marginTop: item.marginTop\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t>\r\n\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<slot v-else />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, sleep, error } from '../../libs/function/index';\r\n\timport test from '../../libs/function/test';\r\n\t// #ifdef APP-NVUE\r\n\t// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\t// #endif\r\n\t/**\r\n\t * Skeleton 骨架屏\r\n\t * @description 骨架屏一般用于页面在请求远程数据尚未完成时，页面用灰色块预显示本来的页面结构，给用户更好的体验。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/skeleton.html\r\n\t * @property {Boolean}\t\t\t\t\tloading\t\t是否显示骨架占位图，设置为false将会展示子组件内容 (默认 true )\r\n\t * @property {Boolean}\t\t\t\t\tanimate\t\t是否开启动画效果 (默认 true )\r\n\t * @property {String | Number}\t\t\trows\t\t段落占位图行数 (默认 0 )\r\n\t * @property {String | Number | Array}\trowsWidth\t段落占位图的宽度，可以为百分比，数值，带单位字符串等，可通过数组传入指定每个段落行的宽度 (默认 '100%' )\r\n\t * @property {String | Number | Array}\trowsHeight\t段落的高度 (默认 18 )\r\n\t * @property {Boolean}\t\t\t\t\ttitle\t\t是否展示标题占位图 (默认 true )\r\n\t * @property {String | Number}\t\t\ttitleWidth\t标题的宽度 (默认 '50%' )\r\n\t * @property {String | Number}\t\t\ttitleHeight\t标题的高度 (默认 18 )\r\n\t * @property {Boolean}\t\t\t\t\tavatar\t\t是否展示头像占位图 (默认 false )\r\n\t * @property {String | Number}\t\t\tavatarSize\t头像占位图大小 (默认 32 )\r\n\t * @property {String}\t\t\t\t\tavatarShape\t头像占位图的形状，circle-圆形，square-方形 (默认 'circle' )\r\n\t * @example <u-search placeholder=\"日照香炉生紫烟\" v-model=\"keyword\"></u-search>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-skeleton',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\twidth: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tloading() {\r\n\t\t\t\tthis.getComponentWidth()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\trowsArray() {\r\n\t\t\t\tif (/%$/.test(this.rowsHeight)) {\r\n\t\t\t\t\terror('rowsHeight参数不支持百分比单位')\r\n\t\t\t\t}\r\n\t\t\t\tconst rows = []\r\n\t\t\t\tfor (let i = 0; i < this.rows; i++) {\r\n\t\t\t\t\tlet item = {},\r\n\t\t\t\t\t\t// 需要预防超出数组边界的情况\r\n\t\t\t\t\t\trowWidth = test.array(this.rowsWidth) ? (this.rowsWidth[i] || (i === this.rows - 1 ? '70%' : '100%')) : i ===\r\n\t\t\t\t\t\tthis.rows - 1 ? '70%' : this.rowsWidth,\r\n\t\t\t\t\t\trowHeight = test.array(this.rowsHeight) ? (this.rowsHeight[i] || '18px') : this.rowsHeight\r\n\t\t\t\t\t// 如果有title占位图，第一个段落占位图的外边距需要大一些，如果没有title占位图，第一个段落占位图则无需外边距\r\n\t\t\t\t\t// 之所以需要这么做，是因为weex的无能，以提升性能为借口不支持css的一些伪类\r\n\t\t\t\t\titem.marginTop = !this.title && i === 0 ? 0 : this.title && i === 0 ? '20px' : '12px'\r\n\t\t\t\t\t// 如果设置的为百分比的宽度，转换为px值，因为nvue不支持百分比单位\r\n\t\t\t\t\tif (/%$/.test(rowWidth)) {\r\n\t\t\t\t\t\t// 通过parseInt提取出百分比单位中的数值部分，除以100得到百分比的小数值\r\n\t\t\t\t\t\titem.width = addUnit(this.width * parseInt(rowWidth) / 100)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\titem.width = addUnit(rowWidth)\r\n\t\t\t\t\t}\r\n\t\t\t\t\titem.height = addUnit(rowHeight)\r\n\t\t\t\t\trows.push(item)\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(rows);\r\n\t\t\t\treturn rows\r\n\t\t\t},\r\n\t\t\tuTitleWidth() {\r\n\t\t\t\tlet tWidth = 0\r\n\t\t\t\tif (/%$/.test(this.titleWidth)) {\r\n\t\t\t\t\t// 通过parseInt提取出百分比单位中的数值部分，除以100得到百分比的小数值\r\n\t\t\t\t\ttWidth = addUnit(this.width * parseInt(this.titleWidth) / 100)\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttWidth = addUnit(this.titleWidth)\r\n\t\t\t\t}\r\n\t\t\t\treturn addUnit(tWidth)\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddUnit,\r\n\t\t\tinit() {\r\n\t\t\t\tthis.getComponentWidth()\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.loading && this.animate && this.setNvueAnimation()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync setNvueAnimation() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 为了让opacity:1的状态保持一定时间，这里做一个延时\r\n\t\t\t\tawait sleep(500)\r\n\t\t\t\tconst skeleton = this.$refs['u-skeleton__wrapper'];\r\n\t\t\t\tskeleton && this.loading && this.animate && animation.transition(skeleton, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\topacity: 0.5\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: 600,\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\t// 这里无需判断是否loading和开启动画状态，因为最终的状态必须达到opacity: 1，否则可能\r\n\t\t\t\t\t// 会停留在opacity: 0.5的状态中\r\n\t\t\t\t\tanimation.transition(skeleton, {\r\n\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\topacity: 1\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tduration: 600,\r\n\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t// 只有在loading中时，才执行动画\r\n\t\t\t\t\t\tthis.loading && this.animate && this.setNvueAnimation()\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 获取组件的宽度\r\n\t\t\tasync getComponentWidth() {\r\n\t\t\t\t// 延时一定时间，以获取dom尺寸\r\n\t\t\t\tawait sleep(20)\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.$uGetRect('.u-skeleton__wrapper__content').then(size => {\r\n\t\t\t\t\tthis.width = size.width\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst ref = this.$refs['u-skeleton__wrapper__content']\r\n\t\t\t\tref && dom.getComponentRect(ref, (res) => {\r\n\t\t\t\t\tthis.width = res.size.width\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t@mixin background {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tbackground-color: #F1F2F4;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbackground: linear-gradient(90deg, #F1F2F4 25%, #e6e6e6 37%, #F1F2F4 50%);\r\n\t\tbackground-size: 400% 100%;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.u-skeleton {\r\n\t\tflex: 1;\r\n\t\t\r\n\t\t&__wrapper {\r\n\t\t\t@include flex(row);\r\n\t\t\t\r\n\t\t\t&__avatar {\r\n\t\t\t\t@include background;\r\n\t\t\t\tmargin-right: 15px;\r\n\t\t\t\r\n\t\t\t\t&--circle {\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t&--square {\r\n\t\t\t\t\tborder-radius: 4px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\r\n\t\t\t\t&__rows,\r\n\t\t\t\t&__title {\r\n\t\t\t\t\t@include background;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.animate {\r\n\t\tanimation: skeleton 1.8s ease infinite\r\n\t}\r\n\r\n\t@keyframes skeleton {\r\n\t\t0% {\r\n\t\t\tbackground-position: 100% 50%\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tbackground-position: 0 50%\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-skeleton/u-skeleton.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "error", "test", "addUnit", "sleep"], "mappings": ";;;;;;;AA8EC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,sDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,UAAU;AACT,WAAK,kBAAkB;AAAA,IACxB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,UAAI,KAAK,KAAK,KAAK,UAAU,GAAG;AAC/BC,kDAAAA,MAAM,sBAAsB;AAAA,MAC7B;AACA,YAAM,OAAO,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AACnC,YAAI,OAAO,CAAE,GAEZ,WAAWC,yCAAI,KAAC,MAAM,KAAK,SAAS,IAAK,KAAK,UAAU,CAAC,MAAM,MAAM,KAAK,OAAO,IAAI,QAAQ,UAAW,MACxG,KAAK,OAAO,IAAI,QAAQ,KAAK,WAC7B,YAAYA,yCAAI,KAAC,MAAM,KAAK,UAAU,IAAK,KAAK,WAAW,CAAC,KAAK,SAAU,KAAK;AAGjF,aAAK,YAAY,CAAC,KAAK,SAAS,MAAM,IAAI,IAAI,KAAK,SAAS,MAAM,IAAI,SAAS;AAE/E,YAAI,KAAK,KAAK,QAAQ,GAAG;AAExB,eAAK,QAAQC,0CAAAA,QAAQ,KAAK,QAAQ,SAAS,QAAQ,IAAI,GAAG;AAAA,eACpD;AACN,eAAK,QAAQA,0CAAO,QAAC,QAAQ;AAAA,QAC9B;AACA,aAAK,SAASA,0CAAO,QAAC,SAAS;AAC/B,aAAK,KAAK,IAAI;AAAA,MACf;AAEA,aAAO;AAAA,IACP;AAAA,IACD,cAAc;AACb,UAAI,SAAS;AACb,UAAI,KAAK,KAAK,KAAK,UAAU,GAAG;AAE/B,iBAASA,0CAAAA,QAAQ,KAAK,QAAQ,SAAS,KAAK,UAAU,IAAI,GAAG;AAAA,aACvD;AACN,iBAASA,0CAAAA,QAAQ,KAAK,UAAU;AAAA,MACjC;AACA,aAAOA,0CAAAA,QAAQ,MAAM;AAAA,IACrB;AAAA,EAED;AAAA,EACD,UAAU;AACT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,SAAS;AAAA,IACR,SAAAA,0CAAO;AAAA,IACP,OAAO;AACN,WAAK,kBAAkB;AAAA,IAIvB;AAAA,IACD,MAAM,mBAAmB;AAAA,IAwBxB;AAAA;AAAA,IAED,MAAM,oBAAoB;AAEzB,YAAMC,0CAAAA,MAAM,EAAE;AAEd,WAAK,UAAU,+BAA+B,EAAE,KAAK,UAAQ;AAC5D,aAAK,QAAQ,KAAK;AAAA,OAClB;AAAA,IASF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxLD,GAAG,gBAAgB,SAAS;"}