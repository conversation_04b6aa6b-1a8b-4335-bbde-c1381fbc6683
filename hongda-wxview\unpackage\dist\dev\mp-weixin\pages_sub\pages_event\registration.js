"use strict";
const common_vendor = require("../../common/vendor.js");
const pages_sub_pages_event_api_data_registration = require("./api/data/registration.js");
const api_data_event = require("../../api/data/event.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  const _easycom_up_loading_page2 = common_vendor.resolveComponent("up-loading-page");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_up_input2 = common_vendor.resolveComponent("up-input");
  const _easycom_up_form_item2 = common_vendor.resolveComponent("up-form-item");
  const _easycom_up_textarea2 = common_vendor.resolveComponent("up-textarea");
  const _easycom_up_form2 = common_vendor.resolveComponent("up-form");
  const _easycom_up_picker2 = common_vendor.resolveComponent("up-picker");
  (_easycom_up_navbar2 + _easycom_up_loading_page2 + _easycom_up_button2 + _easycom_up_empty2 + _easycom_up_input2 + _easycom_up_form_item2 + _easycom_up_textarea2 + _easycom_up_form2 + _easycom_up_picker2)();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_up_loading_page = () => "../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_up_input = () => "../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_up_form_item = () => "../../uni_modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_up_textarea = () => "../../uni_modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_up_form = () => "../../uni_modules/uview-plus/components/u-form/u-form.js";
const _easycom_up_picker = () => "../../uni_modules/uview-plus/components/u-picker/u-picker.js";
if (!Math) {
  (_easycom_up_navbar + _easycom_up_loading_page + _easycom_up_button + _easycom_up_empty + _easycom_up_input + _easycom_up_form_item + _easycom_up_textarea + _easycom_up_form + _easycom_up_picker)();
}
const _sfc_main = {
  __name: "registration",
  setup(__props) {
    const eventId = common_vendor.ref(null);
    const eventInfo = common_vendor.ref(null);
    const formConfig = common_vendor.ref([]);
    const formData = common_vendor.reactive({});
    const formRules = common_vendor.reactive({});
    const isLoading = common_vendor.ref(true);
    const isSubmitting = common_vendor.ref(false);
    const isLoggedIn = common_vendor.ref(false);
    const pickerShow = common_vendor.ref(false);
    const pickerColumns = common_vendor.ref([]);
    const currentPickerField = common_vendor.ref("");
    const showConfirmModal = common_vendor.ref(false);
    const formRef = common_vendor.ref();
    const pickerRef = common_vendor.ref();
    const checkLoginStatus = () => {
      const token = common_vendor.index.getStorageSync("token");
      return !!token;
    };
    common_vendor.onLoad(async (options) => {
      eventId.value = options.id;
      if (!eventId.value) {
        common_vendor.index.$u.toast("活动信息错误");
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
    });
    common_vendor.onShow(async () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:298", "=== 报名页面 onShow 触发 ===");
      const currentLoginStatus = checkLoginStatus();
      isLoggedIn.value = currentLoginStatus;
      common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:304", "当前登录状态:", currentLoginStatus);
      common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:305", "当前token:", common_vendor.index.getStorageSync("token"));
      if (!currentLoginStatus) {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:309", "用户未登录，返回上一页让detail页面处理登录跳转");
        isLoading.value = false;
        common_vendor.index.showToast({
          title: "请先登录后报名",
          icon: "none",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:320", "返回上一页（detail页面）");
          common_vendor.index.navigateBack({
            fail: () => {
              common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:324", "返回失败，跳转到首页");
              common_vendor.index.switchTab({ url: "/pages/index/index" });
            }
          });
        }, 800);
      } else {
        common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:332", "用户已登录，开始加载活动信息和表单数据");
        if (eventId.value) {
          await loadEventInfo();
        }
        if (eventId.value && (!formConfig.value || formConfig.value.length === 0)) {
          await loadFormDefinition();
        } else {
          isLoading.value = false;
        }
      }
    });
    const goToLogin = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:351", "手动跳转到登录页");
      common_vendor.index.navigateTo({
        url: "/pages/login/index"
      });
    };
    const handleNavBack = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:359", "=== 开始智能导航回退处理 ===");
      common_vendor.index.navigateBack({
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:364", "正常回退成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("warn", "at pages_sub/pages_event/registration.vue:367", "⚠️ 正常回退失败:", err);
          common_vendor.index.navigateTo({
            url: "/pages/event/index",
            success: () => {
              common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:373", "跳转到活动列表页面成功");
            },
            fail: (err2) => {
              common_vendor.index.__f__("warn", "at pages_sub/pages_event/registration.vue:376", "跳转到活动列表页面失败:", err2);
              common_vendor.index.switchTab({
                url: "/pages/index/index",
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:382", "跳转到首页成功");
                },
                fail: (err3) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:385", "所有导航方案都失败了:", err3);
                  common_vendor.index.showToast({
                    title: "导航失败，请重新打开小程序",
                    icon: "none"
                  });
                }
              });
            }
          });
        }
      });
    };
    const loadEventInfo = async () => {
      try {
        const response = await api_data_event.getEventDetailApi(eventId.value);
        if (response.code === 200 && response.data) {
          eventInfo.value = response.data;
          common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:404", "成功加载活动信息:", eventInfo.value);
        } else {
          throw new Error(response.msg || "获取活动信息失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:409", "加载活动信息失败:", error);
      }
    };
    const loadFormDefinition = async () => {
      try {
        isLoading.value = true;
        const response = await pages_sub_pages_event_api_data_registration.getFormDefinitionApi(eventId.value);
        if (response.code === 200 && response.data) {
          let formDefinition;
          if (typeof response.data === "string") {
            try {
              formDefinition = JSON.parse(response.data);
              common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:429", "解析后的表单定义:", formDefinition);
            } catch (parseError) {
              common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:431", "解析表单定义JSON失败:", parseError);
              throw new Error("表单配置格式错误");
            }
          } else {
            formDefinition = response.data;
          }
          const fields = formDefinition.fields || [];
          if (Array.isArray(fields) && fields.length > 0) {
            formConfig.value = fields;
            common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:444", "成功加载表单配置，字段数量:", fields.length);
            initFormData();
            initFormRules();
          } else {
            common_vendor.index.__f__("warn", "at pages_sub/pages_event/registration.vue:448", "表单配置中没有字段或字段不是数组:", formDefinition);
            formConfig.value = [];
          }
        } else {
          throw new Error(response.msg || "获取表单配置失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:456", "加载表单定义失败:", error);
        common_vendor.index.$u.toast(error.message || "加载表单失败，请稍后重试");
        formConfig.value = [];
      } finally {
        isLoading.value = false;
      }
    };
    const initFormData = () => {
      formConfig.value.forEach((item) => {
        if (item.field) {
          formData[item.field] = item.defaultValue || "";
        }
      });
    };
    const initFormRules = () => {
      formConfig.value.forEach((item) => {
        if (item.field && item.required) {
          const rules = [{
            required: true,
            message: `请${item.type === "select" ? "选择" : "输入"}${item.label}`,
            trigger: ["blur", "change"]
          }];
          if (item.type === "email") {
            rules.push({
              pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: "请输入有效的邮箱地址",
              trigger: ["blur"]
            });
          } else if (item.type === "phone") {
            rules.push({
              pattern: /^1[3-9]\d{9}$/,
              message: "请输入有效的手机号",
              trigger: ["blur"]
            });
          }
          formRules[item.field] = rules;
        }
      });
    };
    const getSelectDisplayValue = (field, options) => {
      const value = formData[field];
      if (!value || !options)
        return "";
      const option = options.find((opt) => opt.value === value);
      return option ? option.label : "";
    };
    const openPicker = (item) => {
      if (!item.options || !Array.isArray(item.options)) {
        common_vendor.index.$u.toast("选项配置错误");
        return;
      }
      currentPickerField.value = item.field;
      pickerColumns.value = [item.options];
      pickerShow.value = true;
    };
    const onPickerConfirm = (e) => {
      const { value } = e;
      if (value && value[0] && currentPickerField.value) {
        formData[currentPickerField.value] = value[0].value;
      }
      pickerShow.value = false;
    };
    const handleSubmit = async () => {
      if (!formRef.value) {
        common_vendor.index.$u.toast("表单初始化失败");
        return;
      }
      try {
        await formRef.value.validate();
        showConfirmModal.value = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:548", "表单验证失败:", error);
        common_vendor.index.$u.toast("请检查表单填写是否正确");
      }
    };
    const closeConfirmModal = () => {
      showConfirmModal.value = false;
    };
    const confirmSubmitRegistration = async () => {
      closeConfirmModal();
      try {
        isSubmitting.value = true;
        const response = await pages_sub_pages_event_api_data_registration.submitRegistrationApi({
          eventId: eventId.value,
          formData
        });
        if (response.code === 200) {
          common_vendor.index.$u.toast("报名成功！");
          try {
            const registrationStatus = common_vendor.index.getStorageSync("registrationStatus") || {};
            registrationStatus[eventId.value] = {
              isRegistered: true,
              timestamp: Date.now(),
              formData,
              // 可选：保存表单数据供后续查看
              source: "user_registration"
              // 标记数据来源
            };
            common_vendor.index.setStorageSync("registrationStatus", registrationStatus);
            common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:584", "已在本地存储中标记报名状态（状态分离模式）:", registrationStatus);
          } catch (error) {
            common_vendor.index.__f__("warn", "at pages_sub/pages_event/registration.vue:586", "保存本地报名状态失败:", error);
          }
          setTimeout(() => {
            common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:593", "发送数据变化广播事件...");
            common_vendor.index.$emit("dataChanged");
            common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:597", "已发送 dataChanged 事件");
            common_vendor.index.navigateBack({
              success: () => {
                common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:602", "返回上一页成功");
                setTimeout(() => {
                  common_vendor.index.$emit("dataChanged");
                  common_vendor.index.__f__("log", "at pages_sub/pages_event/registration.vue:607", "页面返回后再次发送 dataChanged 事件");
                }, 100);
              },
              fail: (error) => {
                common_vendor.index.__f__("warn", "at pages_sub/pages_event/registration.vue:611", "页面返回失败:", error);
                common_vendor.index.switchTab({
                  url: "/pages/index/index"
                });
              }
            });
          }, 1500);
        } else {
          throw new Error(response.msg || "报名失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_event/registration.vue:625", "提交报名失败:", error);
        if (error.message) {
          common_vendor.index.$u.toast(error.message);
        } else {
          common_vendor.index.$u.toast("提交失败，请稍后重试");
        }
      } finally {
        isSubmitting.value = false;
      }
    };
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.o(handleNavBack),
        b: common_vendor.p({
          title: ((_a = eventInfo.value) == null ? void 0 : _a.title) || "活动报名",
          fixed: true,
          safeAreaInsetTop: true,
          bgColor: "transparent",
          leftIcon: "arrow-left",
          leftIconColor: "#333333",
          titleStyle: "color: #333333; font-weight: bold;"
        }),
        c: isLoading.value
      }, isLoading.value ? {
        d: common_vendor.p({
          loadingText: "正在加载报名表单...",
          loadingMode: "spinner"
        })
      } : !isLoggedIn.value ? {
        f: common_vendor.o(goToLogin),
        g: common_vendor.p({
          type: "primary",
          text: "立即登录",
          customStyle: "background-color: #f56c6c; border-color: #f56c6c; width: 200rpx; height: 70rpx; font-size: 28rpx;"
        }),
        h: common_vendor.p({
          mode: "permission",
          text: "请先登录后进行报名",
          textColor: "#909399",
          textSize: "28"
        })
      } : isLoggedIn.value && (!formConfig.value || formConfig.value.length === 0) ? {
        j: common_vendor.p({
          mode: "data",
          text: "该活动无需报名表单",
          textColor: "#909399",
          textSize: "28"
        })
      } : isLoggedIn.value && formConfig.value && formConfig.value.length > 0 ? common_vendor.e({
        l: eventInfo.value
      }, eventInfo.value ? {
        m: common_vendor.t(eventInfo.value.title)
      } : {}, {
        n: common_vendor.f(formConfig.value, (item, index, i0) => {
          var _a2, _b, _c, _d, _e, _f, _g, _h;
          return common_vendor.e({
            a: item.type === "input"
          }, item.type === "input" ? {
            b: "5cc4b4a8-7-" + i0 + "," + ("5cc4b4a8-6-" + i0),
            c: common_vendor.o(($event) => formData[item.field] = $event, index),
            d: common_vendor.p({
              placeholder: ((_a2 = item.props) == null ? void 0 : _a2.placeholder) || "请输入",
              clearable: true,
              maxlength: ((_b = item.props) == null ? void 0 : _b.maxlength) || 100,
              customStyle: "width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",
              modelValue: formData[item.field]
            }),
            e: "5cc4b4a8-6-" + i0 + ",5cc4b4a8-5",
            f: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : item.type === "textarea" ? {
            h: "5cc4b4a8-9-" + i0 + "," + ("5cc4b4a8-8-" + i0),
            i: common_vendor.o(($event) => formData[item.field] = $event, index),
            j: common_vendor.p({
              placeholder: ((_c = item.props) == null ? void 0 : _c.placeholder) || "请输入",
              maxlength: ((_d = item.props) == null ? void 0 : _d.maxlength) || 500,
              height: "120",
              count: true,
              customStyle: "width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",
              modelValue: formData[item.field]
            }),
            k: "5cc4b4a8-8-" + i0 + ",5cc4b4a8-5",
            l: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : item.type === "select" ? {
            n: common_vendor.o(($event) => openPicker(item), index),
            o: "5cc4b4a8-11-" + i0 + "," + ("5cc4b4a8-10-" + i0),
            p: common_vendor.p({
              value: getSelectDisplayValue(item.field, item.options),
              placeholder: ((_e = item.props) == null ? void 0 : _e.placeholder) || "请选择",
              readonly: true,
              suffixIcon: "arrow-down-fill",
              customStyle: "width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
            }),
            q: "5cc4b4a8-10-" + i0 + ",5cc4b4a8-5",
            r: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : item.type === "number" ? {
            t: "5cc4b4a8-13-" + i0 + "," + ("5cc4b4a8-12-" + i0),
            v: common_vendor.o(($event) => formData[item.field] = $event, index),
            w: common_vendor.p({
              placeholder: ((_f = item.props) == null ? void 0 : _f.placeholder) || "请输入数字",
              type: "number",
              clearable: true,
              customStyle: "width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",
              modelValue: formData[item.field]
            }),
            x: "5cc4b4a8-12-" + i0 + ",5cc4b4a8-5",
            y: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : item.type === "phone" ? {
            A: "5cc4b4a8-15-" + i0 + "," + ("5cc4b4a8-14-" + i0),
            B: common_vendor.o(($event) => formData[item.field] = $event, index),
            C: common_vendor.p({
              placeholder: ((_g = item.props) == null ? void 0 : _g.placeholder) || "请输入手机号",
              type: "number",
              clearable: true,
              maxlength: 11,
              customStyle: "width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",
              modelValue: formData[item.field]
            }),
            D: "5cc4b4a8-14-" + i0 + ",5cc4b4a8-5",
            E: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : item.type === "email" ? {
            G: "5cc4b4a8-17-" + i0 + "," + ("5cc4b4a8-16-" + i0),
            H: common_vendor.o(($event) => formData[item.field] = $event, index),
            I: common_vendor.p({
              placeholder: ((_h = item.props) == null ? void 0 : _h.placeholder) || "请输入邮箱地址",
              clearable: true,
              customStyle: "width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",
              modelValue: formData[item.field]
            }),
            J: "5cc4b4a8-16-" + i0 + ",5cc4b4a8-5",
            K: common_vendor.p({
              label: item.label,
              prop: item.field,
              required: item.required
            })
          } : {}, {
            g: item.type === "textarea",
            m: item.type === "select",
            s: item.type === "number",
            z: item.type === "phone",
            F: item.type === "email",
            L: index
          });
        }),
        o: common_vendor.sr(formRef, "5cc4b4a8-5", {
          "k": "formRef"
        }),
        p: common_vendor.p({
          model: formData,
          rules: formRules,
          labelPosition: "top",
          labelWidth: "auto",
          labelStyle: {
            fontFamily: "Alibaba PuHuiTi 3.0-55 Regular",
            fontWeight: "normal",
            fontSize: "28rpx",
            color: "#23232A",
            lineHeight: "normal"
          }
        })
      }) : {}, {
        e: !isLoggedIn.value,
        i: isLoggedIn.value && (!formConfig.value || formConfig.value.length === 0),
        k: isLoggedIn.value && formConfig.value && formConfig.value.length > 0,
        q: isLoggedIn.value && !isLoading.value && formConfig.value && formConfig.value.length > 0
      }, isLoggedIn.value && !isLoading.value && formConfig.value && formConfig.value.length > 0 ? {
        r: common_vendor.t(isSubmitting.value ? "提交中..." : "提交报名信息"),
        s: common_vendor.o(handleSubmit),
        t: common_vendor.p({
          type: "primary",
          shape: "circle",
          size: "large",
          loading: isSubmitting.value,
          disabled: isSubmitting.value,
          customStyle: "width: 702rpx; height: 76rpx; background: #023F98; border-radius: 8rpx; border-color: #023F98; font-size: 28rpx;"
        })
      } : {}, {
        v: common_vendor.sr(pickerRef, "5cc4b4a8-19", {
          "k": "pickerRef"
        }),
        w: common_vendor.o(onPickerConfirm),
        x: common_vendor.o(($event) => pickerShow.value = false),
        y: common_vendor.p({
          show: pickerShow.value,
          columns: pickerColumns.value,
          keyName: "label"
        }),
        z: showConfirmModal.value
      }, showConfirmModal.value ? {
        A: _ctx.ordersWarningIconUrl,
        B: common_vendor.o(closeConfirmModal),
        C: common_vendor.o(confirmSubmitRegistration),
        D: common_vendor.o(() => {
        }),
        E: common_vendor.o(closeConfirmModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5cc4b4a8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_event/registration.js.map
