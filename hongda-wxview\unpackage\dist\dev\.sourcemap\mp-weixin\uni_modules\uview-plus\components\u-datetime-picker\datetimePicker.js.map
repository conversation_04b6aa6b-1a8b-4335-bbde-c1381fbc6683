{"version": 3, "file": "datetimePicker.js", "sources": ["uni_modules/uview-plus/components/u-datetime-picker/datetimePicker.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:57:48\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/datetimePicker.js\r\n */\r\nexport default {\r\n    // datetimePicker 组件\r\n    datetimePicker: {\r\n        show: false,\r\n\t\tpopupMode: 'bottom',\r\n        showToolbar: true,\r\n        value: '',\r\n        title: '',\r\n        mode: 'datetime',\r\n        maxDate: new Date(new Date().getFullYear() + 10, 0, 1).getTime(),\r\n        minDate: new Date(new Date().getFullYear() - 10, 0, 1).getTime(),\r\n        minHour: 0,\r\n        maxHour: 23,\r\n        minMinute: 0,\r\n        maxMinute: 59,\r\n        filter: null,\r\n        formatter: null,\r\n        loading: false,\r\n        itemHeight: 44,\r\n        cancelText: '取消',\r\n        confirmText: '确认',\r\n        cancelColor: '#909193',\r\n        confirmColor: '#3c9cff',\r\n        visibleItemCount: 5,\r\n        closeOnClickOverlay: false,\r\n        defaultIndex: [],\r\n        inputBorder: 'surround',\r\n        disabled: false,\r\n        disabledColor: '',\r\n        placeholder: '请选择',\r\n        inputProps: {},\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,iBAAA;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS,IAAI,MAAK,oBAAI,QAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,QAAS;AAAA,IAChE,SAAS,IAAI,MAAK,oBAAI,QAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,QAAS;AAAA,IAChE,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,cAAc,CAAE;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,EACjB;AACL;;"}