{"version": 3, "file": "u-input.js", "sources": ["uni_modules/uview-plus/components/u-input/u-input.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWlucHV0L3UtaW5wdXQudnVl"], "sourcesContent": ["<template>\r\n    <view class=\"u-input\" :class=\"inputClass\" :style=\"[wrapperStyle]\">\r\n        <view class=\"u-input__content\">\r\n            <view\r\n                class=\"u-input__content__prefix-icon\"\r\n                v-if=\"prefixIcon || $slots.prefix\"\r\n            >\r\n                <slot name=\"prefix\">\r\n                    <u-icon\r\n                        :name=\"prefixIcon\"\r\n                        size=\"18\"\r\n                        :customStyle=\"prefixIconStyle\"\r\n                    ></u-icon>\r\n                </slot>\r\n            </view>\r\n            <view class=\"u-input__content__field-wrapper\" @tap=\"clickHandler\">\r\n\t\t\t\t<!-- 根据uni-app的input组件文档，H5和APP中只要声明了password参数(无论true还是false)，type均失效，此时\r\n\t\t\t\t\t为了防止type=number时，又存在password属性，type无效，此时需要设置password为undefined\r\n\t\t\t\t -->\r\n            \t<input\r\n                    ref=\"input-native\"\r\n            \t    class=\"u-input__content__field-wrapper__field\"\r\n            \t    :style=\"[inputStyle]\"\r\n            \t    :type=\"type\"\r\n            \t    :focus=\"focus\"\r\n            \t    :cursor=\"cursor\"\r\n            \t    :value=\"innerValue\"\r\n            \t    :auto-blur=\"autoBlur\"\r\n            \t    :disabled=\"disabled || readonly\"\r\n            \t    :maxlength=\"maxlength\"\r\n            \t    :placeholder=\"placeholder\"\r\n            \t    :placeholder-style=\"placeholderStyle\"\r\n            \t    :placeholder-class=\"placeholderClass\"\r\n            \t    :confirm-type=\"confirmType\"\r\n            \t    :confirm-hold=\"confirmHold\"\r\n            \t    :hold-keyboard=\"holdKeyboard\"\r\n            \t    :cursor-spacing=\"cursorSpacing\"\r\n            \t    :adjust-position=\"adjustPosition\"\r\n            \t    :selection-end=\"selectionEnd\"\r\n            \t    :selection-start=\"selectionStart\"\r\n            \t    :password=\"password || type === 'password' || false\"\r\n                    :ignoreCompositionEvent=\"ignoreCompositionEvent\"\r\n            \t    @input=\"onInput\"\r\n            \t    @blur=\"onBlur\"\r\n            \t    @focus=\"onFocus\"\r\n            \t    @confirm=\"onConfirm\"\r\n            \t    @keyboardheightchange=\"onkeyboardheightchange\"\r\n                    @nicknamereview=\"onnicknamereview\"\r\n            \t/>\r\n            </view>\r\n            <view\r\n                class=\"u-input__content__clear\"\r\n                v-if=\"isShowClear\"\r\n                @click=\"onClear\"\r\n            >\r\n                <u-icon\r\n                    name=\"close\"\r\n                    size=\"11\"\r\n                    color=\"#ffffff\"\r\n                    customStyle=\"line-height: 12px\"\r\n                ></u-icon>\r\n            </view>\r\n            <view\r\n                class=\"u-input__content__subfix-icon\"\r\n                v-if=\"suffixIcon || $slots.suffix\"\r\n            >\r\n                <slot name=\"suffix\">\r\n                    <u-icon\r\n                        :name=\"suffixIcon\"\r\n                        size=\"18\"\r\n                        :customStyle=\"suffixIconStyle\"\r\n                    ></u-icon>\r\n                </slot>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { props } from \"./props.js\";\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { debounce } from '../../libs/function/debounce';\r\nimport { addStyle, addUnit, deepMerge, formValidate, $parent, sleep, os } from '../../libs/function/index';\r\n/**\r\n * Input 输入框\r\n * @description  此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\r\n * @tutorial https://uview-plus.jiangruyi.com/components/input.html\r\n * @property {String | Number}\tvalue\t\t\t\t\t输入的值\r\n * @property {String}\t\t\ttype\t\t\t\t\t输入框类型，见上方说明 （ 默认 'text' ）\r\n * @property {Boolean}\t\t\tfixed\t\t\t\t\t如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序 （ 默认 false ）\r\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用输入框 （ 默认 false ）\r\n * @property {String}\t\t\tdisabledColor\t\t\t禁用状态时的背景色（ 默认 '#f5f7fa' ）\r\n * @property {Boolean}\t\t\tclearable\t\t\t\t是否显示清除控件 （ 默认 false ）\r\n * @property {Boolean}\t\t\tpassword\t\t\t\t是否密码类型 （ 默认 false ）\r\n * @property {Number}       \tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度 （ 默认 -1 ）\r\n * @property {String}\t\t\tplaceholder\t\t\t\t输入框为空时的占位符\r\n * @property {String}\t\t\tplaceholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\r\n * @property {String | Object}\tplaceholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\r\n * @property {Boolean}\t\t\tshowWordLimit\t\t\t是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效 （ 默认 false ）\r\n * @property {String}\t\t\tconfirmType\t\t\t\t设置右下角按钮的文字，兼容性详见uni-app文档 （ 默认 'done' ）\r\n * @property {Boolean}\t\t\tconfirmHold\t\t\t\t点击键盘右下角按钮时是否保持键盘不收起，H5无效 （ 默认 false ）\r\n * @property {Boolean}\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，微信小程序有效 （ 默认 false ）\r\n * @property {Boolean}\t\t\tfocus\t\t\t\t\t自动获取焦点，在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点 （ 默认 false ）\r\n * @property {Boolean}\t\t\tautoBlur\t\t\t\t键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效 （ 默认 false ）\r\n * @property {Boolean}\t\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效 （ 默认 false ）\r\n * @property {String ｜ Number}\tcursor\t\t\t\t\t指定focus时光标的位置（ 默认 140 ）\r\n * @property {String ｜ Number}\tcursorSpacing\t\t\t输入框聚焦时底部与键盘的距离 （ 默认 30 ）\r\n * @property {String ｜ Number}\tselectionStart\t\t\t光标起始位置，自动聚集时有效，需与selection-end搭配使用 （ 默认 -1 ）\r\n * @property {String ｜ Number}\tselectionEnd\t\t\t光标结束位置，自动聚集时有效，需与selection-start搭配使用 （ 默认 -1 ）\r\n * @property {Boolean}\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面 （ 默认 true ）\r\n * @property {String}\t\t\tinputAlign\t\t\t\t输入框内容对齐方式（ 默认 'left' ）\r\n * @property {String | Number}\tfontSize\t\t\t\t输入框字体的大小 （ 默认 '15px' ）\r\n * @property {String}\t\t\tcolor\t\t\t\t\t输入框字体颜色\t（ 默认 '#303133' ）\r\n * @property {Function}\t\t\tformatter\t\t\t    内容式化函数\r\n * @property {String}\t\t\tprefixIcon\t\t\t\t输入框前置图标\r\n * @property {String | Object}\tprefixIconStyle\t\t\t前置图标样式，对象或字符串\r\n * @property {String}\t\t\tsuffixIcon\t\t\t\t输入框后置图标\r\n * @property {String | Object}\tsuffixIconStyle\t\t\t后置图标样式，对象或字符串\r\n * @property {String}\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，bottom-底部边框，none-无边框 （ 默认 'surround' ）\r\n * @property {Boolean}\t\t\treadonly\t\t\t\t是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 （ 默认 false ）\r\n * @property {String}\t\t\tshape\t\t\t\t\t输入框形状，circle-圆形，square-方形 （ 默认 'square' ）\r\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\r\n * @property {Boolean}\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理。\r\n * @example <u-input v-model=\"value\" :password=\"true\" suffix-icon=\"lock-fill\" />\r\n */\r\nexport default {\r\n    name: \"u-input\",\r\n    mixins: [mpMixin, mixin, props],\r\n    data() {\r\n        return {\r\n            // 清除操作\r\n            clearInput: false,\r\n            // 输入框的值\r\n            innerValue: \"\",\r\n            // 是否处于获得焦点状态\r\n            focused: false,\r\n            // value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化\r\n            firstChange: true,\r\n            // value绑定值的变化是由内部还是外部引起的\r\n            changeFromInner: false,\r\n\t\t\t// 过滤处理方法\r\n\t\t\tinnerFormatter: value => value\r\n        };\r\n    },\r\n    created() {\r\n        // 格式化过滤方法\r\n        if (this.formatter) {\r\n            this.innerFormatter = this.formatter;\r\n        }\r\n    },\r\n    watch: {\r\n        modelValue: {\r\n            immediate: true,\r\n            handler(newVal, oldVal) {\r\n                // console.log(newVal, oldVal)\r\n                if (this.changeFromInner || this.innerValue === newVal) {\r\n                    this.changeFromInner = false; // 重要否则会出现双向绑定失效问题https://github.com/ijry/uview-plus/issues/419\r\n                    return;\r\n                }\r\n                this.innerValue = newVal;\r\n                // 在H5中，外部value变化后，修改input中的值，不会触发@input事件，此时手动调用值变化方法\r\n                if (\r\n                    this.firstChange === false &&\r\n\t\t\t\t\tthis.changeFromInner === false\r\n                ) {\r\n                    this.valueChange(this.innerValue, true);\r\n                } else {\r\n\t\t\t\t\t// 尝试调用up-form的验证方法\r\n                    if(!this.firstChange) formValidate(this, \"change\");\r\n\t\t\t\t}\r\n                this.firstChange = false;\r\n                // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\r\n                this.changeFromInner = false;\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        // 是否显示清除控件\r\n        isShowClear() {\r\n            const { clearable, readonly, focused, innerValue } = this;\r\n            return !!clearable && !readonly && !!focused && innerValue !== \"\";\r\n        },\r\n        // 组件的类名\r\n        inputClass() {\r\n            let classes = [],\r\n                { border, disabled, shape } = this;\r\n            border === \"surround\" &&\r\n                (classes = classes.concat([\"u-border\", \"u-input--radius\"]));\r\n            classes.push(`u-input--${shape}`);\r\n            border === \"bottom\" &&\r\n                (classes = classes.concat([\r\n                    \"u-border-bottom\",\r\n                    \"u-input--no-radius\",\r\n                ]));\r\n            return classes.join(\" \");\r\n        },\r\n        // 组件的样式\r\n        wrapperStyle() {\r\n            const style = {};\r\n            // 禁用状态下，被背景色加上对应的样式\r\n            if (this.disabled) {\r\n                style.backgroundColor = this.disabledColor;\r\n            }\r\n            // 无边框时，去除内边距\r\n            if (this.border === \"none\") {\r\n                style.padding = \"0\";\r\n            } else {\r\n                // 由于uni-app的iOS端限制，导致需要分开写才有效\r\n                style.paddingTop = \"6px\";\r\n                style.paddingBottom = \"6px\";\r\n                style.paddingLeft = \"9px\";\r\n                style.paddingRight = \"9px\";\r\n            }\r\n            return deepMerge(style, addStyle(this.customStyle));\r\n        },\r\n        // 输入框的样式\r\n        inputStyle() {\r\n            const style = {\r\n                color: this.color,\r\n                fontSize: addUnit(this.fontSize),\r\n\t\t\t\ttextAlign: this.inputAlign\r\n            };\r\n            return style;\r\n        },\r\n    },\r\n    // #ifdef VUE3\r\n    emits: ['update:modelValue', 'focus', 'blur', 'change', 'confirm', 'clear', 'keyboardheightchange', 'nicknamereview'],\r\n    // #endif\r\n    methods: {\r\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\r\n\t\tsetFormatter(e) {\r\n\t\t\tthis.innerFormatter = e\r\n\t\t},\r\n        // 当键盘输入时，触发input事件\r\n        onInput(e) {\r\n            let { value = \"\" } = e.detail || {};\r\n            // 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\r\n            // console.log('onInput', value, this.innerValue)\r\n            this.innerValue = value;\r\n            this.$nextTick(() => {\r\n                let formatValue = this.innerFormatter(value);\r\n            \tthis.innerValue = formatValue;\r\n                this.valueChange(formatValue);\r\n            })\r\n        },\r\n        // 输入框失去焦点时触发\r\n        onBlur(event) {\r\n            this.$emit(\"blur\", event.detail.value);\r\n            // H5端的blur会先于点击清除控件的点击click事件触发，导致focused\r\n            // 瞬间为false，从而隐藏了清除控件而无法被点击到\r\n            sleep(150).then(() => {\r\n                this.focused = false;\r\n            });\r\n            // 尝试调用u-form的验证方法\r\n            formValidate(this, \"blur\");\r\n        },\r\n        // 输入框聚焦时触发\r\n        onFocus(event) {\r\n            this.focused = true;\r\n            this.$emit(\"focus\");\r\n        },\r\n        doFocus() {\r\n            this.$refs['input-native'].focus();\r\n        },\r\n        doBlur() {\r\n            this.$refs['input-native'].blur();\r\n        },\r\n        // 点击完成按钮时触发\r\n        onConfirm(event) {\r\n            this.$emit(\"confirm\", this.innerValue);\r\n        },\r\n        // 键盘高度发生变化的时候触发此事件\r\n        // 兼容性：微信小程序2.7.0+、App 3.1.0+\r\n\t\tonkeyboardheightchange(event) {\r\n            this.$emit(\"keyboardheightchange\", event);\r\n        },\r\n        onnicknamereview(event) {\r\n            this.$emit(\"nicknamereview\", event);\r\n        },\r\n        // 内容发生变化，进行处理\r\n        valueChange(value, isOut = false) {\r\n            if(this.clearInput) {\r\n                this.innerValue = '';\r\n                this.clearInput = false;\r\n            }\r\n            this.$nextTick(() => {\r\n                if (!isOut || this.clearInput) {\r\n                    // 标识value值的变化是由内部引起的\r\n                    this.changeFromInner = true;\r\n                    this.$emit(\"change\", value);\r\n\r\n                    // #ifdef VUE3\r\n                    this.$emit(\"update:modelValue\", value);\r\n                    // #endif\r\n                    // #ifdef VUE2\r\n                    this.$emit(\"input\", value);\r\n                    // #endif\r\n                }\r\n\r\n                // 尝试调用u-form的验证方法\r\n                formValidate(this, \"change\");\r\n            });\r\n        },\r\n        // 点击清除控件\r\n        onClear() {\r\n            this.clearInput = true;\r\n            this.innerValue = \"\";\r\n            this.$nextTick(() => {\r\n                this.valueChange(\"\");\r\n                this.$emit(\"clear\");\r\n            });\r\n        },\r\n        /**\r\n         * 在安卓nvue上，事件无法冒泡\r\n         * 在某些时间，我们希望监听u-from-item的点击事件，此时会导致点击u-form-item内的u-input后\r\n         * 无法触发u-form-item的点击事件，这里通过手动调用u-form-item的方法进行触发\r\n         */\r\n        clickHandler() {\r\n            if (this.disabled || this.readonly) {\r\n                uni.hideKeyboard();\r\n            }\r\n            // #ifdef APP-NVUE\r\n            if (os() === \"android\") {\r\n                const formItem = $parent.call(this, \"u-form-item\");\r\n                if (formItem) {\r\n                    formItem.clickHandler();\r\n                }\r\n            }\r\n            // #endif\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.u-input {\r\n    @include flex(row);\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    flex: 1;\r\n\r\n    &--radius,\r\n    &--square {\r\n        border-radius: 4px;\r\n    }\r\n\r\n    &--no-radius {\r\n        border-radius: 0;\r\n    }\r\n\r\n    &--circle {\r\n        border-radius: 100px;\r\n    }\r\n\r\n    &__content {\r\n        flex: 1;\r\n        @include flex(row);\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        &__field-wrapper {\r\n            position: relative;\r\n            @include flex(row);\r\n            margin: 0;\r\n            flex: 1;\r\n\t\t\t\r\n\t\t\t&__field {\r\n\t\t\t\tline-height: 26px;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\theight: 24px;\r\n\t\t\t\tfont-size: 15px;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n        }\r\n\r\n        &__clear {\r\n            width: 20px;\r\n            height: 20px;\r\n            border-radius: 100px;\r\n            background-color: #c6c7cb;\r\n            @include flex(row);\r\n            align-items: center;\r\n            justify-content: center;\r\n            transform: scale(0.82);\r\n            margin-left: 4px;\r\n        }\r\n\r\n        &__subfix-icon {\r\n            margin-left: 4px;\r\n        }\r\n\r\n        &__prefix-icon {\r\n            margin-right: 4px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-input/u-input.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "formValidate", "deepMerge", "addStyle", "addUnit", "sleep", "uni"], "mappings": ";;;;;;AA8HA,MAAK,YAAU;AAAA,EACX,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,mDAAK;AAAA,EAC9B,OAAO;AACH,WAAO;AAAA;AAAA,MAEH,YAAY;AAAA;AAAA,MAEZ,YAAY;AAAA;AAAA,MAEZ,SAAS;AAAA;AAAA,MAET,aAAa;AAAA;AAAA,MAEb,iBAAiB;AAAA;AAAA,MAE1B,gBAAgB,WAAS;AAAA;EAEvB;AAAA,EACD,UAAU;AAEN,QAAI,KAAK,WAAW;AAChB,WAAK,iBAAiB,KAAK;AAAA,IAC/B;AAAA,EACH;AAAA,EACD,OAAO;AAAA,IACH,YAAY;AAAA,MACR,WAAW;AAAA,MACX,QAAQ,QAAQ,QAAQ;AAEpB,YAAI,KAAK,mBAAmB,KAAK,eAAe,QAAQ;AACpD,eAAK,kBAAkB;AACvB;AAAA,QACJ;AACA,aAAK,aAAa;AAElB,YACI,KAAK,gBAAgB,SACpC,KAAK,oBAAoB,OACZ;AACE,eAAK,YAAY,KAAK,YAAY,IAAI;AAAA,eACnC;AAEH,cAAG,CAAC,KAAK;AAAaC,sDAAY,aAAC,MAAM,QAAQ;AAAA,QACjE;AACY,aAAK,cAAc;AAEnB,aAAK,kBAAkB;AAAA,MAC3B;AAAA,IACJ;AAAA,EACH;AAAA,EACD,UAAU;AAAA;AAAA,IAEN,cAAc;AACV,YAAM,EAAE,WAAW,UAAU,SAAS,WAAS,IAAM;AACrD,aAAO,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,WAAW,eAAe;AAAA,IAClE;AAAA;AAAA,IAED,aAAa;AACT,UAAI,UAAU,CAAE,GACZ,EAAE,QAAQ,UAAU,MAAM,IAAI;AAClC,iBAAW,eACN,UAAU,QAAQ,OAAO,CAAC,YAAY,iBAAiB,CAAC;AAC7D,cAAQ,KAAK,YAAY,KAAK,EAAE;AAChC,iBAAW,aACN,UAAU,QAAQ,OAAO;AAAA,QACtB;AAAA,QACA;AAAA,MACH,CAAA;AACL,aAAO,QAAQ,KAAK,GAAG;AAAA,IAC1B;AAAA;AAAA,IAED,eAAe;AACX,YAAM,QAAQ,CAAA;AAEd,UAAI,KAAK,UAAU;AACf,cAAM,kBAAkB,KAAK;AAAA,MACjC;AAEA,UAAI,KAAK,WAAW,QAAQ;AACxB,cAAM,UAAU;AAAA,aACb;AAEH,cAAM,aAAa;AACnB,cAAM,gBAAgB;AACtB,cAAM,cAAc;AACpB,cAAM,eAAe;AAAA,MACzB;AACA,aAAOC,0CAAAA,UAAU,OAAOC,0CAAAA,SAAS,KAAK,WAAW,CAAC;AAAA,IACrD;AAAA;AAAA,IAED,aAAa;AACT,YAAM,QAAQ;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,UAAUC,0CAAAA,QAAQ,KAAK,QAAQ;AAAA,QAC3C,WAAW,KAAK;AAAA;AAER,aAAO;AAAA,IACV;AAAA,EACJ;AAAA,EAED,OAAO,CAAC,qBAAqB,SAAS,QAAQ,UAAU,WAAW,SAAS,wBAAwB,gBAAgB;AAAA,EAEpH,SAAS;AAAA;AAAA,IAEX,aAAa,GAAG;AACf,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAEK,QAAQ,GAAG;AACP,UAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,UAAU,CAAA;AAGjC,WAAK,aAAa;AAClB,WAAK,UAAU,MAAM;AACjB,YAAI,cAAc,KAAK,eAAe,KAAK;AAC9C,aAAK,aAAa;AACf,aAAK,YAAY,WAAW;AAAA,OAC/B;AAAA,IACJ;AAAA;AAAA,IAED,OAAO,OAAO;AACV,WAAK,MAAM,QAAQ,MAAM,OAAO,KAAK;AAGrCC,sDAAM,GAAG,EAAE,KAAK,MAAM;AAClB,aAAK,UAAU;AAAA,MACnB,CAAC;AAEDJ,6DAAa,MAAM,MAAM;AAAA,IAC5B;AAAA;AAAA,IAED,QAAQ,OAAO;AACX,WAAK,UAAU;AACf,WAAK,MAAM,OAAO;AAAA,IACrB;AAAA,IACD,UAAU;AACN,WAAK,MAAM,cAAc,EAAE,MAAK;AAAA,IACnC;AAAA,IACD,SAAS;AACL,WAAK,MAAM,cAAc,EAAE,KAAI;AAAA,IAClC;AAAA;AAAA,IAED,UAAU,OAAO;AACb,WAAK,MAAM,WAAW,KAAK,UAAU;AAAA,IACxC;AAAA;AAAA;AAAA,IAGP,uBAAuB,OAAO;AACpB,WAAK,MAAM,wBAAwB,KAAK;AAAA,IAC3C;AAAA,IACD,iBAAiB,OAAO;AACpB,WAAK,MAAM,kBAAkB,KAAK;AAAA,IACrC;AAAA;AAAA,IAED,YAAY,OAAO,QAAQ,OAAO;AAC9B,UAAG,KAAK,YAAY;AAChB,aAAK,aAAa;AAClB,aAAK,aAAa;AAAA,MACtB;AACA,WAAK,UAAU,MAAM;AACjB,YAAI,CAAC,SAAS,KAAK,YAAY;AAE3B,eAAK,kBAAkB;AACvB,eAAK,MAAM,UAAU,KAAK;AAG1B,eAAK,MAAM,qBAAqB,KAAK;AAAA,QAKzC;AAGAA,+DAAa,MAAM,QAAQ;AAAA,MAC/B,CAAC;AAAA,IACJ;AAAA;AAAA,IAED,UAAU;AACN,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,UAAU,MAAM;AACjB,aAAK,YAAY,EAAE;AACnB,aAAK,MAAM,OAAO;AAAA,MACtB,CAAC;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,eAAe;AACX,UAAI,KAAK,YAAY,KAAK,UAAU;AAChCK,sBAAG,MAAC,aAAY;AAAA,MACpB;AAAA,IASH;AAAA,EACJ;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3UA,GAAG,gBAAgB,SAAS;"}