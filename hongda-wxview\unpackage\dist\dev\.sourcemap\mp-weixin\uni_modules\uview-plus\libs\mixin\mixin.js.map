{"version": 3, "file": "mixin.js", "sources": ["uni_modules/uview-plus/libs/mixin/mixin.js"], "sourcesContent": ["import { defineMixin } from '../vue'\r\nimport { deepMerge, $parent, sleep } from '../function/index'\r\nimport test from '../function/test'\r\nimport route from '../util/route'\r\n// #ifdef APP-NVUE\r\n// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\r\nconst dom = uni.requireNativePlugin('dom')\r\n// #endif\r\n\r\nexport const mixin = defineMixin({\r\n    // 定义每个组件都可能需要用到的外部样式以及类名\r\n    props: {\r\n        // 每个组件都有的父组件传递的样式，可以为字符串或者对象形式\r\n        customStyle: {\r\n            type: [Object, String],\r\n            default: () => ({})\r\n        },\r\n        customClass: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        // 跳转的页面路径\r\n        url: {\r\n            type: String,\r\n            default: ''\r\n        },\r\n        // 页面跳转的类型\r\n        linkType: {\r\n            type: String,\r\n            default: 'navigateTo'\r\n        }\r\n    },\r\n    data() {\r\n        return {}\r\n    },\r\n    onLoad() {\r\n        // getRect挂载到$u上，因为这方法需要使用in(this)，所以无法把它独立成一个单独的文件导出\r\n        this.$u.getRect = this.$uGetRect\r\n    },\r\n    created() {\r\n        // 组件当中，只有created声明周期，为了能在组件使用，故也在created中将方法挂载到$u\r\n        this.$u.getRect = this.$uGetRect\r\n    },\r\n    computed: {\r\n        // 在2.x版本中，将会把$u挂载到uni对象下，导致在模板中无法使用uni.$u.xxx形式\r\n        // 所以这里通过computed计算属性将其附加到this.$u上，就可以在模板或者js中使用uni.$u.xxx\r\n        // 只在nvue环境通过此方式引入完整的$u，其他平台会出现性能问题，非nvue则按需引入（主要原因是props过大）\r\n        $u() {\r\n            // #ifndef APP-NVUE\r\n            // 在非nvue端，移除props，http，mixin等对象，避免在小程序setData时数据过大影响性能\r\n            return deepMerge(uni.$u, {\r\n                props: undefined,\r\n                http: undefined,\r\n                mixin: undefined\r\n            })\r\n            // #endif\r\n            // #ifdef APP-NVUE\r\n            return uni.$u\r\n            // #endif\r\n        },\r\n        /**\r\n         * 生成bem规则类名\r\n         * 由于微信小程序，H5，nvue之间绑定class的差异，无法通过:class=\"[bem()]\"的形式进行同用\r\n         * 故采用如下折中做法，最后返回的是数组（一般平台）或字符串（支付宝和字节跳动平台），类似['a', 'b', 'c']或'a b c'的形式\r\n         * @param {String} name 组件名称\r\n         * @param {Array} fixed 一直会存在的类名\r\n         * @param {Array} change 会根据变量值为true或者false而出现或者隐藏的类名\r\n         * @returns {Array|string}\r\n         */\r\n        bem() {\r\n            return function (name, fixed, change) {\r\n                // 类名前缀\r\n                const prefix = `u-${name}--`\r\n                const classes = {}\r\n                if (fixed) {\r\n                    fixed.map((item) => {\r\n                        // 这里的类名，会一直存在\r\n                        classes[prefix + this[item]] = true\r\n                    })\r\n                }\r\n                if (change) {\r\n                    change.map((item) => {\r\n                        // 这里的类名，会根据this[item]的值为true或者false，而进行添加或者移除某一个类\r\n                        this[item] ? (classes[prefix + item] = this[item]) : (delete classes[prefix + item])\r\n                    })\r\n                }\r\n                return Object.keys(classes)\r\n                    // 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\r\n                    // #ifdef MP-ALIPAY || MP-TOUTIAO || MP-LARK\r\n                    .join(' ')\r\n                    // #endif\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        // 跳转某一个页面\r\n        openPage(urlKey = 'url') {\r\n            const url = this[urlKey]\r\n            if (url) {\r\n                // h5官方回应：发行h5会自动摇树优化，所有使用uni的地方，都会被直接转换成具体的API调用 https://ask.dcloud.net.cn/question/161523?notification_id-1201922__rf-false__item_id-226372\r\n                // 使用封装的 route 进行跳转（直接调用方法），不使用 uni 对象\r\n                route({ type: this.linkType, url })\r\n                // 执行类似uni.navigateTo的方法\r\n                // uni[this.linkType]({\r\n                //     url\r\n                // })\r\n            }\r\n        },\r\n        navTo(url = '', linkType = 'navigateTo') {\r\n            route({ type: this.linkType, url })\r\n        },\r\n        // 查询节点信息\r\n        // 目前此方法在支付宝小程序中无法获取组件跟接点的尺寸，为支付宝的bug(2020-07-21)\r\n        // 解决办法为在组件根部再套一个没有任何作用的view元素\r\n        $uGetRect(selector, all) {\r\n            return new Promise((resolve) => {\r\n                // #ifndef APP-NVUE\r\n                uni.createSelectorQuery()\r\n                    .in(this)[all ? 'selectAll' : 'select'](selector)\r\n                    .boundingClientRect((rect) => {\r\n                        if (all && Array.isArray(rect) && rect.length) {\r\n                            resolve(rect)\r\n                        }\r\n                        if (!all && rect) {\r\n                            resolve(rect)\r\n                        }\r\n                    })\r\n                    .exec()\r\n                // #endif\r\n                \r\n                // #ifdef APP-NVUE\r\n                sleep(30).then(() => {\r\n                    let selectorNvue = selector.substring(1) // 去掉开头的#或者.\r\n                    let selectorRef = this.$refs[selectorNvue]\r\n                    if (!selectorRef) {\r\n                        // console.log('不存在元素，请检查是否设置了ref属性' + selectorNvue + '。')\r\n                        resolve({\r\n                            with: 0,\r\n                            height: 0,\r\n                            left: 0,\r\n                            right: 0,\r\n                            top: 0,\r\n                            bottom: 0\r\n                        }) \r\n                    }\r\n                    dom.getComponentRect(selectorRef, res => {\r\n                        // console.log(res)\r\n                        resolve(res.size)\r\n                    })\r\n                })\r\n                // #endif\r\n            })\r\n        },\r\n        getParentData(parentName = '') {\r\n            // 避免在created中去定义parent变量\r\n            if (!this.parent) this.parent = {}\r\n            // 这里的本质原理是，通过获取父组件实例(也即类似u-radio的父组件u-radio-group的this)\r\n            // 将父组件this中对应的参数，赋值给本组件(u-radio的this)的parentData对象中对应的属性\r\n            // 之所以需要这么做，是因为所有端中，头条小程序不支持通过this.parent.xxx去监听父组件参数的变化\r\n            // 此处并不会自动更新子组件的数据，而是依赖父组件u-radio-group去监听data的变化，手动调用更新子组件的方法去重新获取\r\n            this.parent = $parent.call(this, parentName)\r\n            if (this.parent.children) {\r\n                // 如果父组件的children不存在本组件的实例，才将本实例添加到父组件的children中\r\n                this.parent.children.indexOf(this) === -1 && this.parent.children.push(this)\r\n            }\r\n            if (this.parent && this.parentData) {\r\n                // 历遍parentData中的属性，将parent中的同名属性赋值给parentData\r\n                Object.keys(this.parentData).map((key) => {\r\n                    this.parentData[key] = this.parent[key]\r\n                })\r\n            }\r\n        },\r\n        // 阻止事件冒泡\r\n        preventEvent(e) {\r\n            e && typeof (e.stopPropagation) === 'function' && e.stopPropagation()\r\n        },\r\n        // 空操作\r\n        noop(e) {\r\n            this.preventEvent(e)\r\n        }\r\n    },\r\n    onReachBottom() {\r\n        uni.$emit('uOnReachBottom')\r\n\t},\r\n\tbeforeUnmount() {\r\n        // 判断当前页面是否存在parent和chldren，一般在checkbox和checkbox-group父子联动的场景会有此情况\r\n        // 组件销毁时，移除子组件在父组件children数组中的实例，释放资源，避免数据混乱\r\n        if (this.parent && test.array(this.parent.children)) {\r\n            // 组件销毁时，移除父组件中的children数组中对应的实例\r\n            const childrenList = this.parent.children\r\n            childrenList.map((child, index) => {\r\n                // 如果相等，则移除\r\n                if (child === this) {\r\n                    childrenList.splice(index, 1)\r\n                }\r\n            })\r\n        }\r\n    }\r\n})\r\n\r\nexport default mixin\r\n"], "names": ["defineMixin", "deepMerge", "uni", "route", "$parent", "test"], "mappings": ";;;;;;AASY,MAAC,QAAQA,+BAAAA,YAAY;AAAA;AAAA,EAE7B,OAAO;AAAA;AAAA,IAEH,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO,CAAA;AAAA,IACnB;AAAA,IACD,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,EACJ;AAAA,EACD,OAAO;AACH,WAAO,CAAE;AAAA,EACZ;AAAA,EACD,SAAS;AAEL,SAAK,GAAG,UAAU,KAAK;AAAA,EAC1B;AAAA,EACD,UAAU;AAEN,SAAK,GAAG,UAAU,KAAK;AAAA,EAC1B;AAAA,EACD,UAAU;AAAA;AAAA;AAAA;AAAA,IAIN,KAAK;AAGD,aAAOC,0CAAS,UAACC,cAAG,MAAC,IAAI;AAAA,QACrB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACvB,CAAa;AAAA,IAKJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUD,MAAM;AACF,aAAO,SAAU,MAAM,OAAO,QAAQ;AAElC,cAAM,SAAS,KAAK,IAAI;AACxB,cAAM,UAAU,CAAE;AAClB,YAAI,OAAO;AACP,gBAAM,IAAI,CAAC,SAAS;AAEhB,oBAAQ,SAAS,KAAK,IAAI,CAAC,IAAI;AAAA,UACvD,CAAqB;AAAA,QACJ;AACD,YAAI,QAAQ;AACR,iBAAO,IAAI,CAAC,SAAS;AAEjB,iBAAK,IAAI,IAAK,QAAQ,SAAS,IAAI,IAAI,KAAK,IAAI,IAAM,OAAO,QAAQ,SAAS,IAAI;AAAA,UAC1G,CAAqB;AAAA,QACJ;AACD,eAAO,OAAO,KAAK,OAAO;AAAA,MAK7B;AAAA,IACJ;AAAA,EACJ;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,SAAS,SAAS,OAAO;AACrB,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,KAAK;AAGLC,8CAAAA,MAAM,EAAE,MAAM,KAAK,UAAU,IAAG,CAAE;AAAA,MAKrC;AAAA,IACJ;AAAA,IACD,MAAM,MAAM,IAAI,WAAW,cAAc;AACrCA,4CAAAA,MAAM,EAAE,MAAM,KAAK,UAAU,IAAG,CAAE;AAAA,IACrC;AAAA;AAAA;AAAA;AAAA,IAID,UAAU,UAAU,KAAK;AACrB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAE5BD,sBAAAA,MAAI,oBAAqB,EACpB,GAAG,IAAI,EAAE,MAAM,cAAc,QAAQ,EAAE,QAAQ,EAC/C,mBAAmB,CAAC,SAAS;AAC1B,cAAI,OAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC3C,oBAAQ,IAAI;AAAA,UACf;AACD,cAAI,CAAC,OAAO,MAAM;AACd,oBAAQ,IAAI;AAAA,UACf;AAAA,QACzB,CAAqB,EACA,KAAM;AAAA,MAwB3B,CAAa;AAAA,IACJ;AAAA,IACD,cAAc,aAAa,IAAI;AAE3B,UAAI,CAAC,KAAK;AAAQ,aAAK,SAAS,CAAE;AAKlC,WAAK,SAASE,0CAAAA,QAAQ,KAAK,MAAM,UAAU;AAC3C,UAAI,KAAK,OAAO,UAAU;AAEtB,aAAK,OAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,MAC9E;AACD,UAAI,KAAK,UAAU,KAAK,YAAY;AAEhC,eAAO,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AACtC,eAAK,WAAW,GAAG,IAAI,KAAK,OAAO,GAAG;AAAA,QAC1D,CAAiB;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAED,aAAa,GAAG;AACZ,WAAK,OAAQ,EAAE,oBAAqB,cAAc,EAAE,gBAAiB;AAAA,IACxE;AAAA;AAAA,IAED,KAAK,GAAG;AACJ,WAAK,aAAa,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACD,gBAAgB;AACZF,kBAAG,MAAC,MAAM,gBAAgB;AAAA,EAChC;AAAA,EACD,gBAAgB;AAGT,QAAI,KAAK,UAAUG,yCAAI,KAAC,MAAM,KAAK,OAAO,QAAQ,GAAG;AAEjD,YAAM,eAAe,KAAK,OAAO;AACjC,mBAAa,IAAI,CAAC,OAAO,UAAU;AAE/B,YAAI,UAAU,MAAM;AAChB,uBAAa,OAAO,OAAO,CAAC;AAAA,QAC/B;AAAA,MACjB,CAAa;AAAA,IACJ;AAAA,EACJ;AACL,CAAC;;"}