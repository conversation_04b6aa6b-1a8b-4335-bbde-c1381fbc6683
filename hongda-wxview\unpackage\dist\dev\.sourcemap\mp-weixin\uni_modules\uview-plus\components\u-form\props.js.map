{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-form/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 当前form的需要验证字段的集合\r\n        model: {\r\n            type: Object,\r\n            default: () => defProps.form.model\r\n        },\r\n        // 验证规则\r\n        rules: {\r\n            type: [Object, Function, Array],\r\n            default: () => defProps.form.rules\r\n        },\r\n        // 有错误时的提示方式，message-提示信息，toast-进行toast提示\r\n        // border-bottom-下边框呈现红色，none-无提示\r\n        errorType: {\r\n            type: String,\r\n            default: () => defProps.form.errorType\r\n        },\r\n        // 是否显示表单域的下划线边框\r\n        borderBottom: {\r\n            type: Boolean,\r\n            default: () => defProps.form.borderBottom\r\n        },\r\n        // label的位置，left-左边，top-上边\r\n        labelPosition: {\r\n            type: String,\r\n            default: () => defProps.form.labelPosition\r\n        },\r\n        // label的宽度，单位px\r\n        labelWidth: {\r\n            type: [String, Number],\r\n            default: () => defProps.form.labelWidth\r\n        },\r\n        // lable字体的对齐方式\r\n        labelAlign: {\r\n            type: String,\r\n            default: () => defProps.form.labelAlign\r\n        },\r\n        // lable的样式，对象形式\r\n        labelStyle: {\r\n            type: Object,\r\n            default: () => defProps.form.labelStyle\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,UAAU,KAAK;AAAA,MAC9B,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA,IAGD,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}