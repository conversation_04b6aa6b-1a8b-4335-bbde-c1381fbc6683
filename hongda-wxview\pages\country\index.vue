：<template>
  <view class="page-container">
    <view class="header-background" :style="{ backgroundImage: `url(${headerBgUrl})` }">
      <view class="page-title-wrapper">
        <text class="page-title">国别列表</text>
      </view>

      <view class="search-bar-wrapper">
        <uni-search-bar
            class="search-bar"
            placeholder="搜索国别名称"
            v-model="searchKeyword"
            @confirm="onSearch"
            @cancel="onCancelSearch"
            @clear="onSearch"
            radius="100"
            bgColor="#ffffff"
        />
      </view>

      <view class="continent-tabs-container">
        <scroll-view class="continent-tabs" scroll-x="true" :show-scrollbar="false"  @scroll="onTabsScroll">
          <view
              v-for="(tab, index) in continents"
              :key="tab.value"
              :class="['tab-item', { active: activeContinent === tab.value }]"
              @click="onFilterTap(tab.value, index)"
              :id="'tab-' + index"
              :style="{ backgroundImage: activeContinent === tab.value ? `url(${activeTabBgUrl})` : 'none' }"
          >
            {{ tab.label }}
          </view>
        </scroll-view>
        <view class="active-indicator" :style="{ left: indicatorLeft + 'px' }"></view>
      </view>
    </view>

    <scroll-view class="country-list-wrapper" scroll-y @scrolltolower="loadMore">
      <view class="country-card" v-for="item in countryList" :key="item.id" @click="goToDetail(item.id)">
        <image class="country-image" :src="baseUrl + item.listCoverUrl" mode="aspectFill" />

        <view class="country-info">
          <view class="info-top">
            <view class="name-line">
              <text class="name-cn">{{ item.nameCn }}</text>
              <text class="name-en">{{ item.nameEn }}</text>
            </view>
            <text class="summary">{{ item.summary }}</text>
          </view>
          <image class="country-flag" :src="baseUrl + item.flagUrl" mode="scaleToFill" />
        </view>
      </view>

      <view v-if="loading" class="status-tip">
        <uni-load-more status="loading" />
      </view>
      <view v-else-if="countryList.length === 0" class="empty-message-container">
        <text class="empty-text">暂无相关国家信息</text>
      </view>

      <view class="scroll-view-bottom-spacer"></view>
    </scroll-view>

    <CustomTabBar :current="3" />
  </view>
</template>

<script setup>
import { ref, computed, nextTick, getCurrentInstance } from 'vue';
import { onPullDownRefresh, onLoad, onShow } from '@dcloudio/uni-app';
import { getCountryList } from '@/api/content/country.js'; // 引入API
import { IMAGE_BASE_URL } from '@/utils/config.js';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';

// 静态资源URL
const headerBgUrl = ref('http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E5%9B%BD%E5%88%AB%E9%A1%B6%E9%83%A8%E8%83%8C%E6%99%AF%402x.png');
const activeTabBgUrl = ref('http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png');

// 基础配置和状态
const baseUrl = IMAGE_BASE_URL;
const searchKeyword = ref('');
const activeContinent = ref('ALL');
const countryList = ref([]);
const loading = ref(false);
const instance = getCurrentInstance();

// 大洲Tab数据
const continents = [
  { label: '全部', value: 'ALL' },
  { label: '亚洲', value: 'ASIA' },
  { label: '欧洲', value: 'EUROPE' },
  { label: '北美洲', value: 'NORTH_AMERICA' },
  { label: '南美洲', value: 'SOUTH_AMERICA' },
  { label: '非洲', value: 'AFRICA' },
  { label: '大洋洲', value: 'OCEANIA' }
];

// --- 指示器逻辑所需的状态 ---
// 当前激活Tab的索引
const activeIndex = ref(0);
// 存储所有Tab初始布局信息的数组
const tabInitialPositions = ref([]);
// Tab栏的水平滚动距离
const tabsScrollLeft = ref(0);


/**
 * @description: 使用 computed 属性动态计算指示器的 left 值，以实现高性能跟随
 */
const indicatorLeft = computed(() => {
  // 确保已获取到Tab的布局信息，否则返回一个屏幕外的值
  if (!tabInitialPositions.value || tabInitialPositions.value.length === 0) {
    return -999;
  }

  const activeTabInfo = tabInitialPositions.value[activeIndex.value];
  if (!activeTabInfo) {
    return -999;
  }

  // 指示器三角形宽度的一半 (20rpx)，需转换为px单位进行计算
  const indicatorHalfWidth = uni.upx2px(20);

  // 核心计算公式: (Tab的初始left - 当前滚动距离) + (Tab宽度 / 2) - (指示器宽度 / 2)
  const finalLeft = (activeTabInfo.left - tabsScrollLeft.value) + (activeTabInfo.width / 2) - indicatorHalfWidth;

  return finalLeft;
});

/**
 * @description: Tab滚动时的处理函数
 * @param {object} event - 滚动事件对象
 */
const onTabsScroll = (event) => {
  tabsScrollLeft.value = event.detail.scrollLeft;
};

/**
 * @description: 在DOM加载后，一次性计算所有Tab的初始位置
 */
const calculateAllTabsPosition = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance);
    query.selectAll('.tab-item').boundingClientRect(data => {
      if (data && data.length) {
        tabInitialPositions.value = data;
      }
    }).exec();
  });
};

/**
 * @description: 获取国别列表数据
 * @param {boolean} isRefresh - 是否为刷新操作
 */
const fetchData = async (isRefresh = false) => {
  if (loading.value) return;
  loading.value = true;
  try {
    const res = await getCountryList({
      continent: activeContinent.value,
      keyword: searchKeyword.value
    });
    countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];
  } catch (error) {
    console.error('获取国别列表失败:', error);
    uni.showToast({ title: '数据加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    if (isRefresh) {
      uni.stopPullDownRefresh();
    }
  }
};

/**
 * @description: 点击Tab切换筛选
 * @param {string} continentValue - 大洲的枚举值
 * @param {number} index - 被点击的Tab的索引
 */
const onFilterTap = (continentValue, index) => {
  if (activeContinent.value === continentValue) return;

  activeContinent.value = continentValue;
  activeIndex.value = index; // 更新当前激活的索引

  fetchData(true);
};

/**
 * @description: 搜索确认
 */
const onSearch = () => {
  fetchData(true);
};

/**
 * @description: 取消搜索
 */
const onCancelSearch = () => {
  searchKeyword.value = '';
  fetchData(true);
};

/**
 * @description: 跳转到国别详情页
 * @param {number} countryId - 国家ID
 */
const goToDetail = (countryId) => {
  uni.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });
};

/**
 * @description: 滚动到底部加载更多（此处为空实现，可根据需求补充）
 */
const loadMore = () => {};


// --- 页面生命周期钩子 ---

onLoad(() => {
  fetchData(true);
  // 延迟执行以确保DOM渲染完毕
  setTimeout(() => {
    calculateAllTabsPosition();
  }, 150);
});

onShow(() => {
  uni.hideTabBar();
});

onPullDownRefresh(() => {
  fetchData(true);
});
</script>

<style lang="scss" scoped>
/* 页面总容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: transparent;
}

/* 顶部背景区域 */
.header-background {
  height: 420rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 10;
}

/* 页面标题容器 */
.page-title-wrapper {
  position: absolute;
  top: 94rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}

/* 搜索栏包裹容器 */
.search-bar-wrapper {
  position: absolute;
  top: 182rpx;
  left: 0;
  width: 750rpx;
  height: 88rpx;
  box-sizing: border-box;
}
:deep(.uni-search-bar) {
  margin: 14rpx 16rpx 14rpx 32rpx;
  height: calc(100% - 28rpx);
  padding: 0 !important;
}
:deep(.uni-search-bar__box) {
  height: 100% !important;
  justify-content: flex-start !important;
}
:deep(.uni-search-bar__text-placeholder) {
  font-size: 28rpx !important;
  color: #9B9A9A !important;
}

:deep(.uni-searchbar__cancel) {
  /* 设置字体大小 (建议使用rpx以适应不同屏幕) */
  font-size: 30rpx !important;
  color: #FFFFFF !important;

  /* 如果需要，还可以设置字体粗细 */
  // font-weight: bold !important;
}

/* 扁扁圆圆的Tab，且有间隔 */
.continent-tabs-container {
  position: absolute;
  top: 320rpx;
  transform: translateY(-50%);
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-top: 10rpx;
}
.continent-tabs {
  white-space: nowrap;
  &::-webkit-scrollbar {
    display: none;
  }
}
.tab-item {
  display: inline-block;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #23232A;
  border-radius: 30rpx;
  background-color: #FFFFFF;
  transition: all 0.3s;
  background-size: cover;
  background-position: center;
  margin-right: 20rpx;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    color: #23232A;
    font-weight: bold;
  }
}

/* 突起的尖角指示器 */
.active-indicator {
  position: absolute;
  bottom: -32rpx;
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 25rpx solid #fff;
  transition: left 0.3s ease;
  z-index: 10;
}

/* 国别列表包裹容器 - 样式保持不变 */
.country-list-wrapper {
  flex: 1;
  height: 0;
  background-color: #ffffff;
  position: relative;
  z-index: 15; /* 提高层级 */
  border-radius: 30rpx 30rpx 0 0; /* 使用简写 */
  margin-top: -32rpx;
  box-sizing: border-box;
  overflow: hidden; /* 确保内容不溢出圆角 */
}

/* 国别卡片 - 样式保持不变 */
.country-card {
  display: flex;
  align-items: center;
  width: 100%; /* 卡片宽度撑满容器 */
  height: 272rpx;
  padding: 0 30rpx;
  background-color: #fff;
  box-sizing: border-box;
  /* 使用下边框作为卡片之间的分隔线 */
  border-bottom: 1rpx solid #f0f0f0;
}

/* 卡片左侧封面图 */
.country-image {
  flex-shrink: 0;
  width: 336rpx;
  height: 192rpx;
  border-radius: 16rpx;
  /* [新增] 增加右边距，与右侧信息区隔开 */
  margin-right: 24rpx;
}

/* 卡片右侧信息布局 */
.country-info {
  flex: 1;
  min-width: 0;
  /* [主要修改] 高度与图片保持一致(192rpx)，为'space-between'提供空间 */
  height: 192rpx;
  display: flex;
  flex-direction: column;
  /* [关键属性] 将子元素推向容器的顶部和底部 */
  justify-content: space-between;
}

/* 顶部信息块（国名+简介）*/
.info-top {
  display: flex;
  flex-direction: column;
}

/* 国名行 */
.name-line {
  display: flex;
  align-items: baseline;
  /* [新增] 增加国名和简介之间的垂直间距 */
  margin-bottom: 8rpx;

  .name-cn {
    font-size: 28rpx;
    font-weight: bold;
    color: #23232A;
  }
  .name-en {
    font-size: 22rpx;
    margin-left: 12rpx;
    color: #9B9A9A;
  }
}

/* 简介 */
.summary {
  font-size: 24rpx;
  color: #23232A;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 简介最多显示2行 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 国旗 */
.country-flag {
  /* [修改] 更新国旗尺寸 */
  width: 60rpx;
  height: 40rpx; /* 调整为更协调的比例 */
  border-radius: 4rpx;
  border: 1rpx solid #eee;
  align-self: flex-start; /* 保持在左侧对齐 */
}

/* 状态提示 */
.status-tip, .empty-message-container {
  padding: 80rpx 0;
  text-align: center;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.scroll-view-bottom-spacer {
  height: 180rpx;
}
</style>