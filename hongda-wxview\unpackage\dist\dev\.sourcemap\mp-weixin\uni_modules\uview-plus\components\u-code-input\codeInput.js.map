{"version": 3, "file": "codeInput.js", "sources": ["uni_modules/uview-plus/components/u-code-input/codeInput.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:55:58\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/codeInput.js\r\n */\r\nexport default {\r\n    // codeInput 组件\r\n    codeInput: {\r\n\t\tadjustPosition: true,\r\n        maxlength: 6,\r\n        dot: false,\r\n        mode: 'box',\r\n        hairline: false,\r\n        space: 10,\r\n        value: '',\r\n        focus: false,\r\n        bold: false,\r\n        color: '#606266',\r\n        fontSize: 18,\r\n        size: 35,\r\n        disabledKeyboard: false,\r\n        borderColor: '#c9cacc',\r\n\t\tdisabledDot: true\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACb,gBAAgB;AAAA,IACV,WAAW;AAAA,IACX,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACnB,aAAa;AAAA,EACV;AACL;;"}