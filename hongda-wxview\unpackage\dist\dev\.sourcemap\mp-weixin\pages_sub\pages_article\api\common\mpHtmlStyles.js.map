{"version": 3, "file": "mpHtmlStyles.js", "sources": ["pages_sub/pages_article/api/common/mpHtmlStyles.js"], "sourcesContent": ["export const tagStyle = {\r\n    p: 'line-height: 1.75; margin: 32rpx 0; color: #3D424D; font-size: 30rpx;',\r\n    h1: 'font-size: 44rpx; font-weight: 700; margin: 40rpx 0 20rpx 0; color: #303133;',\r\n    h2: 'font-size: 40rpx; font-weight: 700; margin: 36rpx 0 20rpx 0; color: #303133; border-bottom: 2rpx solid #e9e9eb; padding-bottom: 10rpx;',\r\n    h3: 'font-size: 36rpx; font-weight: 700; margin: 30rpx 0 16rpx 0; color: #303133; border-left: 8rpx solid #3c9cff; padding-left: 20rpx;',\r\n    blockquote: 'border-left: 6rpx solid #dcdfe6; padding: 20rpx 30rpx; margin: 30rpx 0; background: #f8f9fa; color: #606266; font-style: italic;',\r\n    pre: 'background: #f8f9fa; color: #303133; padding: 30rpx; margin: 30rpx 0; border-radius: 16rpx; font-family: \"Fira Code\", monospace; overflow-x: auto; border: 2rpx solid #e9e9eb;',\r\n    code: 'font-family: \"Fira Code\", monospace; background: #e9e9eb; color: #e53e3e; padding: 4rpx 10rpx; border-radius: 8rpx; font-size: 0.9em;',\r\n    ul: 'padding-left: 50rpx; margin: 30rpx 0;',\r\n    ol: 'padding-left: 50rpx; margin: 30rpx 0;',\r\n    li: 'margin-bottom: 16rpx; line-height: 1.7; color: #3D424D;',\r\n    table: 'width: 100% !important; border-collapse: collapse; margin: 40rpx 0; border-radius: 16rpx; overflow: hidden; box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.05);',\r\n    th: 'background: #f8f9fa; color: #303133; padding: 24rpx 30rpx; text-align: left; font-weight: 600; font-size: 28rpx; border: 2rpx solid #e9e9eb;',\r\n    td: 'border: 2rpx solid #e9e9eb; padding: 24rpx 30rpx; background: #ffffff; color: #3D424D; font-size: 28rpx;',\r\n    img: 'max-width: 100%; height: auto; border-radius: 16rpx; display: block; margin: 40rpx auto; box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);'\r\n};\r\n\r\nexport const containerStyle = `\r\ntable {\r\n  width: 100% !important;\r\n  border-collapse: collapse !important;\r\n  margin: 40rpx 0 !important;\r\n  border-radius: 16rpx !important;\r\n  overflow: hidden !important;\r\n  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05) !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n}\r\nth {\r\n  background: #f8f9fa !important;\r\n  color: #303133 !important;\r\n  padding: 24rpx !important;\r\n  text-align: left !important;\r\n  font-weight: 600 !important;\r\n  font-size: 28rpx !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n}\r\ntd {\r\n  border: 2rpx solid #e9e9eb !important;\r\n  padding: 24rpx !important;\r\n  background: #ffffff !important;\r\n  color: #3D424D !important;\r\n  font-size: 28rpx !important;\r\n  vertical-align: middle !important;\r\n}\r\nthead {\r\n  background: #f8f9fa !important;\r\n}\r\ntbody {\r\n  background: #ffffff !important;\r\n}\r\n`;\r\n\r\n\r\n"], "names": [], "mappings": ";AAAY,MAAC,WAAW;AAAA,EACpB,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AACT;AAEY,MAAC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;"}