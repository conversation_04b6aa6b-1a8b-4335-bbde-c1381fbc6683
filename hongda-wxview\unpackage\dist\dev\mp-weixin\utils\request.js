"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("./config.js");
const baseURL = utils_config.config.baseUrl + "/api/v1";
const request = (options) => {
  return new Promise((resolve, reject) => {
    const token = common_vendor.index.getStorageSync("token");
    const header = {
      "Content-Type": "application/json",
      ...options.header
    };
    if (token) {
      header["Authorization"] = "Bearer " + token;
    }
    let url = baseURL + options.url;
    let data = options.data || {};
    if (options.method === "GET" && options.params) {
      const queryString = Object.keys(options.params).filter((key) => options.params[key] !== void 0 && options.params[key] !== null).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`).join("&");
      if (queryString) {
        url += (url.includes("?") ? "&" : "?") + queryString;
      }
      data = {};
    }
    common_vendor.index.request({
      url,
      method: options.method || "GET",
      data,
      header,
      success: (res) => {
        common_vendor.index.__f__("log", "at utils/request.js:44", "API请求成功:", options.url, res);
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else {
            if (res.data.code === 403) {
              common_vendor.index.showToast({ title: res.data.msg || "账号已被禁用", icon: "none" });
              try {
                common_vendor.index.removeStorageSync("token");
              } catch (e) {
              }
              try {
                common_vendor.index.removeStorageSync("userInfo");
              } catch (e) {
              }
            }
            common_vendor.index.__f__("error", "at utils/request.js:58", "业务错误:", res.data);
            reject(new Error(res.data.msg || "请求失败"));
          }
        } else {
          common_vendor.index.__f__("error", "at utils/request.js:63", "HTTP错误:", res);
          reject(new Error("网络请求失败"));
        }
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/request.js:68", "请求失败:", options.url, err);
        reject(new Error("网络连接失败"));
      }
    });
  });
};
const get = (url, params = {}) => {
  return request({
    url,
    method: "GET",
    params
  });
};
const post = (url, data = {}) => {
  return request({
    url,
    method: "POST",
    data
  });
};
const put = (url, data = {}) => {
  return request({
    url,
    method: "PUT",
    data
  });
};
const del = (url, data = {}) => {
  return request({
    url,
    method: "DELETE",
    data
  });
};
const http = {
  get,
  post,
  put,
  del,
  request
};
exports.del = del;
exports.get = get;
exports.http = http;
exports.post = post;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
