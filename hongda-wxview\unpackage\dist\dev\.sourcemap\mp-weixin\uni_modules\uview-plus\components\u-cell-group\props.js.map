{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-cell-group/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 分组标题\r\n        title: {\r\n            type: String,\r\n            default: () => defProps.cellGroup.title\r\n        },\r\n        // 是否显示外边框\r\n        border: {\r\n            type: Boolean,\r\n            default: () => defProps.cellGroup.border\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,UAAU;AAAA,IACrC;AAAA,EACJ;AACL,CAAC;;"}