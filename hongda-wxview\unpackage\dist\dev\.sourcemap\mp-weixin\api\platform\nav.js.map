{"version": 3, "file": "nav.js", "sources": ["api/platform/nav.js"], "sourcesContent": ["import http from '@/utils/request.js';\r\n\r\n/**\r\n * 根据位置代码获取导航列表\r\n * @param {string} positionCode - 导航位置代码 (例如: 'HOME_QUICK_NAV')\r\n */\r\nexport function getNavList(positionCode) {\r\n\treturn http.get('/nav/list', {\r\n\t\tpositionCode\r\n\t});\r\n}"], "names": ["http"], "mappings": ";;AAMO,SAAS,WAAW,cAAc;AACxC,SAAOA,cAAI,KAAC,IAAI,aAAa;AAAA,IAC5B;AAAA,EACF,CAAE;AACF;;"}