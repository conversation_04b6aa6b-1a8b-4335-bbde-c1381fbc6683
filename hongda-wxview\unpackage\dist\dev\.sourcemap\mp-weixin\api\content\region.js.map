{"version": 3, "file": "region.js", "sources": ["api/content/region.js"], "sourcesContent": ["import { get } from '@/utils/request'; // 确保路径正确，指向您提供的 request.js\r\n\r\n/**\r\n * @description 获取所有已启用的内容地区列表\r\n * @returns {Promise<Object>} 包含地区列表的Promise对象\r\n */\r\nexport function listAllRegion() {\r\n    return get('/content/regions');\r\n}"], "names": ["get"], "mappings": ";;AAMO,SAAS,gBAAgB;AAC5B,SAAOA,cAAAA,IAAI,kBAAkB;AACjC;;"}