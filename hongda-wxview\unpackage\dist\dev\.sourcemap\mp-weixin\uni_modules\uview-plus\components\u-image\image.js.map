{"version": 3, "file": "image.js", "sources": ["uni_modules/uview-plus/components/u-image/image.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:01:51\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/image.js\r\n */\r\nexport default {\r\n    // image组件\r\n    image: {\r\n        src: '',\r\n        mode: 'aspectFill',\r\n        width: '300',\r\n        height: '225',\r\n        shape: 'square',\r\n        radius: 0,\r\n        lazyLoad: true,\r\n        showMenuByLongpress: true,\r\n        loadingIcon: 'photo',\r\n        errorIcon: 'error-circle',\r\n        showLoading: true,\r\n        showError: true,\r\n        fade: true,\r\n        webp: false,\r\n        duration: 500,\r\n        bgColor: '#f3f4f6'\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACZ;AACL;;"}