{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-text/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 主题颜色\r\n        type: {\r\n            type: String,\r\n            default: () => defProps.text.type\r\n        },\r\n        // 是否显示\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.text.show\r\n        },\r\n        // 显示的值\r\n        text: {\r\n            type: [String, Number],\r\n            default: () => defProps.text.text\r\n        },\r\n        // 前置图标\r\n        prefixIcon: {\r\n            type: String,\r\n            default: () => defProps.text.prefixIcon\r\n        },\r\n        // 后置图标\r\n        suffixIcon: {\r\n            type: String,\r\n            default: () => defProps.text.suffixIcon\r\n        },\r\n        // 文本处理的匹配模式\r\n        // text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.text.mode\r\n        },\r\n        // mode=link下，配置的链接\r\n        href: {\r\n            type: String,\r\n            default: () => defProps.text.href\r\n        },\r\n        // 格式化规则\r\n        format: {\r\n            type: [String, Function],\r\n            default: () => defProps.text.format\r\n        },\r\n        // mode=phone时，点击文本是否拨打电话\r\n        call: {\r\n            type: Boolean,\r\n            default: () => defProps.text.call\r\n        },\r\n        // 小程序的打开方式\r\n        openType: {\r\n            type: String,\r\n            default: () => defProps.text.openType\r\n        },\r\n        // 是否粗体，默认normal\r\n        bold: {\r\n            type: Boolean,\r\n            default: () => defProps.text.bold\r\n        },\r\n        // 是否块状\r\n        block: {\r\n            type: Boolean,\r\n            default: () => defProps.text.block\r\n        },\r\n        // 文本显示的行数，如果设置，超出此行数，将会显示省略号\r\n        lines: {\r\n            type: [String, Number],\r\n            default: () => defProps.text.lines\r\n        },\r\n        // 文本颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.text.color\r\n        },\r\n        // 字体大小\r\n        size: {\r\n            type: [String, Number],\r\n            default: () => defProps.text.size\r\n        },\r\n        // 图标的样式\r\n        iconStyle: {\r\n            type: [Object, String],\r\n            default: () => defProps.text.iconStyle\r\n        },\r\n        // 文字装饰，下划线，中划线等，可选值 none|underline|line-through\r\n        decoration: {\r\n            tepe: String,\r\n            default: () => defProps.text.decoration\r\n        },\r\n        // 外边距，对象、字符串，数值形式均可\r\n        margin: {\r\n            type: [Object, String, Number],\r\n            default: () => defProps.text.margin\r\n        },\r\n        // 文本行高\r\n        lineHeight: {\r\n            type: [String, Number],\r\n            default: () => defProps.text.lineHeight\r\n        },\r\n        // 文本对齐方式，可选值left|center|right\r\n        align: {\r\n            type: String,\r\n            default: () => defProps.text.align\r\n        },\r\n        // 文字换行，可选值break-word|normal|anywhere\r\n        wordWrap: {\r\n            type: String,\r\n            default: () => defProps.text.wordWrap\r\n        },\r\n\t\t// 占满剩余空间\r\n\t\tflex1: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.text.flex1\r\n\t\t}\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,QAAQ;AAAA,MACvB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,MAC7B,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAEP,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAC7B;AAAA,EACE;AACL,CAAC;;"}