<view class="event-detail-page data-v-64edfaab"><up-navbar wx:if="{{b}}" class="data-v-64edfaab" bindleftClick="{{a}}" u-i="64edfaab-0" bind:__l="__l" u-p="{{b}}"></up-navbar><view wx:if="{{c}}" class="loading-container data-v-64edfaab"><up-loading-page wx:if="{{d}}" class="data-v-64edfaab" u-i="64edfaab-1" bind:__l="__l" u-p="{{d}}"></up-loading-page></view><scroll-view wx:if="{{e}}" scroll-y class="scroll-content data-v-64edfaab" style="{{'padding-top:' + i}}"><view class="top-image-container data-v-64edfaab"><up-image wx:if="{{f}}" class="data-v-64edfaab" u-i="64edfaab-2" bind:__l="__l" u-p="{{f}}"></up-image></view><event-info-card wx:if="{{g}}" class="data-v-64edfaab" u-i="64edfaab-3" bind:__l="__l" u-p="{{g}}"/><event-detail-content wx:if="{{h}}" class="data-v-64edfaab" u-i="64edfaab-4" bind:__l="__l" u-p="{{h}}"/><view class="bottom-spacer data-v-64edfaab"></view></scroll-view><event-action-bar wx:if="{{j}}" class="data-v-64edfaab" bindshare="{{k}}" bindregister="{{l}}" u-i="64edfaab-5" bind:__l="__l" u-p="{{m}}"/></view>