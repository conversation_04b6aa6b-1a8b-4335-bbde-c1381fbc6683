{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["// 统一的请求封装\r\nimport config from '@/utils/config.js';\r\n\r\nconst baseURL = config.baseUrl + '/api/v1';\r\n\r\n// 请求拦截器\r\nconst request = (options) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// 添加token到请求头\r\n\t\tconst token = uni.getStorageSync('token');\r\n\t\tconst header = {\r\n\t\t\t'Content-Type': 'application/json',\r\n\t\t\t...options.header\r\n\t\t};\r\n\r\n\t\tif (token) {\r\n\t\t\theader['Authorization'] = 'Bearer ' + token;\r\n\t\t}\r\n\r\n\t\t// 处理GET请求的查询参数\r\n\t\tlet url = baseURL + options.url;\r\n\t\tlet data = options.data || {};\r\n\r\n\t\tif (options.method === 'GET' && options.params) {\r\n\t\t\t// 将params转换为查询字符串\r\n\t\t\tconst queryString = Object.keys(options.params)\r\n\t\t\t\t.filter(key => options.params[key] !== undefined && options.params[key] !== null)\r\n\t\t\t\t.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`)\r\n\t\t\t\t.join('&');\r\n\r\n\t\t\tif (queryString) {\r\n\t\t\t\turl += (url.includes('?') ? '&' : '?') + queryString;\r\n\t\t\t}\r\n\t\t\tdata = {}; // GET请求不需要body数据\r\n\t\t}\r\n\r\n\t\t// 发起请求\r\n\t\tuni.request({\r\n\t\t\turl,\r\n\t\t\tmethod: options.method || 'GET',\r\n\t\t\tdata,\r\n\t\t\theader,\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tconsole.log('API请求成功:', options.url, res);\r\n\r\n\t\t\t\t// 统一处理响应\r\n\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\tif (res.data.code === 200) {\r\n\t\t\t\t\t\tresolve(res.data);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 新增：禁用/注销统一处理\r\n\t\t\t\t\t\tif (res.data.code === 403) {\r\n\t\t\t\t\t\t\tuni.showToast({ title: res.data.msg || '账号已被禁用', icon: 'none' });\r\n\t\t\t\t\t\t\ttry { uni.removeStorageSync('token'); } catch (e) {}\r\n\t\t\t\t\t\t\ttry { uni.removeStorageSync('userInfo'); } catch (e) {}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 业务错误\r\n\t\t\t\t\t\tconsole.error('业务错误:', res.data);\r\n\t\t\t\t\t\treject(new Error(res.data.msg || '请求失败'));\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// HTTP错误\r\n\t\t\t\t\tconsole.error('HTTP错误:', res);\r\n\t\t\t\t\treject(new Error('网络请求失败'));\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tconsole.error('请求失败:', options.url, err);\r\n\t\t\t\treject(new Error('网络连接失败'));\r\n\t\t\t}\r\n\t\t});\r\n\t});\r\n};\r\n\r\n// GET请求\r\nexport const get = (url, params = {}) => {\r\n\treturn request({\r\n\t\turl,\r\n\t\tmethod: 'GET',\r\n\t\tparams: params\r\n\t});\r\n};\r\n\r\n// POST请求\r\nexport const post = (url, data = {}) => {\r\n\treturn request({\r\n\t\turl,\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t});\r\n};\r\n\r\n// PUT请求\r\nexport const put = (url, data = {}) => {\r\n\treturn request({\r\n\t\turl,\r\n\t\tmethod: 'PUT',\r\n\t\tdata\r\n\t});\r\n};\r\n\r\n// DELETE请求\r\nexport const del = (url, data = {}) => {\r\n\treturn request({\r\n\t\turl,\r\n\t\tmethod: 'DELETE',\r\n\t\tdata\r\n\t});\r\n};\r\n\r\n// 创建包含所有HTTP方法的对象\r\nconst http = {\r\n\tget,\r\n\tpost,\r\n\tput,\r\n\tdel,\r\n\trequest\r\n};\r\n\r\nexport default http;"], "names": ["config", "uni"], "mappings": ";;;AAGA,MAAM,UAAUA,aAAAA,OAAO,UAAU;AAGjC,MAAM,UAAU,CAAC,YAAY;AAC5B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,UAAM,QAAQC,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAM,SAAS;AAAA,MACd,gBAAgB;AAAA,MAChB,GAAG,QAAQ;AAAA,IACd;AAEE,QAAI,OAAO;AACV,aAAO,eAAe,IAAI,YAAY;AAAA,IACtC;AAGD,QAAI,MAAM,UAAU,QAAQ;AAC5B,QAAI,OAAO,QAAQ,QAAQ;AAE3B,QAAI,QAAQ,WAAW,SAAS,QAAQ,QAAQ;AAE/C,YAAM,cAAc,OAAO,KAAK,QAAQ,MAAM,EAC5C,OAAO,SAAO,QAAQ,OAAO,GAAG,MAAM,UAAa,QAAQ,OAAO,GAAG,MAAM,IAAI,EAC/E,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,QAAQ,OAAO,GAAG,CAAC,CAAC,EAAE,EAClF,KAAK,GAAG;AAEV,UAAI,aAAa;AAChB,gBAAQ,IAAI,SAAS,GAAG,IAAI,MAAM,OAAO;AAAA,MACzC;AACD,aAAO,CAAA;AAAA,IACP;AAGDA,kBAAAA,MAAI,QAAQ;AAAA,MACX;AAAA,MACA,QAAQ,QAAQ,UAAU;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,SAAS,CAAC,QAAQ;AACjBA,4BAAA,MAAA,OAAA,0BAAY,YAAY,QAAQ,KAAK,GAAG;AAGxC,YAAI,IAAI,eAAe,KAAK;AAC3B,cAAI,IAAI,KAAK,SAAS,KAAK;AAC1B,oBAAQ,IAAI,IAAI;AAAA,UACtB,OAAY;AAEN,gBAAI,IAAI,KAAK,SAAS,KAAK;AAC1BA,4BAAAA,MAAI,UAAU,EAAE,OAAO,IAAI,KAAK,OAAO,UAAU,MAAM,OAAM,CAAE;AAC/D,kBAAI;AAAEA,8BAAG,MAAC,kBAAkB,OAAO;AAAA,cAAI,SAAQ,GAAG;AAAA,cAAE;AACpD,kBAAI;AAAEA,8BAAG,MAAC,kBAAkB,UAAU;AAAA,cAAI,SAAQ,GAAG;AAAA,cAAE;AAAA,YACvD;AAEDA,yEAAc,SAAS,IAAI,IAAI;AAC/B,mBAAO,IAAI,MAAM,IAAI,KAAK,OAAO,MAAM,CAAC;AAAA,UACxC;AAAA,QACN,OAAW;AAENA,uEAAc,WAAW,GAAG;AAC5B,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC1B;AAAA,MACD;AAAA,MACD,MAAM,CAAC,QAAQ;AACdA,4BAAA,MAAA,SAAA,0BAAc,SAAS,QAAQ,KAAK,GAAG;AACvC,eAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,MAC1B;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;AAGY,MAAC,MAAM,CAAC,KAAK,SAAS,OAAO;AACxC,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,OAAO,CAAC,KAAK,OAAO,OAAO;AACvC,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGO,MAAM,MAAM,CAAC,KAAK,OAAO,OAAO;AACtC,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,MAAM,CAAC,KAAK,OAAO,OAAO;AACtC,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGK,MAAC,OAAO;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;;;"}