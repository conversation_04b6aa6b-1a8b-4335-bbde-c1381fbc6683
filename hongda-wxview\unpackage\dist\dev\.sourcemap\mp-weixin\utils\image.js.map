{"version": 3, "file": "image.js", "sources": ["utils/image.js"], "sourcesContent": ["/**\r\n * 图片URL处理工具\r\n * @param {string | null | undefined} url 从API获取的图片URL\r\n * @returns {string} 一个可供<image>组件src属性使用的有效URL\r\n */\r\nexport function getFullImageUrl(url) {\r\n\t// 如果传入的url有效（非空、非undefined），则直接返回它。\r\n\t// 后端返回的签名URL已经是完整的，我们不需要做任何额外处理。\r\n\tif (url) {\r\n\t\treturn url;\r\n\t}\r\n\r\n\t// 如果传入的url为空或无效，则返回空字符串（不再使用本地兜底图片）。\r\n\treturn '';\r\n}"], "names": [], "mappings": ";AAKO,SAAS,gBAAgB,KAAK;AAGpC,MAAI,KAAK;AACR,WAAO;AAAA,EACP;AAGD,SAAO;AACR;;"}