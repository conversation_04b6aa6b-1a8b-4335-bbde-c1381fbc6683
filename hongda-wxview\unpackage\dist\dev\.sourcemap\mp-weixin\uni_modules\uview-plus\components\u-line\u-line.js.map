{"version": 3, "file": "u-line.js", "sources": ["uni_modules/uview-plus/components/u-line/u-line.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxpbmUvdS1saW5lLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t    class=\"u-line\"\r\n\t    :style=\"[lineStyle]\"\r\n\t>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\r\n\t/**\r\n\t * line 线条\r\n\t * @description 此组件一般用于显示一根线条，用于分隔内容块，有横向和竖向两种模式，且能设置0.5px线条，使用也很简单\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/line.html\r\n\t * @property {String}\t\t\tcolor\t\t线条的颜色 ( 默认 '#d6d7d9' )\r\n\t * @property {String | Number}\tlength\t\t长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带px单位的值等 ( 默认 '100%' )\r\n\t * @property {String}\t\t\tdirection\t线条的方向，row-横向，col-竖向 (默认 'row' )\r\n\t * @property {Boolean}\t\t\thairline\t是否显示细线条 (默认 true )\r\n\t * @property {String | Number}\tmargin\t\t线条与上下左右元素的间距，字符串形式，如\"30px\"  (默认 0 )\r\n\t * @property {Boolean}\t\t\tdashed\t\t是否虚线，true-虚线，false-实线 (默认 false )\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @example <u-line color=\"red\"></u-line>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-line',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tcomputed: {\r\n\t\t\tlineStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tstyle.margin = this.margin\r\n\t\t\t\t// 如果是水平线条，边框高度为1px，再通过transform缩小一半，就是0.5px了\r\n\t\t\t\tif (this.direction === 'row') {\r\n\t\t\t\t\t// 此处采用兼容分开写，兼容nvue的写法\r\n\t\t\t\t\tstyle.borderBottomWidth = '1px'\r\n\t\t\t\t\tstyle.borderBottomStyle = this.dashed ? 'dashed' : 'solid'\r\n\t\t\t\t\tstyle.width = addUnit(this.length)\r\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleY(0.5)'\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是竖向线条，边框宽度为1px，再通过transform缩小一半，就是0.5px了\r\n\t\t\t\t\tstyle.borderLeftWidth = '1px'\r\n\t\t\t\t\tstyle.borderLeftStyle = this.dashed ? 'dashed' : 'solid'\r\n\t\t\t\t\tstyle.height = addUnit(this.length)\r\n\t\t\t\t\tif (this.hairline) style.transform = 'scaleX(0.5)'\r\n\t\t\t\t}\r\n\r\n\t\t\t\tstyle.borderColor = this.color\r\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-line {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tvertical-align: middle;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-line/u-line.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AA2BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,YAAY;AACX,YAAM,QAAQ,CAAC;AACf,YAAM,SAAS,KAAK;AAEpB,UAAI,KAAK,cAAc,OAAO;AAE7B,cAAM,oBAAoB;AAC1B,cAAM,oBAAoB,KAAK,SAAS,WAAW;AACnD,cAAM,QAAQC,kDAAQ,KAAK,MAAM;AACjC,YAAI,KAAK;AAAU,gBAAM,YAAY;AAAA,aAC/B;AAEN,cAAM,kBAAkB;AACxB,cAAM,kBAAkB,KAAK,SAAS,WAAW;AACjD,cAAM,SAASA,kDAAQ,KAAK,MAAM;AAClC,YAAI,KAAK;AAAU,gBAAM,YAAY;AAAA,MACtC;AAEA,YAAM,cAAc,KAAK;AACzB,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACD;AACD;;;;;;;ACpDD,GAAG,gBAAgB,SAAS;"}