{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-status-bar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.statusBar.bgColor\r\n        },\r\n\t\t// 状态栏获取得高度\r\n\t\theight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: () => defProps.statusBar.height\r\n\t\t}\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAEP,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,UAAU;AAAA,IAClC;AAAA,EACE;AACL,CAAC;;"}