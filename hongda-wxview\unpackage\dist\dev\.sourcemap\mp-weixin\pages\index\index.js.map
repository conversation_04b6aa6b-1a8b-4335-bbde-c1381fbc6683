{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <scroll-view class=\"main-scroll-view\" scroll-y>\n      <HeaderComponent />\n\n      <view>\n        <BannerComponent />\n      </view>\n\n      <view>\n        <QuickNavigationComponent/>\n      </view>\n\n      <view>\n        <CountryHighlightComponent/>\n      </view>\n\n      <view>\n        <EventPromotionComponent/>\n      </view>\n\n      <view>\n        <NewsListComponent/>\n      </view>\n\n      <view>\n        <ActivityGridComponent/>\n      </view>\n\n      <view class=\"no-more-divider\">\n        <view class=\"no-more-line\"></view>\n        <text class=\"no-more-text\">没有更多了</text>\n        <view class=\"no-more-line\"></view>\n      </view>\n\n    </scroll-view>\n\n    <CustomTabBar :current=\"0\"/>\n\n    <!-- 动态绑定客服图标 -->\n    <view class=\"fab-customer-service\" @click=\"navigateToService\">\n      <image class=\"fab-icon\" :src=\"fabIconUrl\" mode=\"aspectFit\"></image>\n    </view>\n\n    <!-- 弹窗广告组件 -->\n    <PopupAdComponent v-if=\"showPopupAd\" :ad-data=\"popupAdData\" @close=\"showPopupAd = false\" />\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onShow } from '@dcloudio/uni-app';\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\nimport HeaderComponent from \"@/components/home/<USER>\";\nimport BannerComponent from '@/components/home/<USER>';\nimport QuickNavigationComponent from '@/components/home/<USER>';\nimport CountryHighlightComponent from '@/components/home/<USER>';\nimport NewsListComponent from \"@/components/home/<USER>\";\nimport ActivityGridComponent from '@/components/home/<USER>';\nimport EventPromotionComponent from '@/components/home/<USER>';\nimport PopupAdComponent from '@/components/common/PopupAdComponent.vue';\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\n\n// --- 状态定义 ---\n\n// 定义客服图标的URL（仅暗号读取）\nconst fabIconUrl = ref('');\n\n// 弹窗广告相关\nconst showPopupAd = ref(false);\nconst popupAdData = ref(null);\nconst AD_POSITION_CODE = 'splash_screen';\n\n// --- 方法定义 ---\n\nconst checkAndShowPopupAd = async () => {\n  try {\n    const response = await getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 1 });\n    if (response && response.data && response.data.length > 0) {\n      popupAdData.value = response.data[0];\n      showPopupAd.value = true;\n    }\n  } catch (error) {\n    console.error('获取弹窗广告失败:', error.message || error);\n  }\n};\n\nconst navigateToService = () => {\n  uni.navigateTo({\n    url: '/pages_sub/pages_profile/contact'\n  });\n};\n\n// --- 生命周期钩子 ---\n\nonShow(() => {\n  // 隐藏原生tabbar\n  uni.hideTabBar();\n\n  // 检查并显示弹窗广告\n  checkAndShowPopupAd();\n\n  // 每次页面显示时，从全局缓存读取正确的静态资源数据\n  const assets = uni.getStorageSync('staticAssets');\n  fabIconUrl.value = assets?.fab_customer_service_icon || '';\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #FFFFFF;\n  overflow: hidden;\n}\n\n.main-scroll-view {\n  flex: 1;\n  height: 0;\n  position: relative;\n  z-index: 0;\n  transform: translateZ(0);\n  padding-top: 176rpx;\n  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));\n  box-sizing: border-box;\n\n\n  :deep(.u-loadmore) {\n    display: none !important;\n  }\n}\n\n.fab-customer-service {\n  position: fixed;\n  right: 20rpx;\n  bottom: calc(200rpx + env(safe-area-inset-bottom));\n  width: 100rpx;\n  height: 100rpx;\n  background-color: #FFFFFF;\n  border-radius: 50%;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n  transition: opacity 0.3s;\n}\n\n.fab-customer-service:active {\n  opacity: 0.7;\n}\n\n.fab-icon {\n  width: 60rpx;\n  height: 60rpx;\n}\n\n.no-more-divider {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 24rpx 0;\n}\n\n.no-more-text {\n  width: 120rpx;\n  height: 34rpx;\n  line-height: 34rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n  font-weight: normal;\n  font-size: 24rpx;\n  color: #9B9A9A;\n  text-align: center;\n  font-style: normal;\n  text-transform: none;\n}\n\n.no-more-line {\n  width: 40rpx;\n  height: 2rpx;\n  background: #CBCBCB;\n  border-radius: 0rpx;\n  margin: 0 12rpx;\n  flex-shrink: 0;\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "getAdListByPositionApi", "uni", "onShow"], "mappings": ";;;;;;AAqDA,MAAM,kBAAkB,MAAW;AACnC,MAAM,kBAAkB,MAAW;AACnC,MAAM,2BAA2B,MAAW;AAC5C,MAAM,4BAA4B,MAAW;AAC7C,MAAM,oBAAoB,MAAW;AACrC,MAAM,wBAAwB,MAAW;AACzC,MAAM,0BAA0B,MAAW;AAC3C,MAAM,mBAAmB,MAAW;AACpC,MAAM,eAAe,MAAW;AAUhC,MAAM,mBAAmB;;;;AALzB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAK5B,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,WAAW,MAAMC,uCAAuB,kBAAkB,EAAE,UAAU,EAAC,CAAE;AAC/E,YAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzD,sBAAY,QAAQ,SAAS,KAAK,CAAC;AACnC,sBAAY,QAAQ;AAAA,QACrB;AAAA,MACF,SAAQ,OAAO;AACdC,0EAAc,aAAa,MAAM,WAAW,KAAK;AAAA,MAClD;AAAA,IACH;AAEA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAIAC,kBAAAA,OAAO,MAAM;AAEXD,oBAAG,MAAC,WAAU;AAGd;AAGA,YAAM,SAASA,cAAAA,MAAI,eAAe,cAAc;AAChD,iBAAW,SAAQ,iCAAQ,8BAA6B;AAAA,IAC1D,CAAC;;;;;;;;;;;;;;;;;;;ACxGD,GAAG,WAAW,eAAe;"}