{"version": 3, "file": "search.js", "sources": ["pages_sub/pages_other/search.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXHNlYXJjaC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <view class=\"custom-header\">\r\n      <view class=\"status-bar\"></view>\r\n      <view class=\"nav-bar\" :style=\"navBarStyle\">\r\n        <u-icon name=\"arrow-left\" size=\"22\" color=\"#303133\" @click=\"navigateBack\"></u-icon>\r\n        <view class=\"page-title-container\">\r\n          <text class=\"page-title\">红大“走出去”</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"search-section\">\r\n        <view class=\"search-input-wrapper\">\r\n          <u-icon name=\"search\" color=\"#909399\" size=\"18\"></u-icon>\r\n          <input\r\n              class=\"search-input\"\r\n              v-model=\"keyword\"\r\n              placeholder=\"搜索活动、资讯\"\r\n              confirm-type=\"search\"\r\n              @confirm=\"handleSearch\"\r\n          />\r\n          <u-icon v-if=\"keyword\" @click=\"handleCancel\" name=\"close-circle-fill\" color=\"#c8c9cc\" size=\"18\"></u-icon>\r\n        </view>\r\n        <text v-if=\"keyword\" class=\"cancel-btn\" @click=\"handleCancel\">取消</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"tabs-container\">\r\n      <view class=\"custom-tabs\">\r\n        <view\r\n            v-for=\"(tab, index) in tabList\"\r\n            :key=\"index\"\r\n            class=\"tab-item\"\r\n            :class=\"{ 'active': currentTab === index }\"\r\n            @click=\"selectTab(index)\"\r\n            :style=\"currentTab === index ? activeTabStyle : {}\"\r\n        >\r\n          {{ tab.name }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"result-list-scroll\"  @scrolltolower=\"loadMoreData\">\r\n      <view v-if=\"isLoading\" class=\"loading-container\">\r\n        <u-loading-icon mode=\"circle\" text=\"正在搜索...\" size=\"24\"></u-loading-icon>\r\n      </view>\r\n      <view v-else>\r\n        <view v-if=\"displayedList.length === 0\" class=\"empty-state-container\">\r\n          <u-empty v-if=\"searchStatus === 'pristine'\" mode=\"search\" text=\"请输入关键词开始搜索\"></u-empty>\r\n          <u-empty v-else mode=\"data\" text=\"暂无相关结果\" marginTop=\"100\"></u-empty>\r\n        </view>\r\n        <view v-else class=\"result-list\">\r\n          <view\r\n              v-for=\"item in displayedList\"\r\n              :key=\"item.id\"\r\n              class=\"result-card\"\r\n              @click=\"goToDetail(item.id, currentTab)\">\r\n            <view class=\"card-cover\">\r\n              <image class=\"cover-image\" :src=\"item.coverUrl\" mode=\"aspectFill\"></image>\r\n              <view v-if=\"item.statusText\" class=\"status-badge\" :class=\"item.statusClass\">{{ item.statusText }}</view>\r\n            </view>\r\n            <view class=\"card-content\">\r\n              <text class=\"card-title\">{{ item.title }}</text>\r\n              <view class=\"card-meta\">\r\n                <u-icon name=\"calendar\" color=\"#909399\" size=\"14\"></u-icon>\r\n                <text class=\"meta-text\">{{ item.date }}</text>\r\n              </view>\r\n              <view class=\"card-meta\">\r\n                <u-icon name=\"map\" color=\"#909399\" size=\"14\"></u-icon>\r\n                <text class=\"meta-text\">{{ item.location }}</text>\r\n              </view>\r\n              <view class=\"card-meta\">\r\n                <u-icon name=\"account\" color=\"#909399\" size=\"14\"></u-icon>\r\n                <text class=\"meta-text\">{{ item.slotsPrefix }}: {{ item.slots }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <u-loadmore :status=\"loadStatus\" :line=\"true\" marginTop=\"20\"/>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {computed, onMounted, ref} from 'vue';\r\nimport {getArticleList} from '@/api/content/article.js';\r\nimport {searchEventsApi} from '@/api/data/event.js';\r\nimport {getFullImageUrl} from '@/utils/image.js';\r\n\r\nconst navBarStyle = ref({});\r\nconst keyword = ref('');\r\nconst currentTab = ref(0);\r\nconst isLoading = ref(false);\r\nconst searchStatus = ref('pristine');\r\nconst tabList = ref([{name: '活动'}, {name: '资讯'}]);\r\nconst activityResults = ref([]);\r\nconst newsResults = ref([]);\r\nconst pageNum = ref(1);\r\nconst pageSize = ref(10);\r\nconst loadStatus = ref('loadmore');\r\nconst hasMoreData = ref(true);\r\nconst assets = ref({}); // 【新增】用于存储静态资源\r\n\r\n// 【新增】动态计算激活Tab的背景样式\r\nconst activeTabStyle = computed(() => {\r\n  // 优先使用后台配置的URL，如果不存在，则使用旧的地址作为备用\r\n  const imageUrl = assets.value.bg_tab_active_search || 'http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png';\r\n  return {\r\n    backgroundImage: `url('${imageUrl}')`\r\n  };\r\n});\r\n\r\nonMounted(() => {\r\n  // 【新增】页面加载时，从全局缓存读取静态资源\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n\r\n  // #ifdef MP-WEIXIN\r\n  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  const navHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;\r\n  const navPaddingRight = systemInfo.windowWidth - menuButtonInfo.left;\r\n  navBarStyle.value = {\r\n    height: `${navHeight}px`,\r\n    'padding-right': `${navPaddingRight}px`,\r\n    'align-items': 'center'\r\n  };\r\n  // #endif\r\n});\r\n\r\nconst displayedList = computed(() => {\r\n  return currentTab.value === 0 ? activityResults.value : newsResults.value;\r\n});\r\n\r\nconst formatEventData = (event) => ({\r\n  id: event.id,\r\n  title: event.title,\r\n  date: event.startTime.split(' ')[0],\r\n  location: event.location,\r\n  slots: (event.maxParticipants || 0) - (event.registeredCount || 0),\r\n  coverUrl: getFullImageUrl(event.coverImageUrl),\r\n  statusText: '报名中',\r\n  statusClass: 'registering',\r\n  slotsPrefix: '剩余名额'\r\n});\r\n\r\nconst formatArticleData = (article) => ({\r\n  id: article.id, title: article.title, date: article.publishTime.split(' ')[0],\r\n  location: article.source || '未知来源', slots: article.viewCount, coverUrl: getFullImageUrl(article.coverImageUrl),\r\n  statusText: '热门', statusClass: 'hot', slotsPrefix: '阅读量'\r\n});\r\n\r\nconst handleSearch = async () => {\r\n  if (!keyword.value.trim()) {\r\n    uni.showToast({ title: '请输入搜索内容', icon: 'none' });\r\n    return;\r\n  }\r\n\r\n  pageNum.value = 1;\r\n  activityResults.value = [];\r\n  newsResults.value = [];\r\n  hasMoreData.value = true;\r\n  loadStatus.value = 'loading';\r\n  isLoading.value = true;\r\n  searchStatus.value = 'searched';\r\n\r\n  try {\r\n    const [eventRes, articleRes] = await Promise.all([\r\n      searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value }),\r\n      getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value })\r\n    ]);\r\n\r\n    if (eventRes.code === 200 && eventRes.rows) {\r\n      activityResults.value = eventRes.rows.map(formatEventData);\r\n      hasMoreData.value = eventRes.rows.length >= pageSize.value;\r\n      loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n    } else {\r\n      activityResults.value = [];\r\n      hasMoreData.value = false;\r\n      loadStatus.value = 'nomore';\r\n    }\r\n\r\n    if (articleRes.code === 200 && articleRes.rows) {\r\n      newsResults.value = articleRes.rows.map(formatArticleData);\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('搜索失败:', error);\r\n    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });\r\n    loadStatus.value = 'loadmore';\r\n  } finally {\r\n    isLoading.value = false;\r\n  }\r\n};\r\n\r\nconst loadMoreData = async () => {\r\n  if (loadStatus.value !== 'loadmore' || !hasMoreData.value) {\r\n    return;\r\n  }\r\n\r\n  loadStatus.value = 'loading';\r\n  pageNum.value++;\r\n\r\n  try {\r\n    if (currentTab.value === 0) {\r\n      const res = await searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });\r\n      if (res.code === 200 && res.rows) {\r\n        activityResults.value.push(...res.rows.map(formatEventData));\r\n        hasMoreData.value = res.rows.length >= pageSize.value;\r\n        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n      }\r\n    } else {\r\n      const res = await getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });\r\n      if (res.code === 200 && res.rows) {\r\n        newsResults.value.push(...res.rows.map(formatArticleData));\r\n        hasMoreData.value = res.rows.length >= pageSize.value;\r\n        loadStatus.value = hasMoreData.value ? 'loadmore' : 'nomore';\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('加载更多失败:', error);\r\n    loadStatus.value = 'loadmore';\r\n  }\r\n};\r\n\r\nconst handleCancel = () => {\r\n  keyword.value = '';\r\n  activityResults.value = [];\r\n  newsResults.value = [];\r\n  searchStatus.value = 'pristine';\r\n};\r\n\r\nconst selectTab = (index) => {\r\n  currentTab.value = index;\r\n  // 切换tab时，重置分页和加载状态\r\n  pageNum.value = 1;\r\n  hasMoreData.value = true;\r\n  if (displayedList.value.length < pageSize.value) {\r\n    loadStatus.value = 'nomore';\r\n    hasMoreData.value = false;\r\n  } else {\r\n    loadStatus.value = 'loadmore';\r\n  }\r\n};\r\n\r\nconst navigateBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst goToDetail = (id, tabIndex) => {\r\n  const url = tabIndex === 0\r\n      ? `/pages_sub/pages_event/detail?id=${id}`\r\n      : `/pages_sub/pages_article/detail?id=${id}`;\r\n  uni.navigateTo({ url });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.custom-header {\r\n  padding: 0 32rpx 24rpx;\r\n  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n  height: 378rpx;\r\n  justify-content: space-around;\r\n}\r\n\r\n.status-bar {\r\n  height: var(--status-bar-height);\r\n}\r\n\r\n.nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n.page-title-container {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.page-title {\r\n  font-size: 34rpx;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.search-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n}\r\n\r\n.search-input-wrapper {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  height: 72rpx;\r\n  padding: 0 24rpx;\r\n  background-color: #ffffff;\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 36rpx;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n}\r\n\r\n.cancel-btn {\r\n  padding: 14rpx 30rpx;\r\n  border-radius: 36rpx;\r\n  color: #303133;\r\n  font-size: 28rpx;\r\n  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);\r\n  transition: all 0.2s ease-in-out;\r\n}\r\n\r\n.tabs-container {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  padding: 20rpx 32rpx;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.custom-tabs {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.tab-item {\r\n  padding: 16rpx 40rpx;\r\n  border-radius: 40rpx;\r\n  color: #23232A;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  background: linear-gradient(to bottom, #C3D3FF, #FFFFFF);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.tab-item.active {\r\n  /* 【关键修改】移除 background-image */\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n.result-list-scroll {\r\n  flex: 1;\r\n  height: 0;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.result-list {\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.loading-container, .empty-state-container {\r\n  padding-top: 200rpx;\r\n}\r\n\r\n.result-card {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  background-color: #ffffff;\r\n  padding: 32rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.card-cover {\r\n  position: relative;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  flex-shrink: 0;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.status-badge {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  padding: 4rpx 12rpx;\r\n  font-size: 20rpx;\r\n  color: #ffffff;\r\n  border-bottom-right-radius: 12rpx;\r\n\r\n  &.registering {\r\n    background: linear-gradient(135deg, #fa3534, #f56c6c);\r\n  }\r\n\r\n  &.hot {\r\n    background: linear-gradient(135deg, #19be6b, #40d289);\r\n  }\r\n}\r\n\r\n.card-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-around;\r\n}\r\n\r\n.card-title {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.card-meta {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10rpx;\r\n  font-size: 24rpx;\r\n  color: #606266;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/search.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "getFullImageUrl", "searchEventsApi", "getArticleList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0FA,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,EAAE;AACtB,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAAA,IAAI,UAAU;AACnC,UAAM,UAAUA,cAAAA,IAAI,CAAC,EAAC,MAAM,KAAI,GAAG,EAAC,MAAM,KAAI,CAAC,CAAC;AAChD,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,CAAC;AACrB,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,aAAaA,cAAAA,IAAI,UAAU;AACjC,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAC5B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AAGrB,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AAEpC,YAAM,WAAW,OAAO,MAAM,wBAAwB;AACtD,aAAO;AAAA,QACL,iBAAiB,QAAQ,QAAQ;AAAA,MACrC;AAAA,IACA,CAAC;AAEDC,kBAAAA,UAAU,MAAM;AAEd,aAAO,QAAQC,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA;AAGrD,YAAM,iBAAiBA,oBAAI;AAC3B,YAAM,aAAaA,oBAAI;AACvB,YAAM,YAAY,eAAe,UAAU,eAAe,MAAM,WAAW,mBAAmB;AAC9F,YAAM,kBAAkB,WAAW,cAAc,eAAe;AAChE,kBAAY,QAAQ;AAAA,QAClB,QAAQ,GAAG,SAAS;AAAA,QACpB,iBAAiB,GAAG,eAAe;AAAA,QACnC,eAAe;AAAA,MACnB;AAAA,IAEA,CAAC;AAED,UAAM,gBAAgBF,cAAQ,SAAC,MAAM;AACnC,aAAO,WAAW,UAAU,IAAI,gBAAgB,QAAQ,YAAY;AAAA,IACtE,CAAC;AAED,UAAM,kBAAkB,CAAC,WAAW;AAAA,MAClC,IAAI,MAAM;AAAA,MACV,OAAO,MAAM;AAAA,MACb,MAAM,MAAM,UAAU,MAAM,GAAG,EAAE,CAAC;AAAA,MAClC,UAAU,MAAM;AAAA,MAChB,QAAQ,MAAM,mBAAmB,MAAM,MAAM,mBAAmB;AAAA,MAChE,UAAUG,YAAAA,gBAAgB,MAAM,aAAa;AAAA,MAC7C,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAEA,UAAM,oBAAoB,CAAC,aAAa;AAAA,MACtC,IAAI,QAAQ;AAAA,MAAI,OAAO,QAAQ;AAAA,MAAO,MAAM,QAAQ,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,MAC5E,UAAU,QAAQ,UAAU;AAAA,MAAQ,OAAO,QAAQ;AAAA,MAAW,UAAUA,4BAAgB,QAAQ,aAAa;AAAA,MAC7G,YAAY;AAAA,MAAM,aAAa;AAAA,MAAO,aAAa;AAAA,IACrD;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,QAAQ,MAAM,QAAQ;AACzBD,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAChD;AAAA,MACD;AAED,cAAQ,QAAQ;AAChB,sBAAgB,QAAQ;AACxB,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,iBAAW,QAAQ;AACnB,gBAAU,QAAQ;AAClB,mBAAa,QAAQ;AAErB,UAAI;AACF,cAAM,CAAC,UAAU,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC/CE,eAAAA,gBAAgB,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAK,CAAE;AAAA,UAC1FC,oBAAAA,eAAe,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAK,CAAE;AAAA,QAC/F,CAAK;AAED,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,0BAAgB,QAAQ,SAAS,KAAK,IAAI,eAAe;AACzD,sBAAY,QAAQ,SAAS,KAAK,UAAU,SAAS;AACrD,qBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,QAC1D,OAAW;AACL,0BAAgB,QAAQ;AACxB,sBAAY,QAAQ;AACpB,qBAAW,QAAQ;AAAA,QACpB;AAED,YAAI,WAAW,SAAS,OAAO,WAAW,MAAM;AAC9C,sBAAY,QAAQ,WAAW,KAAK,IAAI,iBAAiB;AAAA,QAC1D;AAAA,MAEF,SAAQ,OAAO;AACdH,sBAAA,MAAA,MAAA,SAAA,2CAAc,SAAS,KAAK;AAC5BA,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAM,CAAE;AACnD,mBAAW,QAAQ;AAAA,MACvB,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI,WAAW,UAAU,cAAc,CAAC,YAAY,OAAO;AACzD;AAAA,MACD;AAED,iBAAW,QAAQ;AACnB,cAAQ;AAER,UAAI;AACF,YAAI,WAAW,UAAU,GAAG;AAC1B,gBAAM,MAAM,MAAME,eAAAA,gBAAgB,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAO,CAAA;AAC5G,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,4BAAgB,MAAM,KAAK,GAAG,IAAI,KAAK,IAAI,eAAe,CAAC;AAC3D,wBAAY,QAAQ,IAAI,KAAK,UAAU,SAAS;AAChD,uBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,UACrD;AAAA,QACP,OAAW;AACL,gBAAM,MAAM,MAAMC,oBAAAA,eAAe,EAAE,SAAS,QAAQ,OAAO,UAAU,SAAS,OAAO,OAAO,QAAQ,MAAO,CAAA;AAC3G,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,wBAAY,MAAM,KAAK,GAAG,IAAI,KAAK,IAAI,iBAAiB,CAAC;AACzD,wBAAY,QAAQ,IAAI,KAAK,UAAU,SAAS;AAChD,uBAAW,QAAQ,YAAY,QAAQ,aAAa;AAAA,UACrD;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdH,sBAAA,MAAA,MAAA,SAAA,2CAAc,WAAW,KAAK;AAC9B,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,cAAQ,QAAQ;AAChB,sBAAgB,QAAQ;AACxB,kBAAY,QAAQ;AACpB,mBAAa,QAAQ;AAAA,IACvB;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAEnB,cAAQ,QAAQ;AAChB,kBAAY,QAAQ;AACpB,UAAI,cAAc,MAAM,SAAS,SAAS,OAAO;AAC/C,mBAAW,QAAQ;AACnB,oBAAY,QAAQ;AAAA,MACxB,OAAS;AACL,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,IAAI,aAAa;AACnC,YAAM,MAAM,aAAa,IACnB,oCAAoC,EAAE,KACtC,sCAAsC,EAAE;AAC9CA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PA,GAAG,WAAW,eAAe;"}