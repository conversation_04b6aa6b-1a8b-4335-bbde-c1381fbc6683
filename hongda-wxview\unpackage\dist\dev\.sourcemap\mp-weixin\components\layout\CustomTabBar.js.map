{"version": 3, "file": "CustomTabBar.js", "sources": ["components/layout/CustomTabBar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvbGF5b3V0L0N1c3RvbVRhYkJhci52dWU"], "sourcesContent": ["<template>\n\t<view class=\"tab-bar-wrapper\">\n\t\t<view class=\"tab-bar\">\n\t\t\t<!-- 遍历所有 tab 项 -->\n\t\t\t<view v-for=\"(item, index) in list\" :key=\"item.pagePath\" class=\"tab-item\" @click=\"switchTab(item, index)\">\n\t\t\t\t<!-- 中央凸起按钮的占位符 -->\n\t\t\t\t<view v-if=\"item.central\" class=\"tab-item-placeholder\"></view>\n\n\t\t\t\t<!-- 普通 tab 项 -->\n\t\t\t\t<view v-else class=\"tab-item-content\">\n\t\t\t\t\t<image :src=\"current === index ? item.selectedIconPath : item.iconPath\" class=\"tab-icon\"></image>\n\t\t\t\t\t<text class=\"tab-text\" :style=\"{ color: current === index ? selectedColor : color }\">{{ item.text }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 单独渲染的、真正的中央凸起按钮 -->\n\t\t\t<view class=\"central-button-wrapper\" @click=\"switchTab(list[2], 2)\">\n\t\t\t\t<view class=\"central-button\">\n\t\t\t\t\t<image class=\"central-icon\" :src=\"eventHotIconUrl\"></image>\n\t\t\t\t\t<view class=\"central-text\">\n\t\t\t\t\t\t<view class=\"central-text-line\">\n\t\t\t\t\t\t\t<text class=\"central-char\">热</text>\n\t\t\t\t\t\t\t<text class=\"central-char\">门</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"central-text-line\">\n\t\t\t\t\t\t\t<text class=\"central-char\">活</text>\n\t\t\t\t\t\t\t<text class=\"central-char\">动</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 适配 iPhone X 等全面屏手机的底部安全区 -->\n\t\t<view class=\"safe-area-bottom\"></view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 接收当前页面的索引，用于高亮显示\nconst props = defineProps({\n\tcurrent: {\n\t\ttype: Number,\n\t\trequired: true\n\t}\n});\n\nconst color = ref('#8A8A8A');\nconst selectedColor = ref('#023F98');\nconst list = ref([\n  {\n    pagePath: '/pages/index/index',\n    iconPath: '',\n    selectedIconPath: '',\n    text: '推荐'\n  },\n  {\n    pagePath: '/pages/article/index', \n    iconPath: '',\n    selectedIconPath: '',\n    text: '资讯'\n  },\n  {\n    pagePath: '/pages/event/index', \n    iconPath: '',\n    selectedIconPath: '',\n    text: '热门活动',\n    central: true \n  },\n  {\n    pagePath: '/pages/country/index', \n    iconPath: '',\n    selectedIconPath: '',\n    text: '国别资讯'\n  },\n  {\n    pagePath: '/pages/profile/index',\n    iconPath: '',\n    selectedIconPath: '',\n    text: '我的'\n  }\n]);\n\n// 中央“热门活动”按钮图标（仅暗号读取）\nconst eventHotIconUrl = ref('')\n\nonMounted(() => {\n\ttry {\n\t\tconst assets = uni.getStorageSync('staticAssets')\n    if (assets && assets['event_hot']) eventHotIconUrl.value = assets['event_hot']\n\n    // 动态替换普通 tab 的图标（仅暗号读取）\n    const mapping = {\n        0: { icon: 'recommend', active: 'recommend_active' },\n        1: { icon: 'article', active: 'article_active' },\n        3: { icon: 'country', active: 'country_active' },\n        4: { icon: 'profile', active: 'profile_active' }\n    }\n\n    list.value = list.value.map((item, idx) => {\n        if (item.central) return item\n        const m = mapping[idx]\n        if (!m) return item\n        return {\n            ...item,\n            iconPath: assets?.[m.icon] || '',\n            selectedIconPath: assets?.[m.active] || ''\n        }\n    })\n\t} catch (e) {}\n})\n\n// 切换 tab 的方法\nconst switchTab = (item, index) => {\n\tif (props.current === index) {\n\t\treturn; \n\t}\n\tuni.switchTab({\n\t\turl: item.pagePath\n\t});\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.tab-bar-wrapper {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\twidth: 100%;\n\tz-index: 999;\n\tbackground-color: #ffffff;\n\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.tab-bar {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\theight: 144rpx;\n\twidth: 100%;\n\tposition: relative;\n}\n\n.tab-item {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\theight: 100%;\n\tpadding-top: 12rpx;\n\tbox-sizing: border-box;\n}\n\n.tab-item-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 6rpx;\n}\n\n.tab-icon {\n\twidth: 56rpx;\n\theight: 56rpx;\n}\n\n.tab-text {\n\tfont-size: 24rpx; \n\tfont-weight: 500;  \n\tfont-family: 'Alibaba PuHuiTi 3.0';\n}\n\n.central-button-wrapper {\n\tposition: absolute;\n\tleft: 50%;\n\ttop: 10rpx; // 控制凸起的高度\n\ttransform: translateX(-50%);\n\n\n\t.text-line-1 {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block; \n\t}\n\n\t.text-line-2 {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t}\n\n\t.central-icon {\n\t    width: 100rpx;  \n\t    height: 100rpx; \n\t}\n  \n  /*中央按钮上的“热门活动”文字样式 */\n  .central-text {\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    transform: translate(-48%, -53%);\n    width: 100%;\n    max-width: 100rpx;\n    height: auto;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: 800; \n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 2rpx;\n    pointer-events: none;\n  }\n\n  .central-text-line {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 2rpx; \n    height: 34rpx;\n  }\n\n  .central-char {\n    font-size: 28rpx;\n    color: #FFFFFF;\n    line-height: 34rpx;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n  }\n\t\n}\n\n.safe-area-bottom {\n\theight: constant(safe-area-inset-bottom);\n\theight: env(safe-area-inset-bottom);\n\tbackground-color: #ffffff;\n}\n</style>\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/layout/CustomTabBar.vue'\nwx.createComponent(Component)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;AA0CA,UAAM,QAAQ;AAOd,UAAM,QAAQA,cAAAA,IAAI,SAAS;AAC3B,UAAM,gBAAgBA,cAAAA,IAAI,SAAS;AACnC,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf;AAAA,QACE,UAAU;AAAA,QACV,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,UAAU;AAAA,QACV,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,UAAU;AAAA,QACV,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACD;AAAA,QACE,UAAU;AAAA,QACV,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,UAAU;AAAA,QACV,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAE9BC,kBAAAA,UAAU,MAAM;AACf,UAAI;AACH,cAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAC9C,YAAI,UAAU,OAAO,WAAW;AAAG,0BAAgB,QAAQ,OAAO,WAAW;AAG7E,cAAM,UAAU;AAAA,UACZ,GAAG,EAAE,MAAM,aAAa,QAAQ,mBAAoB;AAAA,UACpD,GAAG,EAAE,MAAM,WAAW,QAAQ,iBAAkB;AAAA,UAChD,GAAG,EAAE,MAAM,WAAW,QAAQ,iBAAkB;AAAA,UAChD,GAAG,EAAE,MAAM,WAAW,QAAQ,iBAAkB;AAAA,QACnD;AAED,aAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,MAAM,QAAQ;AACvC,cAAI,KAAK;AAAS,mBAAO;AACzB,gBAAM,IAAI,QAAQ,GAAG;AACrB,cAAI,CAAC;AAAG,mBAAO;AACf,iBAAO;AAAA,YACH,GAAG;AAAA,YACH,WAAU,iCAAS,EAAE,UAAS;AAAA,YAC9B,mBAAkB,iCAAS,EAAE,YAAW;AAAA,UAC3C;AAAA,QACT,CAAK;AAAA,MACL,SAAU,GAAG;AAAA,MAAE;AAAA,IACf,CAAC;AAGD,UAAM,YAAY,CAAC,MAAM,UAAU;AAClC,UAAI,MAAM,YAAY,OAAO;AAC5B;AAAA,MACA;AACDA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK,KAAK;AAAA,MACZ,CAAE;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;ACzHA,GAAG,gBAAgB,SAAS;"}