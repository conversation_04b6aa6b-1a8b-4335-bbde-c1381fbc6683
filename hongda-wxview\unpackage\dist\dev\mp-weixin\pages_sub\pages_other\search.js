"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_article = require("../../api/content/article.js");
const api_data_event = require("../../api/data/event.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  const _easycom_u_loadmore2 = common_vendor.resolveComponent("u-loadmore");
  (_easycom_u_icon2 + _easycom_u_loading_icon2 + _easycom_u_empty2 + _easycom_u_loadmore2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_u_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_u_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_loading_icon + _easycom_u_empty + _easycom_u_loadmore)();
}
const _sfc_main = {
  __name: "search",
  setup(__props) {
    const navBarStyle = common_vendor.ref({});
    const keyword = common_vendor.ref("");
    const currentTab = common_vendor.ref(0);
    const isLoading = common_vendor.ref(false);
    const searchStatus = common_vendor.ref("pristine");
    const tabList = common_vendor.ref([{ name: "活动" }, { name: "资讯" }]);
    const activityResults = common_vendor.ref([]);
    const newsResults = common_vendor.ref([]);
    const pageNum = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const loadStatus = common_vendor.ref("loadmore");
    const hasMoreData = common_vendor.ref(true);
    const assets = common_vendor.ref({});
    const activeTabStyle = common_vendor.computed(() => {
      const imageUrl = assets.value.bg_tab_active_search || "http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png";
      return {
        backgroundImage: `url('${imageUrl}')`
      };
    });
    common_vendor.onMounted(() => {
      assets.value = common_vendor.index.getStorageSync("staticAssets") || {};
      const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const navHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2;
      const navPaddingRight = systemInfo.windowWidth - menuButtonInfo.left;
      navBarStyle.value = {
        height: `${navHeight}px`,
        "padding-right": `${navPaddingRight}px`,
        "align-items": "center"
      };
    });
    const displayedList = common_vendor.computed(() => {
      return currentTab.value === 0 ? activityResults.value : newsResults.value;
    });
    const formatEventData = (event) => ({
      id: event.id,
      title: event.title,
      date: event.startTime.split(" ")[0],
      location: event.location,
      slots: (event.maxParticipants || 0) - (event.registeredCount || 0),
      coverUrl: utils_image.getFullImageUrl(event.coverImageUrl),
      statusText: "报名中",
      statusClass: "registering",
      slotsPrefix: "剩余名额"
    });
    const formatArticleData = (article) => ({
      id: article.id,
      title: article.title,
      date: article.publishTime.split(" ")[0],
      location: article.source || "未知来源",
      slots: article.viewCount,
      coverUrl: utils_image.getFullImageUrl(article.coverImageUrl),
      statusText: "热门",
      statusClass: "hot",
      slotsPrefix: "阅读量"
    });
    const handleSearch = async () => {
      if (!keyword.value.trim()) {
        common_vendor.index.showToast({ title: "请输入搜索内容", icon: "none" });
        return;
      }
      pageNum.value = 1;
      activityResults.value = [];
      newsResults.value = [];
      hasMoreData.value = true;
      loadStatus.value = "loading";
      isLoading.value = true;
      searchStatus.value = "searched";
      try {
        const [eventRes, articleRes] = await Promise.all([
          api_data_event.searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value }),
          api_content_article.getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value })
        ]);
        if (eventRes.code === 200 && eventRes.rows) {
          activityResults.value = eventRes.rows.map(formatEventData);
          hasMoreData.value = eventRes.rows.length >= pageSize.value;
          loadStatus.value = hasMoreData.value ? "loadmore" : "nomore";
        } else {
          activityResults.value = [];
          hasMoreData.value = false;
          loadStatus.value = "nomore";
        }
        if (articleRes.code === 200 && articleRes.rows) {
          newsResults.value = articleRes.rows.map(formatArticleData);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/search.vue:188", "搜索失败:", error);
        common_vendor.index.showToast({ title: "搜索失败，请稍后重试", icon: "none" });
        loadStatus.value = "loadmore";
      } finally {
        isLoading.value = false;
      }
    };
    const loadMoreData = async () => {
      if (loadStatus.value !== "loadmore" || !hasMoreData.value) {
        return;
      }
      loadStatus.value = "loading";
      pageNum.value++;
      try {
        if (currentTab.value === 0) {
          const res = await api_data_event.searchEventsApi({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });
          if (res.code === 200 && res.rows) {
            activityResults.value.push(...res.rows.map(formatEventData));
            hasMoreData.value = res.rows.length >= pageSize.value;
            loadStatus.value = hasMoreData.value ? "loadmore" : "nomore";
          }
        } else {
          const res = await api_content_article.getArticleList({ pageNum: pageNum.value, pageSize: pageSize.value, title: keyword.value });
          if (res.code === 200 && res.rows) {
            newsResults.value.push(...res.rows.map(formatArticleData));
            hasMoreData.value = res.rows.length >= pageSize.value;
            loadStatus.value = hasMoreData.value ? "loadmore" : "nomore";
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/search.vue:221", "加载更多失败:", error);
        loadStatus.value = "loadmore";
      }
    };
    const handleCancel = () => {
      keyword.value = "";
      activityResults.value = [];
      newsResults.value = [];
      searchStatus.value = "pristine";
    };
    const selectTab = (index) => {
      currentTab.value = index;
      pageNum.value = 1;
      hasMoreData.value = true;
      if (displayedList.value.length < pageSize.value) {
        loadStatus.value = "nomore";
        hasMoreData.value = false;
      } else {
        loadStatus.value = "loadmore";
      }
    };
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const goToDetail = (id, tabIndex) => {
      const url = tabIndex === 0 ? `/pages_sub/pages_event/detail?id=${id}` : `/pages_sub/pages_article/detail?id=${id}`;
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(navigateBack),
        b: common_vendor.p({
          name: "arrow-left",
          size: "22",
          color: "#303133"
        }),
        c: common_vendor.s(navBarStyle.value),
        d: common_vendor.p({
          name: "search",
          color: "#909399",
          size: "18"
        }),
        e: common_vendor.o(handleSearch),
        f: keyword.value,
        g: common_vendor.o(($event) => keyword.value = $event.detail.value),
        h: keyword.value
      }, keyword.value ? {
        i: common_vendor.o(handleCancel),
        j: common_vendor.p({
          name: "close-circle-fill",
          color: "#c8c9cc",
          size: "18"
        })
      } : {}, {
        k: keyword.value
      }, keyword.value ? {
        l: common_vendor.o(handleCancel)
      } : {}, {
        m: common_vendor.f(tabList.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectTab(index), index),
            e: common_vendor.s(currentTab.value === index ? activeTabStyle.value : {})
          };
        }),
        n: isLoading.value
      }, isLoading.value ? {
        o: common_vendor.p({
          mode: "circle",
          text: "正在搜索...",
          size: "24"
        })
      } : common_vendor.e({
        p: displayedList.value.length === 0
      }, displayedList.value.length === 0 ? common_vendor.e({
        q: searchStatus.value === "pristine"
      }, searchStatus.value === "pristine" ? {
        r: common_vendor.p({
          mode: "search",
          text: "请输入关键词开始搜索"
        })
      } : {
        s: common_vendor.p({
          mode: "data",
          text: "暂无相关结果",
          marginTop: "100"
        })
      }) : {
        t: common_vendor.f(displayedList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: item.coverUrl,
            b: item.statusText
          }, item.statusText ? {
            c: common_vendor.t(item.statusText),
            d: common_vendor.n(item.statusClass)
          } : {}, {
            e: common_vendor.t(item.title),
            f: "a5d8397f-6-" + i0,
            g: common_vendor.t(item.date),
            h: "a5d8397f-7-" + i0,
            i: common_vendor.t(item.location),
            j: "a5d8397f-8-" + i0,
            k: common_vendor.t(item.slotsPrefix),
            l: common_vendor.t(item.slots),
            m: item.id,
            n: common_vendor.o(($event) => goToDetail(item.id, currentTab.value), item.id)
          });
        }),
        v: common_vendor.p({
          name: "calendar",
          color: "#909399",
          size: "14"
        }),
        w: common_vendor.p({
          name: "map",
          color: "#909399",
          size: "14"
        }),
        x: common_vendor.p({
          name: "account",
          color: "#909399",
          size: "14"
        }),
        y: common_vendor.p({
          status: loadStatus.value,
          line: true,
          marginTop: "20"
        })
      }), {
        z: common_vendor.o(loadMoreData)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a5d8397f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/search.js.map
