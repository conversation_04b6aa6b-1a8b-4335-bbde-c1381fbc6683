{"version": 3, "file": "park_detail.js", "sources": ["pages_sub/pages_other/park_detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXHBhcmtfZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"loading\" class=\"loading-container\">\r\n    <uni-load-more status=\"loading\" />\r\n  </view>\r\n\r\n  <view v-else-if=\"park\" class=\"page-container\">\r\n\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">{{ park.name || '园区详情' }}</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <image class=\"cover-image\" :src=\"baseUrl + park.coverImageUrl\" mode=\"aspectFill\" />\r\n\r\n      <view class=\"main-info-card\">\r\n        <view class=\"park-name\">{{ park.name }}</view>\r\n        <view class=\"info-line\">\r\n          <uni-icons type=\"location-filled\" size=\"16\" color=\"#999\"></uni-icons>\r\n          <text>{{ park.location }}</text>\r\n        </view>\r\n        <view class=\"info-line\">\r\n          <uni-icons type=\"paperclip\" size=\"16\" color=\"#999\"></uni-icons>\r\n          <text>{{ park.mainIndustries }}</text>\r\n        </view>\r\n        <view class=\"summary-box\">\r\n          {{ park.summary }}\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"content-card\">\r\n        <rich-text class=\"rich-text-content\" :nodes=\"park.content\"></rich-text>\r\n      </view>\r\n\r\n      <view class=\"related-country-section\">\r\n        <view class=\"related-country-button\" @click=\"goToCountryDetail\">\r\n          <text>查看所属国别详情：{{ park.countryName }}</text>\r\n          <uni-icons type=\"right\" size=\"16\" color=\"#2979ff\"></uni-icons>\r\n        </view>\r\n      </view>\r\n\r\n    </scroll-view>\r\n\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {ref} from 'vue';\r\nimport {onLoad, onShareAppMessage} from '@dcloudio/uni-app';\r\nimport {getParkDetail} from '@/pages_sub/pages_other/api/content/park.js';\r\nimport {IMAGE_BASE_URL} from '@/utils/config.js';\r\n\r\n// --- 自定义导航栏相关逻辑 (高级版本) ---\r\n// 1. 定义想要的额外间距（单位rpx），您可以随时修改这个值\r\nconst navBarPaddingBottomRpx = 20;\r\n// 2. 将 rpx 转换为 px，用于 JS 计算\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\n\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    // 3. 在总高度计算中使用转换后的 px 值\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    // 4. 在回退方案中也使用转换后的 px 值\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n// --- 导航栏逻辑结束 ---\r\n\r\nconst baseUrl = IMAGE_BASE_URL;\r\nconst park = ref(null);\r\nconst loading = ref(true);\r\n\r\nonLoad(async (options) => {\r\n  // 调用函数获取导航栏尺寸\r\n  getNavBarInfo();\r\n\r\n  if (!options.id) {\r\n    uni.showToast({ title: '参数错误', icon: 'none' });\r\n    uni.navigateBack();\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const res = await getParkDetail(options.id);\r\n    park.value = res.data;\r\n  } catch (error) {\r\n    console.error(error);\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n\r\nconst goToCountryDetail = () => {\r\n  if (park.value && park.value.countryId) {\r\n    uni.navigateTo({\r\n      url: `/pages_sub/pages_country/detail?id=${park.value.countryId}`\r\n    });\r\n  }\r\n};\r\n\r\n// 分享功能依然保留，用户可以通过小程序右上角菜单触发\r\nonShareAppMessage(() => {\r\n  return {\r\n    title: park.value?.name || '园区详情',\r\n    path: `/pages_sub/pages_other/park_detail?id=${park.value?.id || ''}`\r\n  };\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  background-color: #f7f8fa;\r\n  /* [已移除] 不再需要为底部栏留出空间 */\r\n  /* padding-bottom: 140rpx; */\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #23232A;\r\n}\r\n/* --- 导航栏样式结束 --- */\r\n\r\n\r\n.loading-container {\r\n  padding-top: 200rpx;\r\n}\r\n\r\n.cover-image {\r\n  width: 100%;\r\n  height: 400rpx;\r\n}\r\n\r\n.main-info-card {\r\n  background-color: #fff;\r\n  margin: 30rpx 30rpx 30rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .park-name {\r\n    font-size: 40rpx;\r\n    font-weight: bold;\r\n    margin-bottom: 24rpx;\r\n  }\r\n\r\n  .info-line {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #66666E;\r\n    font-size: 26rpx;\r\n    margin-bottom: 16rpx;\r\n\r\n    uni-icons {\r\n      margin-right: 12rpx;\r\n    }\r\n  }\r\n\r\n  .summary-box {\r\n    margin-top: 24rpx;\r\n    background-color: #f7f8fa;\r\n    border-radius: 8rpx;\r\n    padding: 20rpx;\r\n    font-size: 26rpx;\r\n    color: #66666E;\r\n    line-height: 1.6;\r\n  }\r\n}\r\n\r\n.content-card {\r\n  background-color: #fff;\r\n  margin: 0 30rpx 30rpx;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.rich-text-content {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  line-height: 1.8;\r\n\r\n  :deep(p) {\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  :deep(img) {\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n/* [新增] 查看国别详情的按钮样式 */\r\n.related-country-section {\r\n  padding: 0 30rpx 40rpx; /* 在内容最后增加一些边距 */\r\n}\r\n.related-country-button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background-color: #eef2ff;\r\n  padding: 24rpx 30rpx;\r\n  border-radius: 16rpx;\r\n  font-size: 28rpx;\r\n  color: #023F98;\r\n  font-weight: 500;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/park_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "IMAGE_BASE_URL", "onLoad", "getParkDetail", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;AA2DA,MAAA,yBAAA;;;;AAEA,UAAA,wBAAAA,cAAA,MAAA,OAAA,sBAAA;AAEA,UAAA,kBAAAC,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAAD,oBAAA;AACA,wBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA;AAEA,qBAAA,QAAA,eAAA,SAAA;AAAA,MACA,SAAA,GAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AACA,qBAAA,QAAA;AAEA,qBAAA,QAAA,gBAAA,QAAA,aAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA,EAAA,OAAA,EAAA,CAAA;AAAA,IACA;AAGA,UAAA,UAAAE,aAAAA;AACA,UAAA,OAAAD,cAAAA,IAAA,IAAA;AACA,UAAA,UAAAA,cAAAA,IAAA,IAAA;AAEAE,kBAAA,OAAA,OAAA,YAAA;AAEA;AAEA,UAAA,CAAA,QAAA,IAAA;AACAH,sBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AACAA,sBAAA,MAAA,aAAA;AACA;AAAA,MACA;AAEA,UAAA;AACA,cAAA,MAAA,MAAAI,uCAAAA,cAAA,QAAA,EAAA;AACA,aAAA,QAAA,IAAA;AAAA,MACA,SAAA,OAAA;AACAJ,sBAAAA,MAAA,MAAA,SAAA,gDAAA,KAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,oBAAA,MAAA;AACA,UAAA,KAAA,SAAA,KAAA,MAAA,WAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,sCAAA,KAAA,MAAA,SAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGAK,kBAAAA,kBAAA,MAAA;;AACA,aAAA;AAAA,QACA,SAAA,UAAA,UAAA,mBAAA,SAAA;AAAA,QACA,MAAA,2CAAA,UAAA,UAAA,mBAAA,OAAA,EAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7HA,GAAG,WAAW,eAAe;"}