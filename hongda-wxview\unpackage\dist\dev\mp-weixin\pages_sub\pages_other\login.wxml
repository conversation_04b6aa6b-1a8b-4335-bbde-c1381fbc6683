<view class="login-page data-v-de5c63ce"><image class="background-image data-v-de5c63ce" src="{{a}}" mode="aspectFill"></image><up-navbar wx:if="{{b}}" class="data-v-de5c63ce" u-i="de5c63ce-0" bind:__l="__l" u-p="{{b}}"></up-navbar><view class="content-wrapper data-v-de5c63ce"><view class="login-content data-v-de5c63ce"><view class="logo-section data-v-de5c63ce"><image class="logo-image data-v-de5c63ce" src="{{c}}" mode="aspectFit"></image></view><view class="action-section data-v-de5c63ce"><button class="login-btn data-v-de5c63ce" open-type="{{d}}" bindgetphonenumber="{{e}}" bindtap="{{f}}"> 微信手机号快捷登录 </button><view class="agreement-section data-v-de5c63ce" bindtap="{{k}}"><view class="{{['custom-checkbox', 'data-v-de5c63ce', h && 'is-checked']}}"><view wx:if="{{g}}" class="checkmark data-v-de5c63ce"></view></view><view class="agreement-text data-v-de5c63ce"><text class="data-v-de5c63ce">我已阅读并同意</text><text class="link-text data-v-de5c63ce" catchtap="{{i}}">《用户协议》</text><text class="data-v-de5c63ce">和</text><text class="link-text data-v-de5c63ce" catchtap="{{j}}">《隐私政策》</text></view></view></view></view></view></view>