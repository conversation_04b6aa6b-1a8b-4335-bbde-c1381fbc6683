"use strict";
function parseDate(input) {
  if (!input)
    return /* @__PURE__ */ new Date("Invalid Date");
  if (typeof input === "string") {
    const s = String(input).trim();
    if (s.includes("T")) {
      return new Date(s);
    }
    if (s.includes(" ")) {
      return new Date(s.replace(/-/g, "/"));
    }
    return new Date(s);
  }
  return new Date(input);
}
function formatDate(date, format = "YYYY-MM-DD") {
  const d = parseDate(date);
  if (isNaN(d.getTime()))
    return "";
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");
  return format.replace(/yyyy/i, year).replace("MM", month).replace(/dd/i, day).replace(/hh/i, hours).replace("mm", minutes).replace(/ss/i, seconds);
}
function formatEventDate(isoString) {
  const date = parseDate(isoString);
  if (isNaN(date.getTime()))
    return "";
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const week = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][date.getDay()];
  return `${year}-${month}-${day} ${week}`;
}
exports.formatDate = formatDate;
exports.formatEventDate = formatEventDate;
exports.parseDate = parseDate;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/date.js.map
