{"version": 3, "file": "EventPromotionComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9FdmVudFByb21vdGlvbkNvbXBvbmVudC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"promo-container\">\t\t\n\t\t<!-- 骨架屏 - 加载时显示 -->\n\t\t<view v-if=\"isLoading\" class=\"skeleton-wrapper\">\n\t\t\t<u-skeleton\n\t\t\t\t:loading=\"true\"\n\t\t\t\t:animate=\"true\"\n\t\t\t\t:rows=\"4\"\n\t\t\t\t:title=\"true\"\n\t\t\t\ttitleWidth=\"60%\"\n\t\t\t\trowsWidth=\"['100%', '40%', '40%', '100%']\"\n\t\t\t\trowsHeight=\"['180px', '20px', '20px', '40px']\"\n\t\t\t></u-skeleton>\n\t\t</view>\n\n\t\t<view v-else-if=\"promoDataList.length > 0\" class=\"promo-card-wrapper\">\n\t\t\t<!-- 轮播区域 -->\n\t\t\t<swiper \n\t\t\t\tref=\"swiperRef\"\n\t\t\t\tclass=\"promo-swiper\"\n\t\t\t\t:indicator-dots=\"false\"\n\t\t\t\t:autoplay=\"true\"\n\t\t\t\t:interval=\"4000\"\n\t\t\t\t:duration=\"500\"\n\t\t\t\t:circular=\"true\"\n\t\t\t\t:current=\"currentIndex\"\n\t\t\t\t@change=\"onSwiperChange\"\n\t\t\t>\n\t\t\t\t<swiper-item \n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \n\t\t\t\t\t:key=\"item.id || index\"\n\t\t\t\t\tclass=\"swiper-item\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"promo-card\" @click=\"handlePromoClick(item)\">\n\t\t\t\t\t\t<!-- 1. 顶部标题 -->\n\t\t\t\t\t\t<view class=\"promo-header\">\n\t\t\t\t\t\t\t<image v-if=\"item.iconUrl\" :src=\"getFullImageUrl(item.iconUrl)\" class=\"promo-icon\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<text class=\"promo-title\">{{ item.title }}</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 2. 中间主图 -->\n\t\t\t\t\t<view class=\"image-container\">\n\t\t\t\t\t\t<image \n\t\t\t\t\t\t\tclass=\"promo-main-image\" \n\t\t\t\t\t\t\t:src=\"item.image\" \n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t:lazy-load=\"true\"\n\t\t\t\t\t\t\t@error=\"onImageError\"\n\t\t\t\t\t\t\t@load=\"onImageLoad\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 3. 简介信息 -->\n\t\t\t\t\t\t\t<view class=\"promo-description\">\n\t\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine1\" class=\"desc-item\">\n\t\t\t\t\t\t\t\t\t<image class=\"desc-icon\" :src=\"flameIconUrl\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine1 }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine2\" class=\"desc-item\">\n\t\t\t\t\t\t\t\t\t<image class=\"desc-icon\" :src=\"thumbUpIconUrl\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine2 }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 4. 底部按钮 -->\n\t\t\t\t\t\t<view class=\"promo-footer\">\n\t\t\t\t\t\t\t<up-button \n\t\t\t\t\t\t\t\ttype=\"primary\" \n\t\t\t\t\t\t\t\tshape=\"square\" \n\t\t\t\t\t\t\t\ttext=\"立即报名\"\n\t\t\t\t\t\t\t\tsize=\"large\"\n                            @click=\"handlePromoClick(item)\"\n\t\t\t\t\t\t\t\t:customStyle=\"{ \n\t\t\t\t\t\t\t\t\tbackgroundColor: '#023F98',\n\t\t\t\t\t\t\t\t\theight: '68rpx',\n\t\t\t\t\t\t\t\t\twidth: '654rpx',\n\t\t\t\t\t\t\t\t\tborderRadius: '8rpx',\n\t\t\t\t\t\t\t\t\tmargin: '0 auto'\n\t\t\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t></up-button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t\t\n\t\t\t<!-- 自定义指示器-->\n\t\t\t<view v-if=\"promoDataList.length > 1\" class=\"custom-indicators\">\n\t\t\t\t<view \n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\tclass=\"indicator-dot\"\n\t\t\t\t\t:class=\"{ 'active': currentIndex === index }\"\n\t\t\t\t\t@click=\"switchToSlide(index)\"\n\t\t\t\t></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view v-else class=\"no-data-tip\" style=\"padding: 40rpx; text-align: center; color: #999;\">\n\t\t\t<text>暂无推广活动</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\nimport { getFullImageUrl } from '@/utils/image.js';\n\n// 静态资源 URL（仅暗号读取）\nconst flameIconUrl = ref('')\nconst thumbUpIconUrl = ref('')\n\n// --- 响应式状态 ---\nconst promoDataList = ref([]); \nconst isLoading = ref(true);\nconst currentIndex = ref(0); \n\n// 广告位代码常量\nconst POSITION_CODE = 'HOME_PROMO_EVENT';\n\n\n/**\n * 获取推广活动数据\n */\nconst fetchPromoData = async () => {\n\ttry {\n\t\t// 获取多条数据支持轮播\n\t\tconst response = await getAdListByPositionApi(POSITION_CODE, {\n\t\t\tpageSize: 10\n\t\t});\n\t\t\n\t\tconsole.log('活动推广API响应:', response);\n\t\t\n\t\tif (response.data && Array.isArray(response.data) && response.data.length > 0) {\n\t\t\t// 处理所有返回的广告数据\n\t\t\tpromoDataList.value = response.data.map(ad => {\n\t\t\t\t\n\t\t\t\tconst processedData = {\n\t\t\t\t\tid: ad.id,\n\t\t\t\t\ttitle: ad.title,\n\t\t\t\t\timage: getFullImageUrl(ad.imageUrl),\n\t\t\t\t\ticonUrl: ad.iconUrl, \n\t\t\t\t\tlinkUrl: ad.finalLinkUrl || ad.linkUrl, // 优先使用最终跳转链接\n\t\t\t\t\t// 使用后端返回的活动简介和卖点字段，如果为空则提供默认文案\n\t\t\t\t\tdescriptionLine1: ad.eventSummary || '官方认证，品质保证',\n\t\t\t\t\tdescriptionLine2: ad.eventSellPoint || '干货满满，不容错过'\n\t\t\t\t};\n\t\t\t\treturn processedData;\n\t\t\t});\n\t\t} else {\n\t\t\tconsole.warn('未找到推广数据或返回格式异常:', response);\n\t\t\tpromoDataList.value = []; // 确保清空数据\n\t\t}\n\t\t\n\t} catch (error) {\n\t\tconsole.error('获取推广数据失败:', error);\n\t} finally {\n\t\tisLoading.value = false;\n\t}\n};\n\n/**\n * 轮播切换事件处理\n */\nconst onSwiperChange = (e) => {\n\tcurrentIndex.value = e.detail.current;\n};\n\n/**\n * 点击指示器切换轮播\n */\nconst switchToSlide = (index) => {\n\tcurrentIndex.value = index;\n\tconsole.log('点击指示器切换到索引:', index);\n};\n\n/**\n * 处理卡片点击事件\n */\n// 解析广告链接中的活动ID\nconst extractEventId = (url) => {\n    if (!url) return null;\n    try {\n        // 1) 先尝试直接匹配 id=xxx\n        const idMatch = url.match(/[?&#]id=([^&#]+)/i);\n        if (idMatch && idMatch[1]) return decodeURIComponent(idMatch[1]);\n        const pathMatch = url.match(/\\/(?:event|events)\\/([\\w-]+)/i);\n        if (pathMatch && pathMatch[1]) return decodeURIComponent(pathMatch[1]);\n    } catch (e) {\n        console.warn('解析活动ID失败:', e);\n    }\n    return null;\n};\n\n// 卡片点击：优先跳到站内详情页\nconst handlePromoClick = (promoItem) => {\n    if (!promoItem) return;\n    const linkUrl = promoItem.linkUrl || '';\n    const eventId = extractEventId(linkUrl) || promoItem.id;\n\n    if (eventId) {\n        // 统一跳到站内详情\n        uni.navigateTo({\n            url: `/pages_sub/pages_event/detail?id=${encodeURIComponent(eventId)}`\n        });\n        return;\n    }\n\n    // 兼容：若无法解析到ID\n    if (linkUrl && linkUrl.startsWith('http')) {\n        uni.navigateTo({\n            url: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(promoItem.title || '详情')}`\n        });\n    } else if (linkUrl) {\n        uni.navigateTo({ url: linkUrl });\n    } else {\n        console.warn('推广卡片跳转链接为空');\n    }\n};\n\n// 立即报名：跳到站内报名页\nconst handleRegisterClick = (promoItem) => {\n    if (!promoItem) return;\n    const linkUrl = promoItem.linkUrl || '';\n    const eventId = extractEventId(linkUrl) || promoItem.id;\n    if (eventId) {\n        uni.navigateTo({\n            url: `/pages_sub/pages_event/registration?id=${encodeURIComponent(eventId)}`\n        });\n    } else if (linkUrl && linkUrl.startsWith('http')) {\n        // 实在没有ID则退化到H5链接\n        uni.navigateTo({\n            url: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(promoItem.title || '报名')}`\n        });\n    } else if (linkUrl) {\n        uni.navigateTo({ url: linkUrl });\n    } else {\n        console.warn('报名链接缺少活动ID');\n    }\n};\n\n/**\n * 图片加载成功事件\n */\nconst onImageLoad = (e) => {\n\tconsole.log('图片加载成功');\n};\n\n/**\n * 图片加载失败事件\n */\nconst onImageError = (e) => {\n\tconsole.error('图片加载失败:', e);\n};\n\n// --- 生命周期 ---\nonMounted(() => {\n\t// 读取静态资源配置\n\ttry {\n    const assets = uni.getStorageSync('staticAssets')\n    flameIconUrl.value = assets?.['flame-icon'] || ''\n    thumbUpIconUrl.value = assets?.['thumb-up-icon'] || ''\n\t} catch (e) {}\n\n\tfetchPromoData();\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.promo-container {\n\twidth: 702rpx;\n    height: auto; \n\tbackground: linear-gradient(180deg, rgba(2,63,152,0.1) 0%, rgba(2,63,152,0) 100%);\n\tborder-radius: 32rpx 32rpx 32rpx 32rpx;\n    margin: 24rpx 24rpx 0 24rpx; \n}\n\n.skeleton-wrapper {\n\tpadding: 24rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\theight: 100%;\n}\n\n// 轮播卡片容器\n.promo-card-wrapper {\n\tborder-radius: 16rpx 16rpx 0 0;\n\tbox-shadow: none;\n    height: auto;\n    padding-bottom: 16rpx;\n}\n// 轮播组件样式\n.promo-swiper {\n\twidth: 100%;\n    height: 560rpx; \n    position: relative;\n    z-index: 2; \n}\n\n.swiper-item {\n\twidth: 100%;\n\theight: 100%; \n}\n\n.promo-card {\n\tpadding: 24rpx;\n\twidth: 100%;\n\theight: 100%; \n\tbox-sizing: border-box;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.promo-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tflex-shrink: 0;\n\t\n\t.promo-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tborder-radius: 50%;\n\t\tmargin-right: 12rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.promo-title {\n\t\twidth: 552rpx;\n\t\theight: 44rpx;\n\t\tfont-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n\t\tfont-weight: normal;\n\t\tfont-size: 32rpx;\n\t\tcolor: #23232A;\n\t\ttext-align: left;\n\t\tfont-style: normal;\n\t\ttext-transform: none;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tflex: 1;\n\t\tline-height: 44rpx;\n\t}\n}\n\n.promo-main-image {\n\twidth: 100%;\n\tmax-height: 200rpx; \n\tborder-radius: 12rpx;\n\tdisplay: block;\n\tobject-fit: cover; \n\tflex-shrink: 0; \n}\n\n.image-container {\n\tbackground: #FFFFFF; \n\tborder-radius: 16rpx; \n\tpadding: 24rpx; \n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08); \n}\n\n.promo-description {\n\tmargin: 24rpx 0;\n\tflex: 1;\n\t\n\t.desc-item {\n\t\tdisplay: flex;\n\t\talign-items: flex-start; \n\t\tmargin-bottom: 16rpx;\n\t\t\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\t}\n\t\n\t.desc-icon {\n\t    width: 36rpx;\n\t    height: 36rpx;\n\t    flex-shrink: 0; \n\t}\n\t\n\t.desc-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #606266;\n\t\tmargin-left: 12rpx;\n\t\tline-height: 1.4;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n      line-clamp: 2; \n\t\t-webkit-line-clamp: 2; \n\t\toverflow: hidden;\n\t}\n}\n\n.promo-footer {\n\tflex-shrink: 0; \n\tmargin-top: auto; \n  position: relative;\n  z-index: 2;\n    margin-bottom: 24rpx; \n}\n\n// 自定义指示器样式 - 位于立即报名按钮下方\n.custom-indicators {\n    display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-top: 0;\n        padding: 0;\n        gap: 12rpx;\n        position: relative;\n        z-index: 1;\n        pointer-events: none;\n}\n\n.indicator-dot {\n    width: 18rpx;\n        height: 8rpx;\n        border-radius: 4rpx;\n    \tbackground-color: #e4e7ed; \n    \ttransition: background-color 0.3s ease;\n        pointer-events: auto;\n        flex-shrink: 0;\n\t\n\t/* 激活状态 */\n\t&.active {\n\t\tbackground-color: #004085;\n\t}\n}\n</style>\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "getAdListByPositionApi", "uni", "getFullImageUrl", "onMounted"], "mappings": ";;;;;;;;;;;;;;AAqHA,MAAM,gBAAgB;;;;AATtB,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAG7B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAS1B,UAAM,iBAAiB,YAAY;AAClC,UAAI;AAEH,cAAM,WAAW,MAAMC,gBAAsB,uBAAC,eAAe;AAAA,UAC5D,UAAU;AAAA,QACb,CAAG;AAEDC,sBAAY,MAAA,MAAA,OAAA,sDAAA,cAAc,QAAQ;AAElC,YAAI,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,KAAK,SAAS,KAAK,SAAS,GAAG;AAE9E,wBAAc,QAAQ,SAAS,KAAK,IAAI,QAAM;AAE7C,kBAAM,gBAAgB;AAAA,cACrB,IAAI,GAAG;AAAA,cACP,OAAO,GAAG;AAAA,cACV,OAAOC,YAAAA,gBAAgB,GAAG,QAAQ;AAAA,cAClC,SAAS,GAAG;AAAA,cACZ,SAAS,GAAG,gBAAgB,GAAG;AAAA;AAAA;AAAA,cAE/B,kBAAkB,GAAG,gBAAgB;AAAA,cACrC,kBAAkB,GAAG,kBAAkB;AAAA,YAC5C;AACI,mBAAO;AAAA,UACX,CAAI;AAAA,QACJ,OAAS;AACND,wBAAa,MAAA,MAAA,QAAA,sDAAA,mBAAmB,QAAQ;AACxC,wBAAc,QAAQ;QACtB;AAAA,MAED,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAAA,MAClC,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,iBAAiB,CAAC,MAAM;AAC7B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAC/B;AAKA,UAAM,gBAAgB,CAAC,UAAU;AAChC,mBAAa,QAAQ;AACrBA,oBAAA,MAAA,MAAA,OAAA,sDAAY,eAAe,KAAK;AAAA,IACjC;AAMA,UAAM,iBAAiB,CAAC,QAAQ;AAC5B,UAAI,CAAC;AAAK,eAAO;AACjB,UAAI;AAEA,cAAM,UAAU,IAAI,MAAM,mBAAmB;AAC7C,YAAI,WAAW,QAAQ,CAAC;AAAG,iBAAO,mBAAmB,QAAQ,CAAC,CAAC;AAC/D,cAAM,YAAY,IAAI,MAAM,+BAA+B;AAC3D,YAAI,aAAa,UAAU,CAAC;AAAG,iBAAO,mBAAmB,UAAU,CAAC,CAAC;AAAA,MACxE,SAAQ,GAAG;AACRA,sBAAa,MAAA,MAAA,QAAA,sDAAA,aAAa,CAAC;AAAA,MAC9B;AACD,aAAO;AAAA,IACX;AAGA,UAAM,mBAAmB,CAAC,cAAc;AACpC,UAAI,CAAC;AAAW;AAChB,YAAM,UAAU,UAAU,WAAW;AACrC,YAAM,UAAU,eAAe,OAAO,KAAK,UAAU;AAErD,UAAI,SAAS;AAETA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,oCAAoC,mBAAmB,OAAO,CAAC;AAAA,QAChF,CAAS;AACD;AAAA,MACH;AAGD,UAAI,WAAW,QAAQ,WAAW,MAAM,GAAG;AACvCA,sBAAAA,MAAI,WAAW;AAAA,UACX,KAAK,4BAA4B,mBAAmB,OAAO,CAAC,UAAU,mBAAmB,UAAU,SAAS,IAAI,CAAC;AAAA,QAC7H,CAAS;AAAA,MACJ,WAAU,SAAS;AAChBA,sBAAAA,MAAI,WAAW,EAAE,KAAK,QAAS,CAAA;AAAA,MACvC,OAAW;AACHA,sBAAAA,0EAAa,YAAY;AAAA,MAC5B;AAAA,IACL;AA0BA,UAAM,cAAc,CAAC,MAAM;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,QAAQ;AAAA,IACrB;AAKA,UAAM,eAAe,CAAC,MAAM;AAC3BA,oBAAc,MAAA,MAAA,SAAA,sDAAA,WAAW,CAAC;AAAA,IAC3B;AAGAE,kBAAAA,UAAU,MAAM;AAEf,UAAI;AACD,cAAM,SAASF,cAAAA,MAAI,eAAe,cAAc;AAChD,qBAAa,SAAQ,iCAAS,kBAAiB;AAC/C,uBAAe,SAAQ,iCAAS,qBAAoB;AAAA,MACxD,SAAU,GAAG;AAAA,MAAE;AAEd;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvQD,GAAG,gBAAgB,SAAS;"}