{"version": 3, "file": "scrollList.js", "sources": ["uni_modules/uview-plus/components/u-scroll-list/scrollList.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:19:28\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/scrollList.js\r\n */\r\nexport default {\r\n    // scrollList\r\n    scrollList: {\r\n        indicatorWidth: 50,\r\n        indicatorBarWidth: 20,\r\n        indicator: true,\r\n        indicatorColor: '#f2f2f2',\r\n        indicatorActiveColor: '#3c9cff',\r\n        indicatorStyle: ''\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,EACnB;AACL;;"}