{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-loading-icon/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 是否显示组件\r\n        show: {\r\n            type: Boolean,\r\n            default: () => defProps.loadingIcon.show\r\n        },\r\n        // 颜色\r\n        color: {\r\n            type: String,\r\n            default: () => defProps.loadingIcon.color\r\n        },\r\n        // 提示文字颜色\r\n        textColor: {\r\n            type: String,\r\n            default: () => defProps.loadingIcon.textColor\r\n        },\r\n        // 文字和图标是否垂直排列\r\n        vertical: {\r\n            type: <PERSON>olean,\r\n            default: () => defProps.loadingIcon.vertical\r\n        },\r\n        // 模式选择，circle-圆形，spinner-花朵形，semicircle-半圆形\r\n        mode: {\r\n            type: String,\r\n            default: () => defProps.loadingIcon.mode\r\n        },\r\n        // 图标大小，单位默认px\r\n        size: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingIcon.size\r\n        },\r\n        // 文字大小\r\n        textSize: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingIcon.textSize\r\n        },\r\n        // 文字内容\r\n        text: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingIcon.text\r\n        },\r\n        // 动画模式\r\n        timingFunction: {\r\n            type: String,\r\n            default: () => defProps.loadingIcon.timingFunction\r\n        },\r\n        // 动画执行周期时间\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.loadingIcon.duration\r\n        },\r\n        // mode=circle时的暗边颜色\r\n        inactiveColor: {\r\n            type: String,\r\n            default: () => defProps.loadingIcon.inactiveColor\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,YAAY;AAAA,IACvC;AAAA,EACJ;AACL,CAAC;;"}