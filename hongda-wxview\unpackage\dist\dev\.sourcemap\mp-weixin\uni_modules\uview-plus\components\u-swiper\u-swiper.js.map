{"version": 3, "file": "u-swiper.js", "sources": ["uni_modules/uview-plus/components/u-swiper/u-swiper.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LXN3aXBlci91LXN3aXBlci52dWU"], "sourcesContent": ["<template>\r\n\t<view\r\n\t\tclass=\"u-swiper\"\r\n\t\t:style=\"{\r\n\t\t\tbackgroundColor: bgColor,\r\n\t\t\theight: addUnit(height),\r\n\t\t\tborderRadius: addUnit(radius)\r\n\t\t}\"\r\n\t>\r\n\t\t<view\r\n\t\t\tclass=\"u-swiper__loading\"\r\n\t\t\tv-if=\"loading\"\r\n\t\t>\r\n\t\t\t<up-loading-icon mode=\"circle\"></up-loading-icon>\r\n\t\t</view>\r\n\t\t<swiper\r\n\t\t\tv-else\r\n\t\t\tclass=\"u-swiper__wrapper\"\r\n\t\t\t:style=\"{\r\n\t\t\t\tflex: '1',\r\n\t\t\t\theight: addUnit(height)\r\n\t\t\t}\"\r\n\t\t\t@change=\"change\"\r\n\t\t\t:circular=\"circular\"\r\n\t\t\t:interval=\"interval\"\r\n\t\t\t:duration=\"duration\"\r\n\t\t\t:autoplay=\"autoplay\"\r\n\t\t\t:current=\"current\"\r\n\t\t\t:currentItemId=\"currentItemId\"\r\n\t\t\t:previousMargin=\"addUnit(previousMargin)\"\r\n\t\t\t:nextMargin=\"addUnit(nextMargin)\"\r\n\t\t\t:acceleration=\"acceleration\"\r\n\t\t\t:displayMultipleItems=\"list.length > 0 ? displayMultipleItems : 0\"\r\n\t\t\t:easingFunction=\"easingFunction\"\r\n\t\t>\r\n\t\t\t<swiper-item\r\n\t\t\t\tclass=\"u-swiper__wrapper__item\"\r\n\t\t\t\tv-for=\"(item, index) in list\"\r\n\t\t\t\t:key=\"index\"\r\n\t\t\t>\r\n\t\t\t\t<slot :item=\"item\" :index=\"index\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper\"\r\n\t\t\t\t\t\t:style=\"[itemStyle(index)]\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- 在nvue中，image图片的宽度默认为屏幕宽度，需要通过flex:1撑开，另外必须设置高度才能显示图片 -->\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__image\"\r\n\t\t\t\t\t\t\tv-if=\"getItemType(item) === 'image'\"\r\n\t\t\t\t\t\t\t:src=\"getSource(item)\"\r\n\t\t\t\t\t\t\t:mode=\"imgMode\"\r\n\t\t\t\t\t\t\t@tap=\"clickHandler(index)\"\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\theight: addUnit(height),\r\n\t\t\t\t\t\t\t\tborderRadius: addUnit(radius)\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t<video\r\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__video\"\r\n\t\t\t\t\t\t\tv-if=\"getItemType(item) === 'video'\"\r\n\t\t\t\t\t\t\t:id=\"`video-${index}`\"\r\n\t\t\t\t\t\t\t:enable-progress-gesture=\"false\"\r\n\t\t\t\t\t\t\t:src=\"getSource(item)\"\r\n\t\t\t\t\t\t\t:poster=\"getPoster(item)\"\r\n\t\t\t\t\t\t\t:title=\"showTitle && testObject(item) && item.title ? item.title : ''\"\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\theight: addUnit(height)\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\t\t@tap=\"clickHandler(index)\"\r\n\t\t\t\t\t\t></video>\r\n\t\t\t\t\t\t<view v-if=\"showTitle && testObject(item) && item.title && testImage(getSource(item))\"\r\n\t\t\t\t\t\t\tclass=\"u-swiper__wrapper__item__wrapper__title\">\r\n\t\t\t\t\t\t\t<text class=\"u-line-1\">{{ item.title }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view class=\"u-swiper__indicator\" :style=\"[addStyle(indicatorStyle)]\">\r\n\t\t\t<slot name=\"indicator\">\r\n\t\t\t\t<up-swiper-indicator\r\n\t\t\t\t\tv-if=\"!loading && indicator && !showTitle\"\r\n\t\t\t\t\t:indicatorActiveColor=\"indicatorActiveColor\"\r\n\t\t\t\t\t:indicatorInactiveColor=\"indicatorInactiveColor\"\r\n\t\t\t\t\t:length=\"list.length\"\r\n\t\t\t\t\t:current=\"currentIndex\"\r\n\t\t\t\t\t:indicatorMode=\"indicatorMode\"\r\n\t\t\t\t></up-swiper-indicator>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport { props } from './props.js';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addUnit, addStyle, error } from '../../libs/function/index';\r\n\timport test from '../../libs/function/test';\r\n\t/**\r\n\t * Swiper 轮播图\r\n\t * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/swiper.html\r\n\t * @property {Array}\t\t\tlist\t\t\t\t\t轮播图数据\r\n\t * @property {Boolean}\t\t\tindicator\t\t\t\t是否显示面板指示器（默认 false ）\r\n\t * @property {String}\t\t\tindicatorActiveColor\t指示器非激活颜色（默认 '#FFFFFF' ）\r\n\t * @property {String}\t\t\tindicatorInactiveColor\t指示器的激活颜色（默认 'rgba(255, 255, 255, 0.35)' ）\r\n\t * @property {String | Object}\tindicatorStyle\t\t\t指示器样式，可通过bottom，left，right进行定位\r\n\t * @property {String}\t\t\tindicatorMode\t\t\t指示器模式（默认 'line' ）\r\n\t * @property {Boolean}\t\t\tautoplay\t\t\t\t是否自动切换（默认 true ）\r\n\t * @property {String | Number}\tcurrent\t\t\t\t\t当前所在滑块的 index（默认 0 ）\r\n\t * @property {String}\t\t\tcurrentItemId\t\t\t当前所在滑块的 item-id ，不能与 current 被同时指定\r\n\t * @property {String | Number}\tinterval\t\t\t\t滑块自动切换时间间隔（ms）（默认 3000 ）\r\n\t * @property {String | Number}\tduration\t\t\t\t滑块切换过程所需时间（ms）（默认 300 ）\r\n\t * @property {Boolean}\t\t\tcircular\t\t\t\t播放到末尾后是否重新回到开头（默认 false ）\r\n\t * @property {String | Number}\tpreviousMargin\t\t\t前边距，可用于露出前一项的一小部分，nvue和支付宝不支持（默认 0 ）\r\n\t * @property {String | Number}\tnextMargin\t\t\t\t后边距，可用于露出后一项的一小部分，nvue和支付宝不支持（默认 0 ）\r\n\t * @property {Boolean}\t\t\tacceleration\t\t\t当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持（默认 false ）\r\n\t * @property {Number}\t\t\tdisplayMultipleItems\t同时显示的滑块数量，nvue、支付宝小程序不支持（默认 1 ）\r\n\t * @property {String}\t\t\teasingFunction\t\t\t指定swiper切换缓动动画类型， 只对微信小程序有效（默认 'default' ）\r\n\t * @property {String}\t\t\tkeyName\t\t\t\t\tlist数组中指定对象的目标属性名（默认 'url' ）\r\n\t * @property {String}\t\t\timgMode\t\t\t\t\t图片的裁剪模式（默认 'aspectFill' ）\r\n\t * @property {String | Number}\theight\t\t\t\t\t组件高度（默认 130 ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t\t\t背景颜色（默认 \t'#f3f4f6' ）\r\n\t * @property {String | Number}\tradius\t\t\t\t\t组件圆角，数值或带单位的字符串（默认 4 ）\r\n\t * @property {Boolean}\t\t\tloading\t\t\t\t\t是否加载中（默认 false ）\r\n\t * @property {Boolean}\t\t\tshowTitle\t\t\t\t是否显示标题，要求数组对象中有title属性（默认 false ）\r\n\t * @pages_event {Function(index)}\tclick\t点击轮播图时触发\tindex：点击了第几张图片，从0开始\r\n\t * @pages_event {Function(index)}\tchange\t轮播图切换时触发(自动或者手动切换)\tindex：切换到了第几张图片，从0开始\r\n\t * @example\t<u-swiper :list=\"list4\" keyName=\"url\" :autoplay=\"false\"></u-swiper>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-swiper',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcurrentIndex: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcurrent(val, preVal) {\r\n\t\t\t\tif(val === preVal) return;\r\n\t\t\t\tthis.currentIndex = val; // 和上游数据关联上\r\n\t\t\t}\r\n\t\t},\r\n\t\temits: [\"click\", \"change\", \"update:current\"],\r\n\t\tcomputed: {\r\n\t\t\titemStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tconst style = {}\r\n\t\t\t\t\t// #ifndef APP-NVUE || MP-TOUTIAO\r\n\t\t\t\t\t// 左右流出空间的写法不支持nvue和头条\r\n\t\t\t\t\t// 只有配置了此二值，才加上对应的圆角，以及缩放\r\n\t\t\t\t\tif (this.nextMargin && this.previousMargin) {\r\n\t\t\t\t\t\tstyle.borderRadius = addUnit(this.radius)\r\n\t\t\t\t\t\tif (index !== this.currentIndex) style.transform = 'scale(0.92)'\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\treturn style\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\ttestObject: test.object,\r\n\t\t\ttestImage: test.image,\r\n\t\t\tgetItemType(item) {\r\n\t\t\t\tif (typeof item === 'string') return test.video(this.getSource(item)) ? 'video' : 'image'\r\n\t\t\t\tif (typeof item === 'object' && this.keyName) {\r\n\t\t\t\tif (!item.type) return test.video(this.getSource(item)) ? 'video' : 'image'\r\n\t\t\t\tif (item.type === 'image') return 'image'\r\n\t\t\t\tif (item.type === 'video') return 'video'\r\n\t\t\t\treturn 'image'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取目标路径，可能数组中为字符串，对象的形式，额外可指定对象的目标属性名keyName\r\n\t\t\tgetSource(item) {\r\n\t\t\t\tif (typeof item === 'string') return item\r\n\t\t\t\tif (typeof item === 'object' && this.keyName) return item[this.keyName]\r\n\t\t\t\telse error('请按格式传递列表参数')\r\n\t\t\t\treturn ''\r\n\t\t\t},\r\n\t\t\t// 轮播切换事件\r\n\t\t\tchange(e) {\r\n\t\t\t\t// 当前的激活索引\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcurrent\r\n\t\t\t\t} = e.detail\r\n\t\t\t\tthis.pauseVideo(this.currentIndex)\r\n\t\t\t\tthis.currentIndex = current\r\n\t\t\t\tthis.$emit('update:current', this.currentIndex)\r\n\t\t\t\tthis.$emit('change', e.detail)\r\n\t\t\t},\r\n\t\t\t// 切换轮播时，暂停视频播放\r\n\t\t\tpauseVideo(index) {\r\n\t\t\t\tconst lastItem = this.getSource(this.list[index])\r\n\t\t\t\tif (test.video(lastItem)) {\r\n\t\t\t\t\t// 当视频隐藏时，暂停播放\r\n\t\t\t\t\tconst video = uni.createVideoContext(`video-${index}`, this)\r\n\t\t\t\t\tvideo.pause()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 当一个轮播item为视频时，获取它的视频海报\r\n\t\t\tgetPoster(item) {\r\n\t\t\t\treturn typeof item === 'object' && item.poster ? item.poster : ''\r\n\t\t\t},\r\n\t\t\t// 点击某个item\r\n\t\t\tclickHandler(index) {\r\n\t\t\t\tthis.$emit('click', index)\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t\r\n\t.u-swiper__wrapper {\r\n\t\tflex: 1;\r\n\t}\r\n\t.u-swiper {\r\n\t\t@include flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\r\n\t\t&__wrapper {\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t&__item {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t&__wrapper {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttransition: transform 0.3s;\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t&__image {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__video {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__title {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__indicator {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 10px;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-swiper/u-swiper.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "addStyle", "test", "error", "uni"], "mappings": ";;;;;;;AAmIC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,oDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAQ,KAAK,QAAQ;AACpB,UAAG,QAAQ;AAAQ;AACnB,WAAK,eAAe;AAAA,IACrB;AAAA,EACA;AAAA,EACD,OAAO,CAAC,SAAS,UAAU,gBAAgB;AAAA,EAC3C,UAAU;AAAA,IACT,YAAY;AACX,aAAO,WAAS;AACf,cAAM,QAAQ,CAAC;AAIf,YAAI,KAAK,cAAc,KAAK,gBAAgB;AAC3C,gBAAM,eAAeC,kDAAQ,KAAK,MAAM;AACxC,cAAI,UAAU,KAAK;AAAc,kBAAM,YAAY;AAAA,QACpD;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAD,0CAAO;AAAA,IACP,YAAYE,yCAAI,KAAC;AAAA,IACjB,WAAWA,yCAAI,KAAC;AAAA,IAChB,YAAY,MAAM;AACjB,UAAI,OAAO,SAAS;AAAU,eAAOA,8CAAK,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU;AAClF,UAAI,OAAO,SAAS,YAAY,KAAK,SAAS;AAC9C,YAAI,CAAC,KAAK;AAAM,iBAAOA,8CAAK,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,UAAU;AACpE,YAAI,KAAK,SAAS;AAAS,iBAAO;AAClC,YAAI,KAAK,SAAS;AAAS,iBAAO;AAClC,eAAO;AAAA,MACP;AAAA,IACA;AAAA;AAAA,IAED,UAAU,MAAM;AACf,UAAI,OAAO,SAAS;AAAU,eAAO;AACrC,UAAI,OAAO,SAAS,YAAY,KAAK;AAAS,eAAO,KAAK,KAAK,OAAO;AAAA;AACjEC,kDAAAA,MAAM,YAAY;AACvB,aAAO;AAAA,IACP;AAAA;AAAA,IAED,OAAO,GAAG;AAET,YAAM;AAAA,QACL;AAAA,MACD,IAAI,EAAE;AACN,WAAK,WAAW,KAAK,YAAY;AACjC,WAAK,eAAe;AACpB,WAAK,MAAM,kBAAkB,KAAK,YAAY;AAC9C,WAAK,MAAM,UAAU,EAAE,MAAM;AAAA,IAC7B;AAAA;AAAA,IAED,WAAW,OAAO;AACjB,YAAM,WAAW,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC;AAChD,UAAID,yCAAI,KAAC,MAAM,QAAQ,GAAG;AAEzB,cAAM,QAAQE,cAAAA,MAAI,mBAAmB,SAAS,KAAK,IAAI,IAAI;AAC3D,cAAM,MAAM;AAAA,MACb;AAAA,IACA;AAAA;AAAA,IAED,UAAU,MAAM;AACf,aAAO,OAAO,SAAS,YAAY,KAAK,SAAS,KAAK,SAAS;AAAA,IAC/D;AAAA;AAAA,IAED,aAAa,OAAO;AACnB,WAAK,MAAM,SAAS,KAAK;AAAA,IAC1B;AAAA,EACA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnND,GAAG,gBAAgB,SAAS;"}