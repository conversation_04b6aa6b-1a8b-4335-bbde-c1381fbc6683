{"version": 3, "file": "u-loading-page.js", "sources": ["uni_modules/uview-plus/components/u-loading-page/u-loading-page.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWxvYWRpbmctcGFnZS91LWxvYWRpbmctcGFnZS52dWU"], "sourcesContent": ["<template>\r\n    <u-transition\r\n        :show=\"loading\"\r\n        :custom-style=\"{\r\n            position: 'fixed',\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            bottom: 0,\r\n            backgroundColor: bgColor,\r\n            display: 'flex',\r\n            zIndex: zIndex,\r\n            ...customStyle\r\n        }\"\r\n    >\r\n        <view class=\"u-loading-page\">\r\n            <view class=\"u-loading-page__warpper\">\r\n                <view class=\"u-loading-page__warpper__loading-icon\">\r\n                    <image\r\n                        v-if=\"image\"\r\n                        :src=\"image\"\r\n                        class=\"u-loading-page__warpper__loading-icon__img\"\r\n                        mode=\"widthFit\"\r\n\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\twidth: addUnit(iconSize),\r\n\t\t\t\t\t\t    height: addUnit(iconSize)\r\n\t\t\t\t\t\t}\"\r\n                    ></image>\r\n                    <u-loading-icon\r\n                        v-else\r\n                        :mode=\"loadingMode\"\r\n                        :size=\"addUnit(iconSize)\"\r\n                        :color=\"loadingColor\"\r\n                    ></u-loading-icon>\r\n                </view>\r\n                <slot>\r\n                    <text\r\n                        class=\"u-loading-page__warpper__text\"\r\n                        :style=\"{\r\n                            fontSize: addUnit(fontSize),\r\n                            color: color,\r\n                        }\"\r\n                        >{{ loadingText }}</text\r\n                    >\r\n                </slot>\r\n            </view>\r\n        </view>\r\n    </u-transition>\r\n</template>\r\n\r\n<script>\r\nimport { props } from \"./props\";\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { addUnit } from '../../libs/function/index';\r\n/**\r\n * loadingPage 加载动画\r\n * @description 警此组件为一个小动画，目前用在uView的loadmore加载更多和switch开关等组件的正在加载状态场景。\r\n * @tutorial https://ijry.github.io/uview-plus/components/loading.html\r\n * @property {String | Number}\tloadingText\t\t提示内容  (默认 '正在加载' )\r\n * @property {String}\t\t\timage\t\t\t文字上方用于替换loading动画的图片\r\n * @property {String}\t\t\tloadingMode\t\t加载动画的模式，circle-圆形，spinner-花朵形，semicircle-半圆形 （默认 'circle' ）\r\n * @property {Boolean}\t\t\tloading\t\t\t是否加载中 （默认 false ）\r\n * @property {String}\t\t\tbgColor\t\t\t背景色 （默认 '#ffffff' ）\r\n * @property {String}\t\t\tcolor\t\t\t文字颜色 （默认 '#C8C8C8' ）\r\n * @property {String | Number}\tfontSize\t\t文字大小 （默认 19 ）\r\n * @property {String | Number}\ticonSize\t\t图标大小 （默认 28 ）\r\n * @property {String}\t\t\tloadingColor\t加载中图标的颜色，只能rgb或者十六进制颜色值 （默认 '#C8C8C8' ）\r\n * @property {Number}\t\t\tzIndex\t        z-index层级 （默认10 ）\r\n * @property {Object}\t\t\tcustomStyle\t\t自定义样式\r\n * @example <u-loading mode=\"circle\"></u-loading>\r\n */\r\nexport default {\r\n    name: \"u-loading-page\",\r\n    mixins: [mpMixin, mixin, props],\r\n    data() {\r\n        return {};\r\n    },\r\n    methods: {\r\n        addUnit\r\n    }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n$text-color: rgb(200, 200, 200) !default;\r\n$text-size: 19px !default;\r\n$u-loading-icon-margin-bottom: 10px !default;\r\n\r\n.u-loading-page {\r\n    @include flex(column);\r\n    flex: 1;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &__warpper {\r\n        margin-top: -150px;\r\n        justify-content: center;\r\n        align-items: center;\r\n        /* #ifndef APP-NVUE */\r\n        color: $text-color;\r\n        font-size: $text-size;\r\n        /* #endif */\r\n        @include flex(column);\r\n\r\n        &__loading-icon {\r\n            margin-bottom: $u-loading-icon-margin-bottom;\r\n\r\n            &__img {\r\n                width: 40px;\r\n                height: 40px;\r\n            }\r\n        }\r\n\r\n        &__text {\r\n            font-size: $text-size;\r\n            color: $text-color;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-loading-page/u-loading-page.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit"], "mappings": ";;;;;;AAwEA,MAAK,YAAU;AAAA,EACX,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,yDAAK;AAAA,EAC9B,OAAO;AACH,WAAO;EACV;AAAA,EACD,SAAS;AAAA,IACL,SAAAC,0CAAM;AAAA,EACV;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChFA,GAAG,gBAAgB,SAAS;"}