{"version": 3, "file": "tabbar.js", "sources": ["uni_modules/uview-plus/components/u-tabbar/tabbar.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:22:40\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbar.js\r\n */\r\nexport default {\r\n    // tabbar\r\n    tabbar: {\r\n        value: null,\r\n        safeAreaInsetBottom: true,\r\n        border: true,\r\n        zIndex: 1,\r\n        activeColor: '#1989fa',\r\n        inactiveColor: '#7d7e80',\r\n        fixed: true,\r\n        placeholder: true\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,aAAa;AAAA,EAChB;AACL;;"}