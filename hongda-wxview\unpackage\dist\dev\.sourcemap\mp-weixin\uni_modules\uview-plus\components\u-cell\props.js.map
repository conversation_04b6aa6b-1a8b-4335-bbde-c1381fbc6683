{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-cell/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 标题\r\n        title: {\r\n            type: [String, Number],\r\n            default: () => defProps.cell.title\r\n        },\r\n        // 标题下方的描述信息\r\n        label: {\r\n            type: [String, Number],\r\n            default: () => defProps.cell.label\r\n        },\r\n        // 右侧的内容\r\n        value: {\r\n            type: [String, Number],\r\n            default: () => defProps.cell.value\r\n        },\r\n        // 左侧图标名称，或者图片链接(本地文件建议使用绝对地址)\r\n        icon: {\r\n            type: String,\r\n            default: () => defProps.cell.icon\r\n        },\r\n        // 是否禁用cell\r\n        disabled: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.disabled\r\n        },\r\n        // 是否显示下边框\r\n        border: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.border\r\n        },\r\n        // 内容是否垂直居中(主要是针对右侧的value部分)\r\n        center: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.center\r\n        },\r\n        // 点击后跳转的URL地址\r\n        url: {\r\n            type: String,\r\n            default: () => defProps.cell.url\r\n        },\r\n        // 链接跳转的方式，内部使用的是uView封装的route方法，可能会进行拦截操作\r\n        linkType: {\r\n            type: String,\r\n            default: () => defProps.cell.linkType\r\n        },\r\n        // 是否开启点击反馈(表现为点击时加上灰色背景)\r\n        clickable: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.clickable\r\n        },\r\n        // 是否展示右侧箭头并开启点击反馈\r\n        isLink: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.isLink\r\n        },\r\n        // 是否显示表单状态下的必填星号(此组件可能会内嵌入input组件)\r\n        required: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.required\r\n        },\r\n        // 右侧的图标箭头\r\n        rightIcon: {\r\n            type: String,\r\n            default: () => defProps.cell.rightIcon\r\n        },\r\n        // 右侧箭头的方向，可选值为：left，up，down\r\n        arrowDirection: {\r\n            type: String,\r\n            default: () => defProps.cell.arrowDirection\r\n        },\r\n        // 左侧图标样式\r\n        iconStyle: {\r\n            type: [Object, String],\r\n            default: () => {\r\n\t\t\t\treturn defProps.cell.iconStyle\r\n\t\t\t}\r\n        },\r\n        // 右侧箭头图标的样式\r\n        rightIconStyle: {\r\n            type: [Object, String],\r\n            default: () => {\r\n\t\t\t\treturn defProps.cell.rightIconStyle\r\n\t\t\t}\r\n        },\r\n        // 标题的样式\r\n        titleStyle: {\r\n            type: [Object, String],\r\n\t\t\tdefault: () => {\r\n\t\t\t\treturn defProps.cell.titleStyle\r\n\t\t\t}\r\n        },\r\n        // 单位元的大小，可选值为large\r\n        size: {\r\n            type: String,\r\n            default: () => defProps.cell.size\r\n        },\r\n        // 点击cell是否阻止事件传播\r\n        stop: {\r\n            type: Boolean,\r\n            default: () => defProps.cell.stop\r\n        },\r\n        // 标识符，cell被点击时返回\r\n        name: {\r\n            type: [Number, String],\r\n            default: () => defProps.cell.name\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM;AACvB,eAAOA,wCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM;AACvB,eAAOA,wCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MAC9B,SAAS,MAAM;AACd,eAAOA,wCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}