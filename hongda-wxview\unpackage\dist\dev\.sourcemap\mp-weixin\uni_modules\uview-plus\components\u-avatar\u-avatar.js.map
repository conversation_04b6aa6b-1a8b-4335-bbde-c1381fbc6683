{"version": 3, "file": "u-avatar.js", "sources": ["uni_modules/uview-plus/components/u-avatar/u-avatar.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L3VuaV9tb2R1bGVzL3V2aWV3LXBsdXMvY29tcG9uZW50cy91LWF2YXRhci91LWF2YXRhci52dWU"], "sourcesContent": ["<template>\r\n\t<view\r\n\t\tclass=\"u-avatar\"\r\n\t\t:class=\"[`u-avatar--${shape}`]\"\r\n\t\t:style=\"[{\r\n\t\t\tbackgroundColor: (text || icon) ? (randomBgColor ? colors[colorIndex !== '' ? colorIndex : random(0, 19)] : bgColor) : 'transparent',\r\n\t\t\twidth: addUnit(size),\r\n\t\t\theight: addUnit(size),\r\n\t\t}, addStyle(customStyle)]\"\r\n\t\t@tap=\"clickHandler\"\r\n\t>\r\n\t\t<slot>\r\n\t\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU  -->\r\n\t\t\t<open-data\r\n\t\t\t\tv-if=\"mpAvatar && allowMp\"\r\n\t\t\t\ttype=\"userAvatarUrl\"\r\n\t\t\t\t:style=\"[{\r\n\t\t\t\t\twidth: addUnit(size),\r\n\t\t\t\t\theight: addUnit(size)\r\n\t\t\t\t}]\"\r\n\t\t\t/>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifndef MP-WEIXIN && MP-QQ && MP-BAIDU  -->\r\n\t\t\t<template v-if=\"mpAvatar && allowMp\"></template>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<u-icon\r\n\t\t\t\tv-else-if=\"icon\"\r\n\t\t\t\t:name=\"icon\"\r\n\t\t\t\t:size=\"fontSize\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t></u-icon>\r\n\t\t\t<up-text\r\n\t\t\t\tv-else-if=\"text\"\r\n\t\t\t\t:text=\"text\"\r\n\t\t\t\t:size=\"fontSize\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t\talign=\"center\"\r\n\t\t\t\tcustomStyle=\"justify-content: center\"\r\n\t\t\t></up-text>\r\n\t\t\t<image\r\n\t\t\t\tclass=\"u-avatar__image\"\r\n\t\t\t\tv-else\r\n\t\t\t\t:class=\"[`u-avatar__image--${shape}`]\"\r\n\t\t\t\t:src=\"avatarUrl || defaultUrl\"\r\n\t\t\t\t:mode=\"mode\"\r\n\t\t\t\t@error=\"errorHandler\"\r\n\t\t\t\t:style=\"[{\r\n\t\t\t\t\twidth: addUnit(size),\r\n\t\t\t\t\theight: addUnit(size)\r\n\t\t\t\t}]\"\r\n\t\t\t></image>\r\n\t\t</slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from './props';\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle, addUnit, random } from '../../libs/function/index';\r\n\tconst base64Avatar =\r\n\t\t\"data:image/jpg;base64,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\";\r\n\t/**\r\n\t * Avatar  头像\r\n\t * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/avatar.html\r\n\t *\r\n\t * @property {String}\t\t\tsrc\t\t\t\t头像路径，如加载失败，将会显示默认头像(不能为相对路径)\r\n\t * @property {String}\t\t\tshape\t\t\t头像形状  （ circle (默认) | square）\r\n\t * @property {String | Number}\tsize\t\t\t头像尺寸，可以为指定字符串(large, default, mini)，或者数值 （默认 40 ）\r\n\t * @property {String}\t\t\tmode\t\t\t头像图片的裁剪类型，与uni的image组件的mode参数一致，如效果达不到需求，可尝试传widthFix值 （默认 'scaleToFill' ）\r\n\t * @property {String}\t\t\ttext\t\t\t用文字替代图片，级别优先于src\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色，一般显示文字时用 （默认 '#c0c4cc' ）\r\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色 （默认 '#ffffff' ）\r\n\t * @property {String | Number}\tfontSize\t\t文字大小  （默认 18 ）\r\n\t * @property {String}\t\t\ticon\t\t\t显示的图标\r\n\t * @property {Boolean}\t\t\tmpAvatar\t\t显示小程序头像，只对百度，微信，QQ小程序有效  （默认 false ）\r\n\t * @property {Boolean}\t\t\trandomBgColor\t是否使用随机背景色  （默认 false ）\r\n\t * @property {String}\t\t\tdefaultUrl\t\t加载失败的默认头像(组件有内置默认图片)\r\n\t * @property {String | Number}\tcolorIndex\t\t如果配置了randomBgColor为true，且配置了此值，则从默认的背景色数组中取出对应索引的颜色值，取值0-19之间\r\n\t * @property {String}\t\t\tname\t\t\t组件标识符  （默认 'level' ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t *\r\n\t * @pages_event    {Function}        click       点击组件时触发   index: 用户传递的标识符\r\n\t * @example  <u-avatar :src=\"src\" mode=\"square\"></u-avatar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-avatar',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 如果配置randomBgColor参数为true，在图标或者文字的模式下，会随机从中取出一个颜色值当做背景色\r\n\t\t\t\tcolors: ['#ffb34b', '#f2bba9', '#f7a196', '#f18080', '#88a867', '#bfbf39', '#89c152', '#94d554', '#f19ec2',\r\n\t\t\t\t\t'#afaae4', '#e1b0df', '#c38cc1', '#72dcdc', '#9acdcb', '#77b1cc', '#448aca', '#86cefa', '#98d1ee',\r\n\t\t\t\t\t'#73d1f1',\r\n\t\t\t\t\t'#80a7dc'\r\n\t\t\t\t],\r\n\t\t\t\tavatarUrl: this.src,\r\n\t\t\t\tallowMp: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听头像src的变化，赋值给内部的avatarUrl变量，因为图片加载失败时，需要修改图片的src为默认值\r\n\t\t\t// 而组件内部不能直接修改props的值，所以需要一个中间变量\r\n\t\t\tsrc: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.avatarUrl = newVal\r\n\t\t\t\t\t// 如果没有传src，则主动触发error事件，用于显示默认的头像，否则src为''空字符等的时候，会无内容展示\r\n\t\t\t\t\tif(!newVal) {\r\n\t\t\t\t\t\tthis.errorHandler()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\timageStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\treturn style\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\temits: [\"click\"],\r\n\t\tmethods: {\r\n\t\t\taddStyle,\r\n\t\t\taddUnit,\r\n\t\t\trandom,\r\n\t\t\tinit() {\r\n\t\t\t\t// 目前只有这几个小程序平台具有open-data标签\r\n\t\t\t\t// 其他平台可以通过uni.getUserInfo类似接口获取信息，但是需要弹窗授权(首次)，不合符组件逻辑\r\n\t\t\t\t// 故目前自动获取小程序头像只支持这几个平台\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU\r\n\t\t\t\tthis.allowMp = true\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\r\n\t\t\tisImg() {\r\n\t\t\t\treturn this.src.indexOf('/') !== -1\r\n\t\t\t},\r\n\t\t\t// 图片加载时失败时触发\r\n\t\t\terrorHandler() {\r\n\t\t\t\tthis.avatarUrl = this.defaultUrl || base64Avatar\r\n\t\t\t},\r\n\t\t\tclickHandler(e) {\r\n\t\t\t\tthis.$emit('click', this.name, e)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.u-avatar {\r\n\t\t@include flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t&--circle {\r\n\t\t\tborder-radius: 100px;\r\n\t\t}\r\n\r\n\t\t&--square {\r\n\t\t\tborder-radius: 4px;\r\n\t\t}\r\n\r\n\t\t&__image {\r\n\t\t\t&--circle {\r\n\t\t\t\tborder-radius: 100px;\r\n                overflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t&--square {\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/uni_modules/uview-plus/components/u-avatar/u-avatar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle", "addUnit", "random"], "mappings": ";;;;;;AA4DC,MAAM,eACL;AAyBD,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,oDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,QAAQ;AAAA,QAAC;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAChG;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QACxF;AAAA,QACA;AAAA,MACA;AAAA,MACD,WAAW,KAAK;AAAA,MAChB,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AAAA;AAAA;AAAA,IAGN,KAAK;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ,QAAQ;AACf,aAAK,YAAY;AAEjB,YAAG,CAAC,QAAQ;AACX,eAAK,aAAa;AAAA,QACnB;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,YAAM,QAAQ,CAAC;AACf,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAC,0CAAO;AAAA,IACP,QAAAC,0CAAM;AAAA,IACN,OAAO;AAKN,WAAK,UAAU;AAAA,IAEf;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,IAAI,QAAQ,GAAG,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,eAAe;AACd,WAAK,YAAY,KAAK,cAAc;AAAA,IACpC;AAAA,IACD,aAAa,GAAG;AACf,WAAK,MAAM,SAAS,KAAK,MAAM,CAAC;AAAA,IACjC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJD,GAAG,gBAAgB,SAAS;"}