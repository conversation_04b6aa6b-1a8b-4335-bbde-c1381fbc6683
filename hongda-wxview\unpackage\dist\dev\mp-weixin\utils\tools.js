"use strict";
function formatEventStatus(status) {
  const statusMap = {
    0: "未开始",
    1: "报名中",
    2: "已结束",
    3: "已取消",
    4: "进行中"
  };
  return statusMap[status] || "未知";
}
function getStatusClass(status) {
  return status === 2 || status === 3 ? "ended" : "registering";
}
function calculateRemainingSpots(maxParticipants, registeredCount) {
  if (!maxParticipants || maxParticipants <= 0) {
    return "不限";
  }
  const remaining = maxParticipants - (registeredCount || 0);
  return remaining > 0 ? remaining : 0;
}
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
exports.calculateRemainingSpots = calculateRemainingSpots;
exports.debounce = debounce;
exports.formatEventStatus = formatEventStatus;
exports.getStatusClass = getStatusClass;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/tools.js.map
