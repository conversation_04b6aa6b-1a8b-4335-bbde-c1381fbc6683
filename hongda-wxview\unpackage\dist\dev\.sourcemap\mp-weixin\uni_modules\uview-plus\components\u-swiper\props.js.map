{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-swiper/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 列表数组，元素可为字符串，如为对象可通过keyName指定目标属性名\r\n        list: {\r\n            type: Array,\r\n            default: () => defProps.swiper.list\r\n        },\r\n        // 是否显示面板指示器\r\n        indicator: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.indicator\r\n        },\r\n        // 指示器非激活颜色\r\n        indicatorActiveColor: {\r\n            type: String,\r\n            default: () => defProps.swiper.indicatorActiveColor\r\n        },\r\n        // 指示器的激活颜色\r\n        indicatorInactiveColor: {\r\n            type: String,\r\n            default: () => defProps.swiper.indicatorInactiveColor\r\n        },\r\n        // 指示器样式，可通过bottom，left，right进行定位\r\n        indicatorStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.swiper.indicatorStyle\r\n        },\r\n        // 指示器模式，line-线型，dot-点型\r\n        indicatorMode: {\r\n            type: String,\r\n            default: () => defProps.swiper.indicatorMode\r\n        },\r\n        // 是否自动切换\r\n        autoplay: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.autoplay\r\n        },\r\n        // 当前所在滑块的 index\r\n        current: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.current\r\n        },\r\n        // 当前所在滑块的 item-id ，不能与 current 被同时指定\r\n        currentItemId: {\r\n            type: String,\r\n            default: () => defProps.swiper.currentItemId\r\n        },\r\n        // 滑块自动切换时间间隔\r\n        interval: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.interval\r\n        },\r\n        // 滑块切换过程所需时间\r\n        duration: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.duration\r\n        },\r\n        // 播放到末尾后是否重新回到开头\r\n        circular: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.circular\r\n        },\r\n        // 前边距，可用于露出前一项的一小部分，nvue和支付宝不支持\r\n        previousMargin: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.previousMargin\r\n        },\r\n        // 后边距，可用于露出后一项的一小部分，nvue和支付宝不支持\r\n        nextMargin: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.nextMargin\r\n        },\r\n        // 当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持\r\n        acceleration: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.acceleration\r\n        },\r\n        // 同时显示的滑块数量，nvue、支付宝小程序不支持\r\n        displayMultipleItems: {\r\n            type: Number,\r\n            default: () => defProps.swiper.displayMultipleItems\r\n        },\r\n        // 指定swiper切换缓动动画类型，有效值：default、linear、easeInCubic、easeOutCubic、easeInOutCubic\r\n        // 只对微信小程序有效\r\n        easingFunction: {\r\n            type: String,\r\n            default: () => defProps.swiper.easingFunction\r\n        },\r\n        // list数组中指定对象的目标属性名\r\n        keyName: {\r\n            type: String,\r\n            default: () => defProps.swiper.keyName\r\n        },\r\n        // 图片的裁剪模式\r\n        imgMode: {\r\n            type: String,\r\n            default: () => defProps.swiper.imgMode\r\n        },\r\n        // 组件高度\r\n        height: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.height\r\n        },\r\n        // 背景颜色\r\n        bgColor: {\r\n            type: String,\r\n            default: () => defProps.swiper.bgColor\r\n        },\r\n        // 组件圆角，数值或带单位的字符串\r\n        radius: {\r\n            type: [String, Number],\r\n            default: () => defProps.swiper.radius\r\n        },\r\n        // 是否加载中\r\n        loading: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.loading\r\n        },\r\n        // 是否显示标题，要求数组对象中有title属性\r\n        showTitle: {\r\n            type: Boolean,\r\n            default: () => defProps.swiper.showTitle\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}